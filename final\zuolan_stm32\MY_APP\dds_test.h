/*******************************************************************************
 * @file      dds_test.h
 * <AUTHOR>
 * @version   V1.0
 * @date      2025-07-31
 * @brief     软件DDS测试模块头文件
 * @note      实现串口控制的频率步进测试功能
 *******************************************************************************/

#ifndef __DDS_TEST_H__
#define __DDS_TEST_H__

#include "stm32f4xx_hal.h"
#include "software_dds.h"
#include "my_usart.h"

// 频率模式定义
typedef enum {
    FREQ_MODE_LOW = 0,      // 低频模式: 100Hz-3000Hz, 步进100Hz
    FREQ_MODE_HIGH          // 高频模式: 1kHz-50kHz, 步进200Hz
} FreqMode_t;

// 频率控制结构体
typedef struct {
    FreqMode_t mode;        // 当前频率模式
    uint32_t frequency;     // 当前频率
    uint8_t amplitude;      // 当前幅度 (0-100%)
    uint8_t test_active;    // 测试激活标志
} DDS_TestConfig_t;

// 函数声明

/**
 * @brief  初始化DDS测试模块
 * @param  None
 * @retval None
 */
void DDS_Test_Init(void);

/**
 * @brief  处理串口DDS测试命令
 * @param  command: 接收到的命令字符串
 * @retval None
 */
void DDS_Test_ProcessCommand(char* command);

/**
 * @brief  设置频率模式
 * @param  mode: 频率模式 (FREQ_MODE_LOW/FREQ_MODE_HIGH)
 * @retval None
 */
void DDS_Test_SetMode(FreqMode_t mode);

/**
 * @brief  频率增加一个步进
 * @param  None
 * @retval None
 */
void DDS_Test_FreqPlus(void);

/**
 * @brief  频率减少一个步进
 * @param  None
 * @retval None
 */
void DDS_Test_FreqMinus(void);

/**
 * @brief  设置输出幅度
 * @param  amplitude: 幅度百分比 (0-100)
 * @retval None
 */
void DDS_Test_SetAmplitude(uint8_t amplitude);

/**
 * @brief  显示当前DDS状态
 * @param  None
 * @retval None
 */
void DDS_Test_ShowStatus(void);

/**
 * @brief  显示帮助信息
 * @param  None
 * @retval None
 */
void DDS_Test_ShowHelp(void);

/**
 * @brief  启动DDS测试
 * @param  None
 * @retval None
 */
void DDS_Test_Start(void);

/**
 * @brief  停止DDS测试
 * @param  None
 * @retval None
 */
void DDS_Test_Stop(void);

/**
 * @brief  显示详细调试信息
 * @param  None
 * @retval None
 */
void DDS_Test_ShowDebugInfo(void);

/**
 * @brief  测试DAC直接输出
 * @param  None
 * @retval None
 */
void DDS_Test_TestDAC(void);

/**
 * @brief  显示实时DDS状态
 * @param  None
 * @retval None
 */
void DDS_Test_ShowRealTimeStatus(void);

#endif // __DDS_TEST_H__

/*******************************************************************************
 * @file      software_dds.c
 * <AUTHOR>
 * @version   V1.0
 * @date      2025-07-31
 * @brief     软件DDS实现 - 基于STM32 DAC的直接数字合成
 * @note      替代AD9833，实现完全可控的频率、幅度、相位输出
 *******************************************************************************/

#include "software_dds.h"
#include "my_usart.h"
#include "dac.h"
#include "tim.h"
#include <math.h>

// 外部变量声明
extern DAC_HandleTypeDef hdac;
extern TIM_HandleTypeDef htim6;

// DDS全局变量
static DDS_Config_t dds_config = {1000, 2048, 0, DDS_WAVEFORM_SINE, 0};
static uint32_t phase_accumulator = 0;
static uint32_t phase_increment = 0;

// 正弦波查找表生成函数 (运行时生成，节省Flash空间)
static uint16_t sine_table[DDS_TABLE_SIZE];
static uint8_t table_initialized = 0;

// 初始化正弦波查找表
static void init_sine_table(void)
{
    if (table_initialized) return;

    for (int i = 0; i < DDS_TABLE_SIZE; i++) {
        // 生成12位精度的正弦波 (0-4095)
        float angle = (2.0f * 3.14159265f * i) / DDS_TABLE_SIZE;
        sine_table[i] = (uint16_t)(2048 + 2047 * sinf(angle));
    }

    table_initialized = 1;
}

/**
 * @brief  初始化软件DDS
 * @param  None
 * @retval None
 */
void DDS_Init(void)
{
    // 初始化正弦波查找表
    init_sine_table();

    // 初始化DAC
    HAL_DAC_Start(&hdac, DAC_CHANNEL_1);

    // 设置初始输出为中点电压
    HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, 2048);

    // 计算初始相位增量
    DDS_CalculatePhaseIncrement(dds_config.frequency);

    my_printf(&huart2, "Software DDS initialized with %d-point sine table\r\n", DDS_TABLE_SIZE);
}

/**
 * @brief  启动DDS输出
 * @param  None
 * @retval None
 */
void DDS_Start(void)
{
    dds_config.enabled = 1;

    // 重置相位累加器
    phase_accumulator = 0;

    // 启动定时器中断 (100kHz采样率)
    HAL_StatusTypeDef status = HAL_TIM_Base_Start_IT(&htim6);

    my_printf(&huart2, "DDS started: %lu Hz, %d%% amplitude, TIM6 status: %d\r\n",
              dds_config.frequency, (dds_config.amplitude * 100) / 4095, status);
    my_printf(&huart2, "DDS config: enabled=%d, amplitude=%d, phase_increment=0x%08lX\r\n",
              dds_config.enabled, dds_config.amplitude, phase_increment);
}

/**
 * @brief  停止DDS输出
 * @param  None
 * @retval None
 */
void DDS_Stop(void)
{
    dds_config.enabled = 0;
    
    // 停止定时器中断
    HAL_TIM_Base_Stop_IT(&htim6);
    
    // 输出中点电压
    HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, 2048);
    
    my_printf(&huart2, "DDS stopped\r\n");
}

/**
 * @brief  计算相位增量
 * @param  frequency: 目标频率
 * @retval None
 */
void DDS_CalculatePhaseIncrement(uint32_t frequency)
{
    // 相位增量 = (频率 × 2^32) / 采样率
    phase_increment = ((uint64_t)frequency << DDS_PHASE_BITS) / DDS_SAMPLE_RATE;
}

/**
 * @brief  设置DDS频率
 * @param  frequency: 目标频率 (Hz)
 * @retval None
 */
void DDS_SetFrequency(uint32_t frequency)
{
    if (frequency > DDS_SAMPLE_RATE / 2) {
        frequency = DDS_SAMPLE_RATE / 2;  // 限制最大频率
        my_printf(&huart2, "DDS: Frequency limited to %lu Hz (max: %d Hz)\r\n",
                  frequency, DDS_SAMPLE_RATE / 2);
    }

    dds_config.frequency = frequency;
    DDS_CalculatePhaseIncrement(frequency);

    my_printf(&huart2, "DDS: Frequency set to %lu Hz, phase_increment = 0x%08lX\r\n",
              frequency, phase_increment);
}

/**
 * @brief  设置DDS幅度
 * @param  amplitude_percent: 幅度百分比 (0-100)
 * @retval None
 */
void DDS_SetAmplitude(uint8_t amplitude_percent)
{
    if (amplitude_percent > 100) {
        amplitude_percent = 100;
    }

    dds_config.amplitude = (amplitude_percent * DDS_MAX_AMPLITUDE) / 100;

    my_printf(&huart2, "DDS: Amplitude set to %d%% (DAC value: %d/4095)\r\n",
              amplitude_percent, dds_config.amplitude);
}

/**
 * @brief  设置DDS相位
 * @param  phase_deg: 相位角度 (0-359度)
 * @retval None
 */
void DDS_SetPhase(uint16_t phase_deg)
{
    if (phase_deg >= 360) {
        phase_deg = phase_deg % 360;
    }
    
    dds_config.phase_offset = phase_deg;
}

/**
 * @brief  设置DDS波形类型
 * @param  waveform: 波形类型
 * @retval None
 */
void DDS_SetWaveform(DDS_Waveform_t waveform)
{
    dds_config.waveform = waveform;
}

/**
 * @brief  DDS综合设置函数
 * @param  frequency: 频率 (Hz)
 * @param  amplitude_percent: 幅度百分比 (0-100)
 * @param  phase_deg: 相位角度 (0-359度)
 * @param  waveform: 波形类型
 * @retval None
 */
void DDS_Setup(uint32_t frequency, uint8_t amplitude_percent, uint16_t phase_deg, DDS_Waveform_t waveform)
{
    DDS_SetFrequency(frequency);
    DDS_SetAmplitude(amplitude_percent);
    DDS_SetPhase(phase_deg);
    DDS_SetWaveform(waveform);
    
    my_printf(&huart2, "DDS setup: %lu Hz, %d%%, %d°, waveform %d\r\n",
              frequency, amplitude_percent, phase_deg, waveform);
}

/**
 * @brief  获取当前DDS配置
 * @param  None
 * @retval DDS_Config_t: 当前配置
 */
DDS_Config_t DDS_GetConfig(void)
{
    return dds_config;
}

// 调试计数器（静态变量）
static uint32_t dds_debug_counter = 0;
static uint32_t dds_sample_counter = 0;

/**
 * @brief  DDS输出一个采样点 (在定时器中断中调用)
 * @param  None
 * @retval None
 */
void DDS_OutputSample(void)
{
    if (!dds_config.enabled) {
        return;
    }

    dds_sample_counter++;

    uint16_t table_index;
    uint16_t dac_value;

    // 更新相位累加器
    phase_accumulator += phase_increment;

    // 获取查找表索引 (高10位)
    table_index = (phase_accumulator >> (DDS_PHASE_BITS - 10)) & 0x3FF;

    // 应用相位偏移
    if (dds_config.phase_offset > 0) {
        uint16_t phase_offset_index = (dds_config.phase_offset * DDS_TABLE_SIZE) / 360;
        table_index = (table_index + phase_offset_index) & 0x3FF;
    }
    
    // 根据波形类型获取波形值
    switch (dds_config.waveform) {
        case DDS_WAVEFORM_SINE:
            dac_value = sine_table[table_index];
            break;
            
        case DDS_WAVEFORM_TRIANGLE:
            // 三角波生成
            if (table_index < 512) {
                dac_value = 2048 + (table_index * 4);
            } else {
                dac_value = 2048 + ((1023 - table_index) * 4);
            }
            break;
            
        case DDS_WAVEFORM_SQUARE:
            // 方波生成
            dac_value = (table_index < 512) ? 4095 : 0;
            break;
            
        case DDS_WAVEFORM_SAWTOOTH:
            // 锯齿波生成
            dac_value = table_index * 4;
            break;
            
        default:
            dac_value = sine_table[table_index];
            break;
    }
    
    // 应用幅度控制
    dac_value = 2048 + (((int32_t)dac_value - 2048) * dds_config.amplitude) / DDS_MAX_AMPLITUDE;

    // 输出到DAC
    HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_value);

    // 调试信息：每10000次采样输出一次状态
    dds_debug_counter++;
    if(dds_debug_counter >= 10000) {
        dds_debug_counter = 0;
        // 注意：在中断中使用printf可能导致问题，这里仅用于调试
        // 实际使用时应该设置标志位，在主循环中输出
        // my_printf(&huart2, "DDS: samples=%lu, table_idx=%d, dac_val=%d\r\n",
        //           dds_sample_counter, table_index, dac_value);
    }
}

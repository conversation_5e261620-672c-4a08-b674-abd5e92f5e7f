# DDS测试验证步骤

## 🔧 修复内容确认

### 已修复的问题：
1. ✅ **TIM6定时器配置** - 从10kHz修复为100kHz
2. ✅ **定时器中断处理** - 解决与系统时钟的冲突
3. ✅ **调试命令添加** - 在UART1和UART2中都添加了`debug`命令支持
4. ✅ **调试信息增强** - 添加了详细的状态显示

## 🧪 立即测试步骤

### 1. 编译并烧录
确保所有修改都已编译并烧录到STM32

### 2. 基本命令测试
```bash
# 测试帮助命令（应该显示debug命令）
dds help

# 测试新的调试命令
debug

# 查看当前状态
status
```

### 3. 验证TIM6配置
使用`debug`命令查看：
- TIM6 Prescaler应该是17
- TIM6 Period应该是99  
- Expected freq应该是100000 Hz

### 4. 测试DDS功能
```bash
# 启动DDS
start

# 查看启动后的状态
status
debug

# 测试频率控制
plus
status
plus  
status
minus
status

# 测试幅度控制
amp 25
status
amp 75
status
amp 100
status
```

## 🎯 预期结果

### debug命令输出应该包含：
```
=== DDS Debug Info ===
Test Config:
  mode: 0, freq: 1000 Hz, amp: 80%, active: 1
DDS Config:
  freq: 1000 Hz, amp: 2047, phase: 0, waveform: 0, enabled: 1
TIM6 Config:
  Prescaler: 17, Period: 99
  Expected freq: 100000 Hz
======================
```

### 关键验证点：
- [ ] `dds help`显示包含`debug`命令
- [ ] `debug`命令能正常执行并显示详细信息
- [ ] TIM6预期频率显示为100000 Hz（而不是10000 Hz）
- [ ] `start`命令后DDS enabled应该为1
- [ ] `plus`/`minus`命令能正确改变频率
- [ ] `amp XX`命令能正确设置幅度

## 🔍 问题排查

### 如果debug命令不工作：
1. 检查编译是否成功
2. 确认代码已烧录
3. 验证串口连接和波特率

### 如果TIM6频率仍然是10000 Hz：
1. 检查tim.c的修改是否生效
2. 确认CubeMX配置没有覆盖手动修改
3. 重新生成代码并重新应用修改

### 如果频率控制仍然无效：
1. 查看DDS Config中的freq值是否变化
2. 检查phase_increment计算是否正确
3. 确认DDS_OutputSample是否被调用

## 📊 测试记录模板

```
测试时间：_______
固件版本：_______

基本功能测试：
[ ] dds help显示debug命令
[ ] debug命令执行成功
[ ] status命令正常

TIM6配置验证：
[ ] Prescaler: 17
[ ] Period: 99
[ ] Expected freq: 100000 Hz

DDS功能测试：
[ ] start命令启动成功
[ ] plus命令增加频率
[ ] minus命令减少频率  
[ ] amp命令设置幅度

实际测量：
输出频率：_______ Hz
输出幅度：_______ V
波形质量：_______

问题记录：
_________________
_________________
```

## 🚀 下一步

根据测试结果：
1. 如果一切正常 → 进行更详细的性能测试
2. 如果有问题 → 根据debug信息进一步诊断
3. 如果频率仍不对 → 检查相位增量计算和DAC输出

请先进行这些基本测试，然后告诉我结果，我们可以根据实际情况进一步调试。

Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to software_dds.o(i.DDS_OutputSample) for DDS_OutputSample
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to main.o(.data) for .data
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS) for HAL_RCC_EnableCSS
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to fmc.o(i.MX_FMC_Init) for MX_FMC_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to dac.o(i.MX_DAC_Init) for MX_DAC_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to cmd_to_fun.o(i.CTRL_INIT) for CTRL_INIT
    main.o(i.main) refers to adc_app.o(i.adc_tim_dma_init) for adc_tim_dma_init
    main.o(i.main) refers to da_output.o(i.DA_Init) for DA_Init
    main.o(i.main) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    main.o(i.main) refers to dac_app.o(i.dac_app_init) for dac_app_init
    main.o(i.main) refers to dac_app.o(i.dac_app_set_waveform) for dac_app_set_waveform
    main.o(i.main) refers to app_pid.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to my_fft.o(i.fft_init) for fft_init
    main.o(i.main) refers to software_dds.o(i.DDS_Init) for DDS_Init
    main.o(i.main) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    main.o(i.main) refers to dds_test.o(i.DDS_Test_Init) for DDS_Test_Init
    main.o(i.main) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to my_usart.o(i.process_uart1_command) for process_uart1_command
    main.o(i.main) refers to my_usart.o(.data) for rxTemp2
    main.o(i.main) refers to usart.o(.bss) for huart2
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    dac.o(i.HAL_DAC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.HAL_DAC_MspInit) refers to dac.o(.bss) for .bss
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC_Init) refers to dac.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    fmc.o(i.HAL_FMC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.HAL_FMC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fmc.o(i.HAL_FMC_MspInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fmc.o(i.HAL_SRAM_MspDeInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspInit) refers to fmc.o(i.HAL_FMC_MspInit) for HAL_FMC_MspInit
    fmc.o(i.MX_FMC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.MX_FMC_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    fmc.o(i.MX_FMC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    fmc.o(i.MX_FMC_Init) refers to fmc.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to my_usart.o(.bss) for uart1_dma_buffer
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to dac.o(.bss) for hdma_dac1
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.NMI_Handler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) for HAL_RCC_NMI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    ad_measure.o(i.ad_proc) refers to ad_measure.o(i.vpp_adc_parallel) for vpp_adc_parallel
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(i.readFIFOData) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(i.setSamplingFrequency) refers to cmd_to_fun.o(i.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(i.setSamplingFrequency) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.setSamplingFrequency) for setSamplingFrequency
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE) for AD_FIFO_WRITE_ENABLE
    ad_measure.o(i.vpp_adc_parallel) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE) for AD_FIFO_WRITE_DISABLE
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.readFIFOData) for readFIFOData
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.findMinMax) for findMinMax
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.data) for .data
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.bss) for .bss
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(i.DA_Apply_Settings) refers to ffixul.o(.text) for __aeabi_f2ulz
    da_output.o(i.DA_Apply_Settings) refers to uldiv.o(.text) for __aeabi_uldivmod
    da_output.o(i.DA_Apply_Settings) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_START) for DA_FPGA_START
    da_output.o(i.DA_Apply_Settings) refers to da_output.o(.bss) for .bss
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.DA_SetConfig) refers to da_output.o(.bss) for .bss
    da_output.o(i.wave_test) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.wave_test) refers to stm32f4xx_hal.o(.data) for uwTick
    da_output.o(i.wave_test) refers to da_output.o(.data) for .data
    da_output.o(i.wave_test) refers to da_output.o(.bss) for .bss
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE) for AD_FREQ_CLR_ENABLE
    freq_measure.o(i.fre_measure) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE) for AD_FREQ_CLR_DISABLE
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_START) for AD_FREQ_START
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_STOP) for AD_FREQ_STOP
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad1) for fre_measure_ad1
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad2) for fre_measure_ad2
    freq_measure.o(i.freq_proc) refers to f2d.o(.text) for __aeabi_f2d
    freq_measure.o(i.freq_proc) refers to my_usart.o(i.my_printf) for my_printf
    freq_measure.o(i.freq_proc) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.get_current_ad_frequency) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_proc) refers to my_usart.o(i.my_printf) for my_printf
    key_app.o(i.key_proc) refers to my_fft.o(i.calculate_fft_spectrum) for calculate_fft_spectrum
    key_app.o(i.key_proc) refers to my_fft.o(i.output_fft_spectrum) for output_fft_spectrum
    key_app.o(i.key_proc) refers to dac_app.o(i.dac_app_set_waveform) for dac_app_set_waveform
    key_app.o(i.key_proc) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    key_app.o(i.key_proc) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.key_proc) refers to ad_measure.o(.bss) for fifo_data1_f
    key_app.o(i.key_proc) refers to da_output.o(.bss) for da_channels
    key_app.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.set_current_ad_frequency) refers to key_app.o(.data) for .data
    ad9833.o(i.AD9833_Init) refers to memseta.o(.text) for __aeabi_memclr4
    ad9833.o(i.AD9833_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9833.o(i.AD9833_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_Init2) refers to memseta.o(.text) for __aeabi_memclr4
    ad9833.o(i.AD9833_Init2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9833.o(i.AD9833_Init2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_SetFrequency) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_SetFrequency) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_SetFrequency) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_SetFrequency) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_SetFrequency2) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_SetFrequency2) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_SetFrequency2) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_SetFrequency2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_SetPhase) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_SetPhase2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_SetWave) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_SetWave2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_SetFrequency) for AD9833_SetFrequency
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_SetPhase) for AD9833_SetPhase
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_SetWave) for AD9833_SetWave
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_SetFrequency2) for AD9833_SetFrequency2
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_SetPhase2) for AD9833_SetPhase2
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_SetWave2) for AD9833_SetWave2
    ad9833.o(i.AD9833_WriteData) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_WriteData) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    ad9833.o(i.AD9833_WriteData2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_WriteData2) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for hadc1
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to my_usart.o(i.my_printf) for my_printf
    adc_app.o(i.adc_task) refers to memseta.o(.text) for __aeabi_memclr4
    adc_app.o(i.adc_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_task) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_task) refers to usart.o(.bss) for huart1
    adc_app.o(i.adc_task) refers to adc.o(.bss) for hadc1
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_tim_dma_init) refers to tim.o(.bss) for htim3
    adc_app.o(i.adc_tim_dma_init) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_tim_dma_init) refers to adc.o(.bss) for hadc1
    dac_app.o(i.dac_app_get_actual_frequency) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(i.dac_app_get_actual_frequency) refers to tim.o(.bss) for htim6
    dac_app.o(i.dac_app_get_amplitude) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_get_zero_based) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.dac_app_set_amplitude) for dac_app_set_amplitude
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_init) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_sine) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    dac_app.o(i.generate_sine) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_sine) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_square) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_square) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_triangle) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_triangle) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_sine) for generate_sine
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_square) for generate_square
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_triangle) for generate_triangle
    dac_app.o(i.generate_waveform) refers to dac_app.o(.data) for .data
    dac_app.o(i.start_dac_dma) refers to dac_app.o(i.update_timer_frequency) for update_timer_frequency
    dac_app.o(i.start_dac_dma) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) for HAL_DAC_Start_DMA
    dac_app.o(i.start_dac_dma) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    dac_app.o(i.start_dac_dma) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.start_dac_dma) refers to dac.o(.bss) for hdac
    dac_app.o(i.start_dac_dma) refers to tim.o(.bss) for htim6
    dac_app.o(i.stop_dac_dma) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(i.stop_dac_dma) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(i.stop_dac_dma) refers to dac.o(.bss) for hdac
    dac_app.o(i.stop_dac_dma) refers to tim.o(.bss) for htim6
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(i.update_timer_frequency) refers to uldiv.o(.text) for __aeabi_uldivmod
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent) for HAL_TIM_GenerateEvent
    dac_app.o(i.update_timer_frequency) refers to dac_app.o(.data) for .data
    dac_app.o(i.update_timer_frequency) refers to tim.o(.bss) for htim6
    software_dds.o(i.DDS_CalculatePhaseIncrement) refers to uldiv.o(.text) for __aeabi_uldivmod
    software_dds.o(i.DDS_CalculatePhaseIncrement) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_GetConfig) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Init) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    software_dds.o(i.DDS_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    software_dds.o(i.DDS_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(i.DDS_Init) refers to software_dds.o(i.DDS_CalculatePhaseIncrement) for DDS_CalculatePhaseIncrement
    software_dds.o(i.DDS_Init) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Init) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Init) refers to software_dds.o(.bss) for .bss
    software_dds.o(i.DDS_Init) refers to dac.o(.bss) for hdac
    software_dds.o(i.DDS_Init) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_OutputSample) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(i.DDS_OutputSample) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_OutputSample) refers to software_dds.o(.bss) for .bss
    software_dds.o(i.DDS_OutputSample) refers to dac.o(.bss) for hdac
    software_dds.o(i.DDS_SetAmplitude) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_SetAmplitude) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_SetAmplitude) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_SetFrequency) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_SetFrequency) refers to software_dds.o(i.DDS_CalculatePhaseIncrement) for DDS_CalculatePhaseIncrement
    software_dds.o(i.DDS_SetFrequency) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_SetFrequency) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_SetPhase) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_SetWaveform) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Setup) refers to software_dds.o(i.DDS_SetFrequency) for DDS_SetFrequency
    software_dds.o(i.DDS_Setup) refers to software_dds.o(i.DDS_SetAmplitude) for DDS_SetAmplitude
    software_dds.o(i.DDS_Setup) refers to software_dds.o(i.DDS_SetPhase) for DDS_SetPhase
    software_dds.o(i.DDS_Setup) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Setup) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Setup) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_Start) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    software_dds.o(i.DDS_Start) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Start) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Start) refers to tim.o(.bss) for htim6
    software_dds.o(i.DDS_Start) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_Stop) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT) for HAL_TIM_Base_Stop_IT
    software_dds.o(i.DDS_Stop) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(i.DDS_Stop) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Stop) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Stop) refers to tim.o(.bss) for htim6
    software_dds.o(i.DDS_Stop) refers to dac.o(.bss) for hdac
    software_dds.o(i.DDS_Stop) refers to usart.o(.bss) for huart2
    my_fft.o(i.calculate_fft_spectrum) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    my_fft.o(i.calculate_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_sinad) refers to my_fft.o(i.calculate_thd_n) for calculate_thd_n
    my_fft.o(i.calculate_sinad) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    my_fft.o(i.calculate_thd) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_thd_n) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd_n) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.fft_init) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    my_fft.o(i.fft_init) refers to my_fft.o(i.generate_hanning_window) for generate_hanning_window
    my_fft.o(i.fft_init) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.generate_hanning_window) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(i.generate_hanning_window) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.get_precise_peak_frequency) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.output_fft_spectrum) refers to key_app.o(i.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.output_fft_spectrum) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.get_precise_peak_frequency) for get_precise_peak_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.round_to_nearest_k) for round_to_nearest_k
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd) for calculate_thd
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd_n) for calculate_thd_n
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_sinad) for calculate_sinad
    my_fft.o(i.output_fft_spectrum) refers to usart.o(.bss) for huart1
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.round_to_nearest_k) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_filter.o(i.arm_fir_f32_lp) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for arm_fir_init_f32
    my_filter.o(i.arm_fir_f32_lp) refers to my_filter.o(.constdata) for .constdata
    phase_measure.o(i.calculate_phase_diff) refers to phase_measure.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman) refers to kalman.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(.bss) for .bss
    kalman.o(i.kalman_thd) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman_thd) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman_thd) refers to kalman.o(.data) for .data
    kalman.o(i.kalman_thd) refers to kalman.o(.bss) for .bss
    wave_recognition.o(i.calculate_carrier_suppression) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(i.calculate_carrier_suppression) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.calculate_carrier_suppression) refers to dfixi.o(.text) for __aeabi_d2iz
    wave_recognition.o(i.detect_symmetry) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(i.detect_symmetry) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.detect_symmetry) refers to dfixi.o(.text) for __aeabi_d2iz
    wave_recognition.o(i.find_peaks) refers to f2d.o(.text) for __aeabi_f2d
    wave_recognition.o(i.find_peaks) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.find_peaks) refers to d2f.o(.text) for __aeabi_d2f
    wave_recognition.o(i.preprocess_signal) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    wave_recognition.o(i.recognize_waveform) refers to strcpy.o(.text) for strcpy
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.preprocess_signal) for preprocess_signal
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.find_peaks) for find_peaks
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.calculate_bandwidth) for calculate_bandwidth
    wave_recognition.o(i.recognize_waveform) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(i.recognize_waveform) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.recognize_waveform) refers to f2d.o(.text) for __aeabi_f2d
    wave_recognition.o(i.recognize_waveform) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    wave_recognition.o(i.recognize_waveform) refers to printfa.o(i.__0sprintf) for __2sprintf
    wave_recognition.o(i.recognize_waveform) refers to memcpya.o(.text) for __aeabi_memcpy4
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.calculate_carrier_suppression) for calculate_carrier_suppression
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.detect_symmetry) for detect_symmetry
    wave_recognition.o(i.recognize_waveform) refers to cdcmple.o(.text) for __aeabi_cdcmple
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(.conststring) for .conststring
    my_hmi.o(i.HMI_Send_Float) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Float) refers to dflti.o(.text) for __aeabi_i2d
    my_hmi.o(i.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(i.HMI_Send_Float) refers to f2d.o(.text) for __aeabi_f2d
    my_hmi.o(i.HMI_Send_Float) refers to dmul.o(.text) for __aeabi_dmul
    my_hmi.o(i.HMI_Send_Float) refers to dfixi.o(.text) for __aeabi_d2iz
    my_hmi.o(i.HMI_Send_Float) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_Int) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Int) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_String) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_String) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Wave_Clear) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Wave_Clear) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hmi.o(i.HMI_Write_Wave_Low) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Low) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart3
    my_usart.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.process_uart1_command) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.process_uart1_command) refers to strncmp.o(.text) for strncmp
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_FreqPlus) for DDS_Test_FreqPlus
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_FreqMinus) for DDS_Test_FreqMinus
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_Start) for DDS_Test_Start
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_Stop) for DDS_Test_Stop
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_SetMode) for DDS_Test_SetMode
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_ShowDebugInfo) for DDS_Test_ShowDebugInfo
    my_usart.o(i.process_uart1_command) refers to my_usart.o(.data) for .data
    my_usart.o(i.process_uart1_command) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.process_uart1_command) refers to usart.o(.bss) for huart1
    my_usart.o(i.process_uart1_command) refers to atoi.o(.text) for atoi
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_SetAmplitude) for DDS_Test_SetAmplitude
    my_usart.o(i.process_uart1_command) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.process_uart2_command) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.process_uart2_command) refers to strstr.o(.text) for strstr
    my_usart.o(i.process_uart2_command) refers to scheduler.o(i.start_circuit_learning) for start_circuit_learning
    my_usart.o(i.process_uart2_command) refers to dds_test.o(i.DDS_Test_ProcessCommand) for DDS_Test_ProcessCommand
    my_usart.o(i.process_uart2_command) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.process_uart2_command) refers to my_usart.o(.data) for .data
    my_usart.o(i.process_uart2_command) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.process_uart2_command) refers to usart.o(.bss) for huart2
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.ParseFrame) refers to my_usart_pack.o(i.ParseDataToVariables) for ParseDataToVariables
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.SendFrame) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.data) for .data
    scheduler.o(i.circuit_learning_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.circuit_learning_task) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    scheduler.o(i.circuit_learning_task) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.circuit_learning_task) refers to wave_recognition.o(i.recognize_waveform) for recognize_waveform
    scheduler.o(i.circuit_learning_task) refers to memcpya.o(.text) for __aeabi_memcpy4
    scheduler.o(i.circuit_learning_task) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(i.circuit_learning_task) refers to scheduler.o(.data) for .data
    scheduler.o(i.circuit_learning_task) refers to usart.o(.bss) for huart2
    scheduler.o(i.circuit_learning_task) refers to scheduler.o(.bss) for .bss
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(i.start_circuit_learning) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.start_circuit_learning) refers to software_dds.o(i.DDS_Start) for DDS_Start
    scheduler.o(i.start_circuit_learning) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.start_circuit_learning) refers to scheduler.o(.data) for .data
    scheduler.o(i.start_circuit_learning) refers to usart.o(.bss) for huart2
    scheduler.o(i.uart_proc) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(i.uart_proc) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.uart_proc) refers to ad_measure.o(.data) for vol_amp2
    scheduler.o(i.uart_proc) refers to app_pid.o(.data) for output
    scheduler.o(i.uart_proc) refers to usart.o(.bss) for huart1
    scheduler.o(.data) refers to key_app.o(i.key_proc) for key_proc
    scheduler.o(.data) refers to scheduler.o(i.circuit_learning_task) for circuit_learning_task
    scheduler.o(.data) refers to my_usart.o(i.process_uart2_command) for process_uart2_command
    app_pid.o(i.PID_Init) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.Pid_Proc) refers to app_pid.o(i.increment_pid_ctrl) for increment_pid_ctrl
    app_pid.o(i.Pid_Proc) refers to ad_measure.o(.data) for vol_amp2
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.data) for .data
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.bss) for .bss
    dds_test.o(i.DDS_Test_FreqMinus) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_FreqMinus) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_FreqMinus) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_FreqMinus) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_FreqMinus) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_FreqPlus) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_FreqPlus) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_FreqPlus) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_FreqPlus) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_FreqPlus) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_Init) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_Init) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_Init) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ProcessCommand) refers to strchr.o(.text) for strchr
    dds_test.o(i.DDS_Test_ProcessCommand) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ProcessCommand) refers to strcmp.o(.text) for strcmp
    dds_test.o(i.DDS_Test_ProcessCommand) refers to strncmp.o(.text) for strncmp
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_SetMode) for DDS_Test_SetMode
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_FreqPlus) for DDS_Test_FreqPlus
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_FreqMinus) for DDS_Test_FreqMinus
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_Start) for DDS_Test_Start
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_Stop) for DDS_Test_Stop
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_ShowHelp) for DDS_Test_ShowHelp
    dds_test.o(i.DDS_Test_ProcessCommand) refers to atoi.o(.text) for atoi
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_SetAmplitude) for DDS_Test_SetAmplitude
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_ShowDebugInfo) for DDS_Test_ShowDebugInfo
    dds_test.o(i.DDS_Test_ProcessCommand) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_SetAmplitude) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_SetAmplitude) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_SetAmplitude) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_SetAmplitude) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_SetAmplitude) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_SetMode) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_SetMode) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_SetMode) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_SetMode) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_SetMode) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to software_dds.o(i.DDS_GetConfig) for DDS_GetConfig
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to tim.o(.bss) for htim6
    dds_test.o(i.DDS_Test_ShowHelp) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ShowHelp) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ShowHelp) refers to dds_test.o(.conststring) for .conststring
    dds_test.o(i.DDS_Test_ShowStatus) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ShowStatus) refers to f2d.o(.text) for __aeabi_f2d
    dds_test.o(i.DDS_Test_ShowStatus) refers to software_dds.o(i.DDS_GetConfig) for DDS_GetConfig
    dds_test.o(i.DDS_Test_ShowStatus) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_ShowStatus) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_Start) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_Start) refers to software_dds.o(i.DDS_Start) for DDS_Start
    dds_test.o(i.DDS_Test_Start) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_Start) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_Start) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_Start) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_Stop) refers to software_dds.o(i.DDS_Stop) for DDS_Stop
    dds_test.o(i.DDS_Test_Stop) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_Stop) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_Stop) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_Stop) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) for FLASH_OB_DisablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) for FLASH_OB_BootConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) for FLASH_OB_EnablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to fmc.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to fmc.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_fir_init_f32.o(.text.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    log10f.o(i.__hardfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f.o(i.__hardfp_log10f) refers to errno.o(i.__set_errno) for __set_errno
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    log10f.o(i.__hardfp_log10f) refers to log10f.o(.constdata) for .constdata
    log10f.o(i.__softfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__softfp_log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(i.log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f_x.o(i.____hardfp_log10f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log10f_x.o(i.____hardfp_log10f$lsc) refers to log10f_x.o(.constdata) for .constdata
    log10f_x.o(i.____softfp_log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____softfp_log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(i.__log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.__log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to errno.o(i.__set_errno) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.HAL_TIM_PeriodElapsedCallback), (52 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (2 bytes).
    Removing main.o(.data), (4 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (60 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.HAL_DAC_MspDeInit), (56 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing fmc.o(.rev16_text), (4 bytes).
    Removing fmc.o(.revsh_text), (4 bytes).
    Removing fmc.o(.rrx_text), (6 bytes).
    Removing fmc.o(i.HAL_SRAM_MspDeInit), (92 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (140 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(.rev16_text), (4 bytes).
    Removing ad_measure.o(.revsh_text), (4 bytes).
    Removing ad_measure.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(i.ad_proc), (24 bytes).
    Removing ad_measure.o(i.findMinMax), (48 bytes).
    Removing ad_measure.o(i.readFIFOData), (124 bytes).
    Removing ad_measure.o(i.setSamplingFrequency), (160 bytes).
    Removing ad_measure.o(i.vpp_adc_parallel), (292 bytes).
    Removing ad_measure.o(.data), (16 bytes).
    Removing da_output.o(.rev16_text), (4 bytes).
    Removing da_output.o(.revsh_text), (4 bytes).
    Removing da_output.o(.rrx_text), (6 bytes).
    Removing da_output.o(i.wave_test), (76 bytes).
    Removing da_output.o(.data), (8 bytes).
    Removing freq_measure.o(.rev16_text), (4 bytes).
    Removing freq_measure.o(.revsh_text), (4 bytes).
    Removing freq_measure.o(.rrx_text), (6 bytes).
    Removing freq_measure.o(i.fre_measure), (192 bytes).
    Removing freq_measure.o(i.fre_measure_ad1), (20 bytes).
    Removing freq_measure.o(i.fre_measure_ad2), (20 bytes).
    Removing freq_measure.o(i.freq_proc), (60 bytes).
    Removing freq_measure.o(.data), (24 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(i.set_current_ad_frequency), (12 bytes).
    Removing key_app.o(.data), (4 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing ad9833.o(.rrx_text), (6 bytes).
    Removing ad9833.o(i.AD9833_Delay), (12 bytes).
    Removing ad9833.o(i.AD9833_Init), (100 bytes).
    Removing ad9833.o(i.AD9833_Init2), (100 bytes).
    Removing ad9833.o(i.AD9833_SetFrequency), (96 bytes).
    Removing ad9833.o(i.AD9833_SetFrequency2), (96 bytes).
    Removing ad9833.o(i.AD9833_SetPhase), (6 bytes).
    Removing ad9833.o(i.AD9833_SetPhase2), (6 bytes).
    Removing ad9833.o(i.AD9833_SetWave), (10 bytes).
    Removing ad9833.o(i.AD9833_SetWave2), (10 bytes).
    Removing ad9833.o(i.AD9833_Setup), (104 bytes).
    Removing ad9833.o(i.AD9833_Setup2), (104 bytes).
    Removing ad9833.o(i.AD9833_WriteData), (128 bytes).
    Removing ad9833.o(i.AD9833_WriteData2), (120 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing adc_app.o(i.adc_task), (152 bytes).
    Removing adc_app.o(.bss), (4096 bytes).
    Removing adc_app.o(.data), (4 bytes).
    Removing dac_app.o(.rev16_text), (4 bytes).
    Removing dac_app.o(.revsh_text), (4 bytes).
    Removing dac_app.o(.rrx_text), (6 bytes).
    Removing dac_app.o(i.dac_app_get_actual_frequency), (52 bytes).
    Removing dac_app.o(i.dac_app_get_amplitude), (12 bytes).
    Removing dac_app.o(i.dac_app_get_zero_based), (12 bytes).
    Removing dac_app.o(i.dac_app_set_frequency), (32 bytes).
    Removing dac_app.o(i.dac_app_set_zero_based), (44 bytes).
    Removing software_dds.o(.rev16_text), (4 bytes).
    Removing software_dds.o(.revsh_text), (4 bytes).
    Removing software_dds.o(.rrx_text), (6 bytes).
    Removing software_dds.o(i.DDS_OutputSample), (188 bytes).
    Removing software_dds.o(i.DDS_SetWaveform), (12 bytes).
    Removing my_fft.o(.rev16_text), (4 bytes).
    Removing my_fft.o(.revsh_text), (4 bytes).
    Removing my_fft.o(.rrx_text), (6 bytes).
    Removing my_filter.o(.rev16_text), (4 bytes).
    Removing my_filter.o(.revsh_text), (4 bytes).
    Removing my_filter.o(.rrx_text), (6 bytes).
    Removing my_filter.o(i.arm_fir_f32_lp), (40 bytes).
    Removing my_filter.o(.constdata), (4 bytes).
    Removing my_filter.o(.constdata), (204 bytes).
    Removing phase_measure.o(.rev16_text), (4 bytes).
    Removing phase_measure.o(.revsh_text), (4 bytes).
    Removing phase_measure.o(.rrx_text), (6 bytes).
    Removing phase_measure.o(i.calculate_phase_diff), (208 bytes).
    Removing phase_measure.o(.data), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rrx_text), (6 bytes).
    Removing kalman.o(i.Kalman_init), (48 bytes).
    Removing kalman.o(i.kalman), (96 bytes).
    Removing kalman.o(i.kalman_filter), (82 bytes).
    Removing kalman.o(i.kalman_thd), (64 bytes).
    Removing kalman.o(.bss), (308 bytes).
    Removing kalman.o(.data), (4 bytes).
    Removing wave_recognition.o(.rev16_text), (4 bytes).
    Removing wave_recognition.o(.revsh_text), (4 bytes).
    Removing wave_recognition.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(.rev16_text), (4 bytes).
    Removing my_hmi.o(.revsh_text), (4 bytes).
    Removing my_hmi.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(i.HMI_Send_Float), (188 bytes).
    Removing my_hmi.o(i.HMI_Send_Int), (120 bytes).
    Removing my_hmi.o(i.HMI_Send_String), (120 bytes).
    Removing my_hmi.o(i.HMI_Wave_Clear), (120 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Fast), (180 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Low), (128 bytes).
    Removing my_usart.o(.rev16_text), (4 bytes).
    Removing my_usart.o(.revsh_text), (4 bytes).
    Removing my_usart.o(.rrx_text), (6 bytes).
    Removing my_usart.o(.bss), (128 bytes).
    Removing my_usart.o(.data), (2 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart_pack.o(.rev16_text), (4 bytes).
    Removing my_usart_pack.o(.revsh_text), (4 bytes).
    Removing my_usart_pack.o(.rrx_text), (6 bytes).
    Removing my_usart_pack.o(i.ParseDataToVariables), (160 bytes).
    Removing my_usart_pack.o(i.ParseFrame), (66 bytes).
    Removing my_usart_pack.o(i.PrepareFrame), (232 bytes).
    Removing my_usart_pack.o(i.SendFrame), (24 bytes).
    Removing my_usart_pack.o(i.SetParseTemplate), (52 bytes).
    Removing my_usart_pack.o(.bss), (52 bytes).
    Removing my_usart_pack.o(.data), (2 bytes).
    Removing cmd_to_fun.o(.rev16_text), (4 bytes).
    Removing cmd_to_fun.o(.revsh_text), (4 bytes).
    Removing cmd_to_fun.o(.rrx_text), (6 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_READ_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_READ_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE), (42 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_SET), (42 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_START), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_STOP), (30 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler.o(i.uart_proc), (76 bytes).
    Removing scheduler.o(.data), (4 bytes).
    Removing app_pid.o(.rev16_text), (4 bytes).
    Removing app_pid.o(.revsh_text), (4 bytes).
    Removing app_pid.o(.rrx_text), (6 bytes).
    Removing app_pid.o(i.Pid_Proc), (84 bytes).
    Removing app_pid.o(i.increment_pid_ctrl), (118 bytes).
    Removing app_pid.o(.data), (8 bytes).
    Removing dds_test.o(.rev16_text), (4 bytes).
    Removing dds_test.o(.revsh_text), (4 bytes).
    Removing dds_test.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (64 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (198 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (136 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (296 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (312 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (524 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (64 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (166 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (268 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (276 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (100 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (304 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (100 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (220 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (108 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (684 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (448 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (52 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (136 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (116 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (68 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (24 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (52 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (62 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (24 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (372 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (42 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (34 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (98 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2980 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (106 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (310 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (74 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (148 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive), (124 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode), (100 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (56 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (40 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (136 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (184 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (40 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (232 bytes).
    Removing stm32f4xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit), (32 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler), (112 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop), (26 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (24 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (110 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (28 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (68 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (68 bytes).
    Removing stm32f4xx_ll_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_AttributeSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_CommonSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_DeInit), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC), (110 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_Init), (88 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_AttributeSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_CommonSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_IOSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_Init), (38 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_GetModeStatus), (22 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init), (94 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate), (12 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand), (80 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SetAutoRefreshNumber), (12 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init), (134 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Enable), (16 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (32 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (64 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (64 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (64 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (76 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (50 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (42 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (84 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (238 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (210 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (180 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (528 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (176 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (544 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (338 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (372 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (88 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (154 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (102 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (148 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (188 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (282 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (150 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (172 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (100 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (256 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (212 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (148 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_fir_init_f32.o(.text), (0 bytes).
    Removing arm_fir_init_f32.o(.text.arm_fir_init_f32), (32 bytes).
    Removing arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).

802 unused section(s) (total 932730 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f_x.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ..\MY_APP\app_pid.c                      0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\MY_APP\dds_test.c                     0x00000000   Number         0  dds_test.o ABSOLUTE
    ..\MY_APP\scheduler.c                    0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\MY_Algorithms\Src\kalman.c            0x00000000   Number         0  kalman.o ABSOLUTE
    ..\MY_Algorithms\Src\my_fft.c            0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\MY_Algorithms\Src\my_filter.c         0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\MY_Algorithms\Src\phase_measure.c     0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\MY_Algorithms\Src\wave_recognition.c  0x00000000   Number         0  wave_recognition.o ABSOLUTE
    ..\MY_Communication\Src\my_hmi.c         0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\MY_Communication\Src\my_usart.c       0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\MY_Communication\Src\my_usart_pack.c  0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\AD9833.c      0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\ad_measure.c  0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\adc_app.c     0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\da_output.c   0x00000000   Number         0  da_output.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\dac_app.c     0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\key_app.c     0x00000000   Number         0  key_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\software_dds.c 0x00000000   Number         0  software_dds.o ABSOLUTE
    ..\MY_Utilities\Src\cmd_to_fun.c         0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    ..\\MY_APP\\app_pid.c                    0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\\MY_APP\\dds_test.c                   0x00000000   Number         0  dds_test.o ABSOLUTE
    ..\\MY_APP\\scheduler.c                  0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\kalman.c         0x00000000   Number         0  kalman.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_fft.c         0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_filter.c      0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\phase_measure.c  0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\wave_recognition.c 0x00000000   Number         0  wave_recognition.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_hmi.c      0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart.c    0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart_pack.c 0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\AD9833.c   0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\ad_measure.c 0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\adc_app.c  0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\da_output.c 0x00000000   Number         0  da_output.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\dac_app.c  0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\key_app.c  0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\software_dds.c 0x00000000   Number         0  software_dds.o ABSOLUTE
    ..\\MY_Utilities\\Src\\cmd_to_fun.c      0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    arm_bitreversal.c                        0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    arm_cfft_radix4_f32.c                    0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    arm_cfft_radix4_init_f32.c               0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    arm_cmplx_mag_f32.c                      0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    arm_cos_f32.c                            0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    arm_fir_init_f32.c                       0x00000000   Number         0  arm_fir_init_f32.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f429xx.s                    0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c4   Section       36  startup_stm32f429xx.o(.text)
    $v0                                      0x080001c4   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x080001e8   Section        0  uldiv.o(.text)
    .text                                    0x0800024a   Section        0  memcpya.o(.text)
    .text                                    0x0800026e   Section        0  memseta.o(.text)
    .text                                    0x08000292   Section        0  strstr.o(.text)
    .text                                    0x080002b6   Section        0  strchr.o(.text)
    .text                                    0x080002ca   Section        0  strcmp.o(.text)
    .text                                    0x080002e6   Section        0  strcpy.o(.text)
    .text                                    0x080002f8   Section        0  strncmp.o(.text)
    .text                                    0x08000316   Section        0  atoi.o(.text)
    .text                                    0x08000330   Section        0  dmul.o(.text)
    .text                                    0x08000414   Section        0  ddiv.o(.text)
    .text                                    0x080004f2   Section        0  dflti.o(.text)
    .text                                    0x08000514   Section        0  ffixul.o(.text)
    .text                                    0x08000540   Section        0  dfixi.o(.text)
    .text                                    0x0800057e   Section        0  f2d.o(.text)
    .text                                    0x080005a4   Section       48  cdcmple.o(.text)
    .text                                    0x080005d4   Section       48  cdrcmple.o(.text)
    .text                                    0x08000604   Section        0  d2f.o(.text)
    .text                                    0x0800063c   Section        0  uidiv.o(.text)
    .text                                    0x08000668   Section        0  llshl.o(.text)
    .text                                    0x08000686   Section        0  llushr.o(.text)
    .text                                    0x080006a6   Section        0  strtol.o(.text)
    .text                                    0x08000716   Section        0  iusefp.o(.text)
    .text                                    0x08000716   Section        0  fepilogue.o(.text)
    .text                                    0x08000784   Section        0  frnd.o(.text)
    .text                                    0x080007c0   Section        0  depilogue.o(.text)
    .text                                    0x0800087a   Section        0  dadd.o(.text)
    .text                                    0x080009c8   Section        0  dfixul.o(.text)
    .text                                    0x080009f8   Section       36  init.o(.text)
    .text                                    0x08000a1c   Section        0  llsshr.o(.text)
    .text                                    0x08000a40   Section        0  ctype_o.o(.text)
    .text                                    0x08000a48   Section        0  _strtoul.o(.text)
    .text                                    0x08000ae6   Section        0  _chval.o(.text)
    [Anonymous Symbol]                       0x08000b04   Section        0  arm_bitreversal.o(.text.arm_bitreversal_f32)
    [Anonymous Symbol]                       0x08000bc2   Section        0  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    [Anonymous Symbol]                       0x08000c04   Section        0  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    [Anonymous Symbol]                       0x08000c98   Section        0  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    [Anonymous Symbol]                       0x08000dec   Section        0  arm_cos_f32.o(.text.arm_cos_f32)
    [Anonymous Symbol]                       0x08000e84   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    [Anonymous Symbol]                       0x080011e0   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    i.ADC_DMAConvCplt                        0x0800155a   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x0800155b   Thumb Code   116  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x080015ce   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x080015cf   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x080015e4   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x080015e5   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_IRQHandler                         0x080015f0   Section        0  stm32f4xx_it.o(i.ADC_IRQHandler)
    i.ADC_Init                               0x080015fc   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x080015fd   Thumb Code   298  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x0800172c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CTRL_INIT                              0x0800172e   Section        0  cmd_to_fun.o(i.CTRL_INIT)
    i.DAC_DMAConvCpltCh1                     0x08001738   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1)
    i.DAC_DMAConvCpltCh2                     0x08001748   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2)
    i.DAC_DMAErrorCh1                        0x08001758   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1)
    i.DAC_DMAErrorCh2                        0x08001770   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2)
    i.DAC_DMAHalfConvCpltCh1                 0x08001788   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1)
    i.DAC_DMAHalfConvCpltCh2                 0x08001792   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2)
    i.DA_Apply_Settings                      0x0800179c   Section        0  da_output.o(i.DA_Apply_Settings)
    i.DA_FPGA_START                          0x0800187c   Section        0  cmd_to_fun.o(i.DA_FPGA_START)
    i.DA_FPGA_STOP                           0x0800188a   Section        0  cmd_to_fun.o(i.DA_FPGA_STOP)
    i.DA_Init                                0x08001898   Section        0  da_output.o(i.DA_Init)
    i.DA_SetConfig                           0x080018d0   Section        0  da_output.o(i.DA_SetConfig)
    i.DDS_CalculatePhaseIncrement            0x080018f0   Section        0  software_dds.o(i.DDS_CalculatePhaseIncrement)
    i.DDS_GetConfig                          0x0800190c   Section        0  software_dds.o(i.DDS_GetConfig)
    i.DDS_Init                               0x0800191c   Section        0  software_dds.o(i.DDS_Init)
    i.DDS_SetAmplitude                       0x080019fc   Section        0  software_dds.o(i.DDS_SetAmplitude)
    i.DDS_SetFrequency                       0x08001a58   Section        0  software_dds.o(i.DDS_SetFrequency)
    i.DDS_SetPhase                           0x08001b04   Section        0  software_dds.o(i.DDS_SetPhase)
    i.DDS_Setup                              0x08001b20   Section        0  software_dds.o(i.DDS_Setup)
    i.DDS_Start                              0x08001b8c   Section        0  software_dds.o(i.DDS_Start)
    i.DDS_Stop                               0x08001c54   Section        0  software_dds.o(i.DDS_Stop)
    i.DDS_Test_FreqMinus                     0x08001c9c   Section        0  dds_test.o(i.DDS_Test_FreqMinus)
    i.DDS_Test_FreqPlus                      0x08001d44   Section        0  dds_test.o(i.DDS_Test_FreqPlus)
    i.DDS_Test_Init                          0x08001dec   Section        0  dds_test.o(i.DDS_Test_Init)
    i.DDS_Test_ProcessCommand                0x08001ebc   Section        0  dds_test.o(i.DDS_Test_ProcessCommand)
    i.DDS_Test_SetAmplitude                  0x0800207c   Section        0  dds_test.o(i.DDS_Test_SetAmplitude)
    i.DDS_Test_SetMode                       0x080020c4   Section        0  dds_test.o(i.DDS_Test_SetMode)
    i.DDS_Test_ShowDebugInfo                 0x0800218c   Section        0  dds_test.o(i.DDS_Test_ShowDebugInfo)
    i.DDS_Test_ShowHelp                      0x08002344   Section        0  dds_test.o(i.DDS_Test_ShowHelp)
    i.DDS_Test_ShowStatus                    0x080025dc   Section        0  dds_test.o(i.DDS_Test_ShowStatus)
    i.DDS_Test_Start                         0x08002778   Section        0  dds_test.o(i.DDS_Test_Start)
    i.DDS_Test_Stop                          0x080027bc   Section        0  dds_test.o(i.DDS_Test_Stop)
    i.DMA1_Stream5_IRQHandler                0x080027f4   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream0_IRQHandler                0x08002810   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x0800281c   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08002828   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08002829   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x0800285c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x0800285d   Thumb Code   126  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x080028da   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080028db   Thumb Code    56  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08002912   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08002914   Section        0  main.o(i.Error_Handler)
    i.FMC_NORSRAM_Extended_Timing_Init       0x08002918   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    i.FMC_NORSRAM_Init                       0x08002964   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    i.FMC_NORSRAM_Timing_Init                0x080029d4   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x08002a34   Section        0  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADC_ConfigChannel                  0x08002a38   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08002bc4   Section        0  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08002be0   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08002be2   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_IRQHandler                     0x08002be4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x08002d32   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x08002d88   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x08002d8c   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08002e34   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x08002fc8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DACEx_ConvCpltCallbackCh2          0x08003038   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2)
    i.HAL_DACEx_ConvHalfCpltCallbackCh2      0x0800303a   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2)
    i.HAL_DACEx_ErrorCallbackCh2             0x0800303c   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2)
    i.HAL_DAC_ConfigChannel                  0x0800303e   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_ConvCpltCallbackCh1            0x08003094   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1)
    i.HAL_DAC_ConvHalfCpltCallbackCh1        0x08003096   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1)
    i.HAL_DAC_ErrorCallbackCh1               0x08003098   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1)
    i.HAL_DAC_Init                           0x0800309a   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x080030c4   Section        0  dac.o(i.HAL_DAC_MspInit)
    i.HAL_DAC_SetValue                       0x08003164   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    i.HAL_DAC_Start                          0x0800318a   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    i.HAL_DAC_Start_DMA                      0x080031f4   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
    i.HAL_DAC_Stop_DMA                       0x08003300   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA)
    i.HAL_DMA_Abort                          0x08003356   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080033f8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x0800341c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08003608   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080036f4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_FMC_MspInit                        0x08003764   Section        0  fmc.o(i.HAL_FMC_MspInit)
    HAL_FMC_MspInit                          0x08003765   Thumb Code   138  fmc.o(i.HAL_FMC_MspInit)
    i.HAL_GPIO_Init                          0x08003808   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08003a54   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08003a62   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08003a70   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08003a7c   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003a8c   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08003ac0   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003b04   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003b34   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003b50   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003bb8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_EnableOverDrive              0x08003bdc   Section        0  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    i.HAL_RCC_CSSCallback                    0x08003c58   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    i.HAL_RCC_ClockConfig                    0x08003c5c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_EnableCSS                      0x08003dd8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    i.HAL_RCC_GetHCLKFreq                    0x08003de4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08003df0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003e10   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003e30   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_NMI_IRQHandler                 0x08003ea0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    i.HAL_RCC_OscConfig                      0x08003ec0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SRAM_Init                          0x08004342   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x0800439a   Section        0  fmc.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_Config                     0x080043a0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080043c8   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08004462   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080044c0   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x08004500   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Start_IT                  0x08004580   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Base_Stop                      0x0800460a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    i.HAL_TIM_Base_Stop_IT                   0x08004630   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    i.HAL_TIM_ConfigClockSource              0x08004660   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_GenerateEvent                  0x0800474a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08004774   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x080047d8   Section        0  my_usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08004838   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x080048a8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080048ac   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004b0c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004b70   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08004d00   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004d30   Section        0  my_usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08004df8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08004dfa   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004ec4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004ec6   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_ADC1_Init                           0x08004ec8   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DAC_Init                            0x08004f28   Section        0  dac.o(i.MX_DAC_Init)
    i.MX_DMA_Init                            0x08004f78   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FMC_Init                            0x08004fd4   Section        0  fmc.o(i.MX_FMC_Init)
    i.MX_GPIO_Init                           0x08005048   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM3_Init                           0x080051e8   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM6_Init                           0x08005250   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_USART1_UART_Init                    0x08005298   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x080052e8   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08005320   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08005358   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800535a   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PID_Init                               0x08005360   Section        0  app_pid.o(i.PID_Init)
    i.PendSV_Handler                         0x080053b0   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080053b2   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080053b4   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080053b8   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005458   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08005468   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08005534   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x0800554a   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x0800554b   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x0800555a   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x0800555b   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005580   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005581   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x080055a4   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080055a5   Thumb Code    16  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080055b4   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080055b5   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x080055fe   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x080055ff   Thumb Code   132  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08005682   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08005683   Thumb Code    26  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x0800569c   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800569d   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x080056ea   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x080056eb   Thumb Code    26  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08005704   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08005705   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08005720   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005721   Thumb Code   200  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080057e8   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080057e9   Thumb Code   248  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x080058e4   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08005978   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x080059ae   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080059af   Thumb Code    94  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08005a0c   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08005a0d   Thumb Code   140  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08005a98   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08005aa4   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08005ab0   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08005abc   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0sprintf                             0x08005ac0   Section        0  printfa.o(i.__0sprintf)
    i.__0vsnprintf                           0x08005ae8   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassifyf                      0x08005b1c   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__aeabi_errno_addr                     0x08005b44   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__hardfp_cosf                          0x08005b4c   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_log10f                        0x08005c9c   Section        0  log10f.o(i.__hardfp_log10f)
    i.__hardfp_roundf                        0x08005e1c   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sinf                          0x08005eb8   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sqrtf                         0x08006048   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x08006084   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08006098   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x080060a0   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x080060b0   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x080060c0   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__scatterload_copy                     0x08006214   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08006222   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08006224   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08006234   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08006240   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08006241   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080063c4   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080063c5   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08006a78   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08006a79   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08006a9c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08006a9d   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08006aca   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08006acb   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x08006ae0   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08006ae1   Thumb Code    10  printfa.o(i._sputc)
    i.adc_tim_dma_init                       0x08006aec   Section        0  adc_app.o(i.adc_tim_dma_init)
    i.calculate_bandwidth                    0x08006b20   Section        0  wave_recognition.o(i.calculate_bandwidth)
    i.calculate_carrier_suppression          0x08006ba8   Section        0  wave_recognition.o(i.calculate_carrier_suppression)
    i.calculate_fft_spectrum                 0x08006c30   Section        0  my_fft.o(i.calculate_fft_spectrum)
    i.calculate_sinad                        0x08006cf4   Section        0  my_fft.o(i.calculate_sinad)
    i.calculate_thd                          0x08006d2c   Section        0  my_fft.o(i.calculate_thd)
    i.calculate_thd_n                        0x08006e98   Section        0  my_fft.o(i.calculate_thd_n)
    i.circuit_learning_task                  0x08006f88   Section        0  scheduler.o(i.circuit_learning_task)
    i.dac_app_init                           0x080070e0   Section        0  dac_app.o(i.dac_app_init)
    i.dac_app_set_amplitude                  0x08007100   Section        0  dac_app.o(i.dac_app_set_amplitude)
    i.dac_app_set_waveform                   0x08007168   Section        0  dac_app.o(i.dac_app_set_waveform)
    i.detect_symmetry                        0x0800718c   Section        0  wave_recognition.o(i.detect_symmetry)
    i.fft_init                               0x08007240   Section        0  my_fft.o(i.fft_init)
    i.find_peaks                             0x0800725c   Section        0  wave_recognition.o(i.find_peaks)
    i.generate_hanning_window                0x08007358   Section        0  my_fft.o(i.generate_hanning_window)
    i.generate_sine                          0x080073b8   Section        0  dac_app.o(i.generate_sine)
    generate_sine                            0x080073b9   Thumb Code   202  dac_app.o(i.generate_sine)
    i.generate_square                        0x08007494   Section        0  dac_app.o(i.generate_square)
    generate_square                          0x08007495   Thumb Code   116  dac_app.o(i.generate_square)
    i.generate_triangle                      0x08007510   Section        0  dac_app.o(i.generate_triangle)
    generate_triangle                        0x08007511   Thumb Code   292  dac_app.o(i.generate_triangle)
    i.generate_waveform                      0x08007640   Section        0  dac_app.o(i.generate_waveform)
    generate_waveform                        0x08007641   Thumb Code    28  dac_app.o(i.generate_waveform)
    i.get_current_ad_frequency               0x08007660   Section        0  key_app.o(i.get_current_ad_frequency)
    i.get_precise_peak_frequency             0x0800766c   Section        0  my_fft.o(i.get_precise_peak_frequency)
    i.key_proc                               0x0800773c   Section        0  key_app.o(i.key_proc)
    i.key_read                               0x080078d4   Section        0  key_app.o(i.key_read)
    i.main                                   0x08007918   Section        0  main.o(i.main)
    i.my_printf                              0x08007a24   Section        0  my_usart.o(i.my_printf)
    i.output_fft_spectrum                    0x08007a58   Section        0  my_fft.o(i.output_fft_spectrum)
    i.preprocess_signal                      0x08007d54   Section        0  wave_recognition.o(i.preprocess_signal)
    preprocess_signal                        0x08007d55   Thumb Code   162  wave_recognition.o(i.preprocess_signal)
    i.process_uart1_command                  0x08007e00   Section        0  my_usart.o(i.process_uart1_command)
    i.process_uart2_command                  0x0800844c   Section        0  my_usart.o(i.process_uart2_command)
    i.recognize_waveform                     0x080086b8   Section        0  wave_recognition.o(i.recognize_waveform)
    i.round_to_nearest_k                     0x080089e4   Section        0  my_fft.o(i.round_to_nearest_k)
    i.scheduler_init                         0x08008a08   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08008a14   Section        0  scheduler.o(i.scheduler_run)
    i.start_circuit_learning                 0x08008a54   Section        0  scheduler.o(i.start_circuit_learning)
    i.start_dac_dma                          0x08008b24   Section        0  dac_app.o(i.start_dac_dma)
    start_dac_dma                            0x08008b25   Thumb Code    44  dac_app.o(i.start_dac_dma)
    i.stop_dac_dma                           0x08008b5c   Section        0  dac_app.o(i.stop_dac_dma)
    stop_dac_dma                             0x08008b5d   Thumb Code    22  dac_app.o(i.stop_dac_dma)
    i.update_timer_frequency                 0x08008b7c   Section        0  dac_app.o(i.update_timer_frequency)
    update_timer_frequency                   0x08008b7d   Thumb Code   334  dac_app.o(i.update_timer_frequency)
    .constdata                               0x08008cd8   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08008cd8   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08008ce0   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08008cf0   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08008cf8   Section       64  log10f.o(.constdata)
    logahi                                   0x08008cf8   Data          32  log10f.o(.constdata)
    logalo                                   0x08008d18   Data          32  log10f.o(.constdata)
    .constdata                               0x08008d38   Section       32  rredf.o(.constdata)
    twooverpi                                0x08008d38   Data          32  rredf.o(.constdata)
    .constdata                               0x08008d58   Section      129  ctype_o.o(.constdata)
    .constdata                               0x08008ddc   Section        4  ctype_o.o(.constdata)
    table                                    0x08008ddc   Data           4  ctype_o.o(.constdata)
    .conststring                             0x08008de0   Section       70  wave_recognition.o(.conststring)
    .conststring                             0x08008e28   Section      143  dds_test.o(.conststring)
    .data                                    0x20000000   Section        8  fmc.o(.data)
    FMC_Initialized                          0x20000000   Data           4  fmc.o(.data)
    FMC_DeInitialized                        0x20000004   Data           4  fmc.o(.data)
    .data                                    0x20000008   Section       12  key_app.o(.data)
    waveform_type                            0x2000000c   Data           1  key_app.o(.data)
    current_phase                            0x2000000e   Data           2  key_app.o(.data)
    current_ad_freq                          0x20000010   Data           4  key_app.o(.data)
    .data                                    0x20000014   Section        1  adc_app.o(.data)
    .data                                    0x20000018   Section       12  dac_app.o(.data)
    current_waveform                         0x20000018   Data           1  dac_app.o(.data)
    zero_based_waveform                      0x20000019   Data           1  dac_app.o(.data)
    current_peak_amplitude_mv                0x2000001a   Data           2  dac_app.o(.data)
    dac_amplitude_raw                        0x2000001c   Data           2  dac_app.o(.data)
    current_frequency_hz                     0x20000020   Data           4  dac_app.o(.data)
    .data                                    0x20000024   Section       32  software_dds.o(.data)
    table_initialized                        0x20000024   Data           1  software_dds.o(.data)
    phase_accumulator                        0x20000028   Data           4  software_dds.o(.data)
    phase_increment                          0x2000002c   Data           4  software_dds.o(.data)
    dds_debug_counter                        0x20000030   Data           4  software_dds.o(.data)
    dds_sample_counter                       0x20000034   Data           4  software_dds.o(.data)
    dds_config                               0x20000038   Data          12  software_dds.o(.data)
    .data                                    0x20000044   Section       10  my_usart.o(.data)
    .data                                    0x20000050   Section       52  scheduler.o(.data)
    learning_active                          0x20000050   Data           1  scheduler.o(.data)
    learning_step                            0x20000051   Data           1  scheduler.o(.data)
    learning_start_time                      0x20000054   Data           4  scheduler.o(.data)
    current_freq                             0x20000058   Data           4  scheduler.o(.data)
    step_start_time                          0x2000005c   Data           4  scheduler.o(.data)
    scheduler_task                           0x20000060   Data          36  scheduler.o(.data)
    .data                                    0x20000084   Section       12  dds_test.o(.data)
    test_config                              0x20000084   Data          12  dds_test.o(.data)
    .data                                    0x20000090   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000009c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x200000a0   Section        4  errno.o(.data)
    _errno                                   0x200000a0   Data           4  errno.o(.data)
    .bss                                     0x200000a4   Section      168  adc.o(.bss)
    .bss                                     0x2000014c   Section      116  dac.o(.bss)
    .bss                                     0x200001c0   Section       80  fmc.o(.bss)
    .bss                                     0x20000210   Section      144  tim.o(.bss)
    .bss                                     0x200002a0   Section      396  usart.o(.bss)
    .bss                                     0x2000042c   Section    12288  ad_measure.o(.bss)
    .bss                                     0x2000342c   Section       24  da_output.o(.bss)
    .bss                                     0x20003444   Section    12288  adc_app.o(.bss)
    .bss                                     0x20006444   Section      256  dac_app.o(.bss)
    waveform_buffer                          0x20006444   Data         256  dac_app.o(.bss)
    .bss                                     0x20006544   Section     2048  software_dds.o(.bss)
    sine_table                               0x20006544   Data        2048  software_dds.o(.bss)
    .bss                                     0x20006d44   Section    16404  my_fft.o(.bss)
    .bss                                     0x2000ad58   Section      512  my_usart.o(.bss)
    .bss                                     0x2000af58   Section     4048  scheduler.o(.bss)
    spectrum_buffer                          0x2000af58   Data        2000  scheduler.o(.bss)
    adc_buffer                               0x2000b728   Data        2048  scheduler.o(.bss)
    .bss                                     0x2000bf28   Section       36  app_pid.o(.bss)
    STACK                                    0x2000bf50   Section     1024  startup_stm32f429xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001c5   Thumb Code     8  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM7_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    __aeabi_uldivmod                         0x080001e9   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x0800024b   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800026f   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800027d   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800027d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800027d   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000281   Thumb Code    18  memseta.o(.text)
    strstr                                   0x08000293   Thumb Code    36  strstr.o(.text)
    strchr                                   0x080002b7   Thumb Code    20  strchr.o(.text)
    strcmp                                   0x080002cb   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x080002e7   Thumb Code    18  strcpy.o(.text)
    strncmp                                  0x080002f9   Thumb Code    30  strncmp.o(.text)
    atoi                                     0x08000317   Thumb Code    26  atoi.o(.text)
    __aeabi_dmul                             0x08000331   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000415   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x080004f3   Thumb Code    34  dflti.o(.text)
    __aeabi_f2ulz                            0x08000515   Thumb Code    44  ffixul.o(.text)
    __aeabi_d2iz                             0x08000541   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x0800057f   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x080005a5   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x080005a5   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x080005d5   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x08000605   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x0800063d   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800063d   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x08000669   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000669   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000687   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000687   Thumb Code     0  llushr.o(.text)
    strtol                                   0x080006a7   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x08000717   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000717   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000729   Thumb Code    92  fepilogue.o(.text)
    _frnd                                    0x08000785   Thumb Code    60  frnd.o(.text)
    _double_round                            0x080007c1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080007df   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x0800087b   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080009bd   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080009c3   Thumb Code     6  dadd.o(.text)
    __aeabi_d2ulz                            0x080009c9   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x080009f9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080009f9   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000a1d   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000a1d   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x08000a41   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x08000a49   Thumb Code   158  _strtoul.o(.text)
    _chval                                   0x08000ae7   Thumb Code    28  _chval.o(.text)
    arm_bitreversal_f32                      0x08000b05   Thumb Code   190  arm_bitreversal.o(.text.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x08000bc3   Thumb Code    64  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x08000c05   Thumb Code   148  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x08000c99   Thumb Code   340  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    arm_cos_f32                              0x08000ded   Thumb Code   152  arm_cos_f32.o(.text.arm_cos_f32)
    arm_radix4_butterfly_f32                 0x08000e85   Thumb Code   858  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x080011e1   Thumb Code   890  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    ADC_IRQHandler                           0x080015f1   Thumb Code     6  stm32f4xx_it.o(i.ADC_IRQHandler)
    BusFault_Handler                         0x0800172d   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    CTRL_INIT                                0x0800172f   Thumb Code    10  cmd_to_fun.o(i.CTRL_INIT)
    DAC_DMAConvCpltCh1                       0x08001739   Thumb Code    16  stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1)
    DAC_DMAConvCpltCh2                       0x08001749   Thumb Code    16  stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2)
    DAC_DMAErrorCh1                          0x08001759   Thumb Code    24  stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1)
    DAC_DMAErrorCh2                          0x08001771   Thumb Code    24  stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2)
    DAC_DMAHalfConvCpltCh1                   0x08001789   Thumb Code    10  stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1)
    DAC_DMAHalfConvCpltCh2                   0x08001793   Thumb Code    10  stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2)
    DA_Apply_Settings                        0x0800179d   Thumb Code   206  da_output.o(i.DA_Apply_Settings)
    DA_FPGA_START                            0x0800187d   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_START)
    DA_FPGA_STOP                             0x0800188b   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_STOP)
    DA_Init                                  0x08001899   Thumb Code    50  da_output.o(i.DA_Init)
    DA_SetConfig                             0x080018d1   Thumb Code    28  da_output.o(i.DA_SetConfig)
    DDS_CalculatePhaseIncrement              0x080018f1   Thumb Code    20  software_dds.o(i.DDS_CalculatePhaseIncrement)
    DDS_GetConfig                            0x0800190d   Thumb Code    10  software_dds.o(i.DDS_GetConfig)
    DDS_Init                                 0x0800191d   Thumb Code   140  software_dds.o(i.DDS_Init)
    DDS_SetAmplitude                         0x080019fd   Thumb Code    32  software_dds.o(i.DDS_SetAmplitude)
    DDS_SetFrequency                         0x08001a59   Thumb Code    54  software_dds.o(i.DDS_SetFrequency)
    DDS_SetPhase                             0x08001b05   Thumb Code    22  software_dds.o(i.DDS_SetPhase)
    DDS_Setup                                0x08001b21   Thumb Code    52  software_dds.o(i.DDS_Setup)
    DDS_Start                                0x08001b8d   Thumb Code    66  software_dds.o(i.DDS_Start)
    DDS_Stop                                 0x08001c55   Thumb Code    40  software_dds.o(i.DDS_Stop)
    DDS_Test_FreqMinus                       0x08001c9d   Thumb Code    74  dds_test.o(i.DDS_Test_FreqMinus)
    DDS_Test_FreqPlus                        0x08001d45   Thumb Code    76  dds_test.o(i.DDS_Test_FreqPlus)
    DDS_Test_Init                            0x08001ded   Thumb Code    42  dds_test.o(i.DDS_Test_Init)
    DDS_Test_ProcessCommand                  0x08001ebd   Thumb Code   252  dds_test.o(i.DDS_Test_ProcessCommand)
    DDS_Test_SetAmplitude                    0x0800207d   Thumb Code    40  dds_test.o(i.DDS_Test_SetAmplitude)
    DDS_Test_SetMode                         0x080020c5   Thumb Code    78  dds_test.o(i.DDS_Test_SetMode)
    DDS_Test_ShowDebugInfo                   0x0800218d   Thumb Code   144  dds_test.o(i.DDS_Test_ShowDebugInfo)
    DDS_Test_ShowHelp                        0x08002345   Thumb Code   134  dds_test.o(i.DDS_Test_ShowHelp)
    DDS_Test_ShowStatus                      0x080025dd   Thumb Code   158  dds_test.o(i.DDS_Test_ShowStatus)
    DDS_Test_Start                           0x08002779   Thumb Code    40  dds_test.o(i.DDS_Test_Start)
    DDS_Test_Stop                            0x080027bd   Thumb Code    28  dds_test.o(i.DDS_Test_Stop)
    DMA1_Stream5_IRQHandler                  0x080027f5   Thumb Code    18  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream0_IRQHandler                  0x08002811   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x0800281d   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08002913   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08002915   Thumb Code     4  main.o(i.Error_Handler)
    FMC_NORSRAM_Extended_Timing_Init         0x08002919   Thumb Code    72  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    FMC_NORSRAM_Init                         0x08002965   Thumb Code   108  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    FMC_NORSRAM_Timing_Init                  0x080029d5   Thumb Code    96  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    HAL_ADCEx_InjectedConvCpltCallback       0x08002a35   Thumb Code     2  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADC_ConfigChannel                    0x08002a39   Thumb Code   374  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08002bc5   Thumb Code    20  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08002be1   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08002be3   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x08002be5   Thumb Code   334  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08002d33   Thumb Code    86  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x08002d89   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x08002d8d   Thumb Code   146  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08002e35   Thumb Code   372  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x08002fc9   Thumb Code   112  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DACEx_ConvCpltCallbackCh2            0x08003039   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2)
    HAL_DACEx_ConvHalfCpltCallbackCh2        0x0800303b   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2)
    HAL_DACEx_ErrorCallbackCh2               0x0800303d   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2)
    HAL_DAC_ConfigChannel                    0x0800303f   Thumb Code    86  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_ConvCpltCallbackCh1              0x08003095   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1)
    HAL_DAC_ConvHalfCpltCallbackCh1          0x08003097   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1)
    HAL_DAC_ErrorCallbackCh1                 0x08003099   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1)
    HAL_DAC_Init                             0x0800309b   Thumb Code    42  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x080030c5   Thumb Code   138  dac.o(i.HAL_DAC_MspInit)
    HAL_DAC_SetValue                         0x08003165   Thumb Code    38  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    HAL_DAC_Start                            0x0800318b   Thumb Code   104  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    HAL_DAC_Start_DMA                        0x080031f5   Thumb Code   244  stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
    HAL_DAC_Stop_DMA                         0x08003301   Thumb Code    86  stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA)
    HAL_DMA_Abort                            0x08003357   Thumb Code   162  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080033f9   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x0800341d   Thumb Code   488  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08003609   Thumb Code   230  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080036f5   Thumb Code   112  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x08003809   Thumb Code   564  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08003a55   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08003a63   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08003a71   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08003a7d   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003a8d   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08003ac1   Thumb Code    58  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003b05   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003b35   Thumb Code    28  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003b51   Thumb Code    98  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003bb9   Thumb Code    30  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x08003bdd   Thumb Code   110  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    HAL_RCC_CSSCallback                      0x08003c59   Thumb Code     2  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    HAL_RCC_ClockConfig                      0x08003c5d   Thumb Code   354  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_EnableCSS                        0x08003dd9   Thumb Code     8  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    HAL_RCC_GetHCLKFreq                      0x08003de5   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003df1   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003e11   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003e31   Thumb Code    94  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_NMI_IRQHandler                   0x08003ea1   Thumb Code    24  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    HAL_RCC_OscConfig                        0x08003ec1   Thumb Code  1154  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x08004343   Thumb Code    88  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x0800439b   Thumb Code     4  fmc.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x080043a1   Thumb Code    36  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x080043c9   Thumb Code   154  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08004463   Thumb Code    92  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080044c1   Thumb Code    52  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x08004501   Thumb Code   128  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x08004581   Thumb Code   138  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Base_Stop                        0x0800460b   Thumb Code    38  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    HAL_TIM_Base_Stop_IT                     0x08004631   Thumb Code    48  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    HAL_TIM_ConfigClockSource                0x08004661   Thumb Code   234  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_GenerateEvent                    0x0800474b   Thumb Code    42  stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08004775   Thumb Code    98  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080047d9   Thumb Code    74  my_usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08004839   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x080048a9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080048ad   Thumb Code   604  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004b0d   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004b71   Thumb Code   364  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08004d01   Thumb Code    46  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004d31   Thumb Code   168  my_usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08004df9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08004dfb   Thumb Code   202  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004ec5   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004ec7   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x08004ec9   Thumb Code    88  adc.o(i.MX_ADC1_Init)
    MX_DAC_Init                              0x08004f29   Thumb Code    70  dac.o(i.MX_DAC_Init)
    MX_DMA_Init                              0x08004f79   Thumb Code    88  dma.o(i.MX_DMA_Init)
    MX_FMC_Init                              0x08004fd5   Thumb Code   108  fmc.o(i.MX_FMC_Init)
    MX_GPIO_Init                             0x08005049   Thumb Code   382  gpio.o(i.MX_GPIO_Init)
    MX_TIM3_Init                             0x080051e9   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_TIM6_Init                             0x08005251   Thumb Code    62  tim.o(i.MX_TIM6_Init)
    MX_USART1_UART_Init                      0x08005299   Thumb Code    66  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x080052e9   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08005321   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08005359   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800535b   Thumb Code     6  stm32f4xx_it.o(i.NMI_Handler)
    PID_Init                                 0x08005361   Thumb Code    60  app_pid.o(i.PID_Init)
    PendSV_Handler                           0x080053b1   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x080053b3   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080053b5   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080053b9   Thumb Code   150  main.o(i.SystemClock_Config)
    SystemInit                               0x08005459   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08005469   Thumb Code   182  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08005535   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_DMA                   0x080058e5   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08005979   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08005a99   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08005aa5   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08005ab1   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08005abd   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0sprintf                               0x08005ac1   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08005ac1   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08005ac1   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08005ac1   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08005ac1   Thumb Code     0  printfa.o(i.__0sprintf)
    __0vsnprintf                             0x08005ae9   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08005ae9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08005ae9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08005ae9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08005ae9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassifyf                        0x08005b1d   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __aeabi_errno_addr                       0x08005b45   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08005b45   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __hardfp_cosf                            0x08005b4d   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_log10f                          0x08005c9d   Thumb Code   332  log10f.o(i.__hardfp_log10f)
    __hardfp_roundf                          0x08005e1d   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sinf                            0x08005eb9   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sqrtf                           0x08006049   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x08006085   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08006099   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x080060a1   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x080060b1   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x080060c1   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __scatterload_copy                       0x08006215   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08006223   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08006225   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08006235   Thumb Code     6  errno.o(i.__set_errno)
    adc_tim_dma_init                         0x08006aed   Thumb Code    34  adc_app.o(i.adc_tim_dma_init)
    calculate_bandwidth                      0x08006b21   Thumb Code   132  wave_recognition.o(i.calculate_bandwidth)
    calculate_carrier_suppression            0x08006ba9   Thumb Code   118  wave_recognition.o(i.calculate_carrier_suppression)
    calculate_fft_spectrum                   0x08006c31   Thumb Code   180  my_fft.o(i.calculate_fft_spectrum)
    calculate_sinad                          0x08006cf5   Thumb Code    48  my_fft.o(i.calculate_sinad)
    calculate_thd                            0x08006d2d   Thumb Code   336  my_fft.o(i.calculate_thd)
    calculate_thd_n                          0x08006e99   Thumb Code   210  my_fft.o(i.calculate_thd_n)
    circuit_learning_task                    0x08006f89   Thumb Code   208  scheduler.o(i.circuit_learning_task)
    dac_app_init                             0x080070e1   Thumb Code    28  dac_app.o(i.dac_app_init)
    dac_app_set_amplitude                    0x08007101   Thumb Code    92  dac_app.o(i.dac_app_set_amplitude)
    dac_app_set_waveform                     0x08007169   Thumb Code    30  dac_app.o(i.dac_app_set_waveform)
    detect_symmetry                          0x0800718d   Thumb Code   168  wave_recognition.o(i.detect_symmetry)
    fft_init                                 0x08007241   Thumb Code    22  my_fft.o(i.fft_init)
    find_peaks                               0x0800725d   Thumb Code   238  wave_recognition.o(i.find_peaks)
    generate_hanning_window                  0x08007359   Thumb Code    82  my_fft.o(i.generate_hanning_window)
    get_current_ad_frequency                 0x08007661   Thumb Code     8  key_app.o(i.get_current_ad_frequency)
    get_precise_peak_frequency               0x0800766d   Thumb Code   192  my_fft.o(i.get_precise_peak_frequency)
    key_proc                                 0x0800773d   Thumb Code   176  key_app.o(i.key_proc)
    key_read                                 0x080078d5   Thumb Code    60  key_app.o(i.key_read)
    main                                     0x08007919   Thumb Code   162  main.o(i.main)
    my_printf                                0x08007a25   Thumb Code    50  my_usart.o(i.my_printf)
    output_fft_spectrum                      0x08007a59   Thumb Code   436  my_fft.o(i.output_fft_spectrum)
    process_uart1_command                    0x08007e01   Thumb Code  1538  my_usart.o(i.process_uart1_command)
    process_uart2_command                    0x0800844d   Thumb Code   244  my_usart.o(i.process_uart2_command)
    recognize_waveform                       0x080086b9   Thumb Code   532  wave_recognition.o(i.recognize_waveform)
    round_to_nearest_k                       0x080089e5   Thumb Code    32  my_fft.o(i.round_to_nearest_k)
    scheduler_init                           0x08008a09   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08008a15   Thumb Code    60  scheduler.o(i.scheduler_run)
    start_circuit_learning                   0x08008a55   Thumb Code    62  scheduler.o(i.start_circuit_learning)
    AHBPrescTable                            0x08008ce0   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08008cf0   Data           8  system_stm32f4xx.o(.constdata)
    __ctype_table                            0x08008d58   Data         129  ctype_o.o(.constdata)
    armBitRevTable                           0x08008eb8   Data        2048  arm_common_tables.o(.rodata.armBitRevTable)
    sinTable_f32                             0x080096b8   Data        2052  arm_common_tables.o(.rodata.sinTable_f32)
    twiddleCoef_4096                         0x08009ebc   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    Region$$Table$$Base                      0x08011ebc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08011edc   Number         0  anon$$obj.o(Region$$Table)
    key_val                                  0x20000008   Data           1  key_app.o(.data)
    key_old                                  0x20000009   Data           1  key_app.o(.data)
    key_down                                 0x2000000a   Data           1  key_app.o(.data)
    key_up                                   0x2000000b   Data           1  key_app.o(.data)
    AdcConvEnd                               0x20000014   Data           1  adc_app.o(.data)
    commandReceived2                         0x20000044   Data           1  my_usart.o(.data)
    commandReceived3                         0x20000045   Data           1  my_usart.o(.data)
    uart1_cmd_flag                           0x20000046   Data           1  my_usart.o(.data)
    rxTemp3                                  0x20000047   Data           1  my_usart.o(.data)
    rxTemp2                                  0x20000048   Data           1  my_usart.o(.data)
    rxIndex3                                 0x2000004a   Data           2  my_usart.o(.data)
    rxIndex2                                 0x2000004c   Data           2  my_usart.o(.data)
    task_num                                 0x20000052   Data           1  scheduler.o(.data)
    uwTickFreq                               0x20000090   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000094   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000098   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000009c   Data           4  system_stm32f4xx.o(.data)
    hadc1                                    0x200000a4   Data          72  adc.o(.bss)
    hdma_adc1                                0x200000ec   Data          96  adc.o(.bss)
    hdac                                     0x2000014c   Data          20  dac.o(.bss)
    hdma_dac1                                0x20000160   Data          96  dac.o(.bss)
    hsram2                                   0x200001c0   Data          80  fmc.o(.bss)
    htim3                                    0x20000210   Data          72  tim.o(.bss)
    htim6                                    0x20000258   Data          72  tim.o(.bss)
    huart1                                   0x200002a0   Data          68  usart.o(.bss)
    huart2                                   0x200002e4   Data          68  usart.o(.bss)
    huart3                                   0x20000328   Data          68  usart.o(.bss)
    hdma_usart1_rx                           0x2000036c   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x200003cc   Data          96  usart.o(.bss)
    fifo_data1                               0x2000042c   Data        2048  ad_measure.o(.bss)
    fifo_data2                               0x20000c2c   Data        2048  ad_measure.o(.bss)
    fifo_data1_f                             0x2000142c   Data        4096  ad_measure.o(.bss)
    fifo_data2_f                             0x2000242c   Data        4096  ad_measure.o(.bss)
    da_channels                              0x2000342c   Data          24  da_output.o(.bss)
    dac_val_buffer                           0x20003444   Data        4096  adc_app.o(.bss)
    adc_val_buffer                           0x20004444   Data        8192  adc_app.o(.bss)
    fft_instance                             0x20006d44   Data          20  my_fft.o(.bss)
    fft_input_buffer                         0x20006d58   Data        8192  my_fft.o(.bss)
    fft_magnitude                            0x20008d58   Data        4096  my_fft.o(.bss)
    window_buffer                            0x20009d58   Data        4096  my_fft.o(.bss)
    rxBuffer3                                0x2000ad58   Data         128  my_usart.o(.bss)
    rxBuffer2                                0x2000add8   Data         128  my_usart.o(.bss)
    uart1_dma_buffer                         0x2000ae58   Data         128  my_usart.o(.bss)
    uart1_cmd_buffer                         0x2000aed8   Data         128  my_usart.o(.bss)
    PID                                      0x2000bf28   Data          36  app_pid.o(.bss)
    __initial_sp                             0x2000c350   Data           0  startup_stm32f429xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00011f80, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00011edc, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         5676  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         6034    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         6037    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6039    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6041    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         6042    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         6049    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         6044    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         6046    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         6035    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x00000024   Code   RO            4    .text               startup_stm32f429xx.o
    0x080001e8   0x080001e8   0x00000062   Code   RO         5679    .text               mc_w.l(uldiv.o)
    0x0800024a   0x0800024a   0x00000024   Code   RO         5681    .text               mc_w.l(memcpya.o)
    0x0800026e   0x0800026e   0x00000024   Code   RO         5683    .text               mc_w.l(memseta.o)
    0x08000292   0x08000292   0x00000024   Code   RO         5685    .text               mc_w.l(strstr.o)
    0x080002b6   0x080002b6   0x00000014   Code   RO         5687    .text               mc_w.l(strchr.o)
    0x080002ca   0x080002ca   0x0000001c   Code   RO         5689    .text               mc_w.l(strcmp.o)
    0x080002e6   0x080002e6   0x00000012   Code   RO         5691    .text               mc_w.l(strcpy.o)
    0x080002f8   0x080002f8   0x0000001e   Code   RO         5693    .text               mc_w.l(strncmp.o)
    0x08000316   0x08000316   0x0000001a   Code   RO         5958    .text               mc_w.l(atoi.o)
    0x08000330   0x08000330   0x000000e4   Code   RO         5960    .text               mf_w.l(dmul.o)
    0x08000414   0x08000414   0x000000de   Code   RO         5962    .text               mf_w.l(ddiv.o)
    0x080004f2   0x080004f2   0x00000022   Code   RO         5964    .text               mf_w.l(dflti.o)
    0x08000514   0x08000514   0x0000002c   Code   RO         5966    .text               mf_w.l(ffixul.o)
    0x08000540   0x08000540   0x0000003e   Code   RO         5968    .text               mf_w.l(dfixi.o)
    0x0800057e   0x0800057e   0x00000026   Code   RO         5970    .text               mf_w.l(f2d.o)
    0x080005a4   0x080005a4   0x00000030   Code   RO         5972    .text               mf_w.l(cdcmple.o)
    0x080005d4   0x080005d4   0x00000030   Code   RO         5974    .text               mf_w.l(cdrcmple.o)
    0x08000604   0x08000604   0x00000038   Code   RO         5976    .text               mf_w.l(d2f.o)
    0x0800063c   0x0800063c   0x0000002c   Code   RO         6053    .text               mc_w.l(uidiv.o)
    0x08000668   0x08000668   0x0000001e   Code   RO         6055    .text               mc_w.l(llshl.o)
    0x08000686   0x08000686   0x00000020   Code   RO         6057    .text               mc_w.l(llushr.o)
    0x080006a6   0x080006a6   0x00000070   Code   RO         6066    .text               mc_w.l(strtol.o)
    0x08000716   0x08000716   0x00000000   Code   RO         6068    .text               mc_w.l(iusefp.o)
    0x08000716   0x08000716   0x0000006e   Code   RO         6069    .text               mf_w.l(fepilogue.o)
    0x08000784   0x08000784   0x0000003c   Code   RO         6071    .text               mf_w.l(frnd.o)
    0x080007c0   0x080007c0   0x000000ba   Code   RO         6073    .text               mf_w.l(depilogue.o)
    0x0800087a   0x0800087a   0x0000014e   Code   RO         6075    .text               mf_w.l(dadd.o)
    0x080009c8   0x080009c8   0x00000030   Code   RO         6081    .text               mf_w.l(dfixul.o)
    0x080009f8   0x080009f8   0x00000024   Code   RO         6083    .text               mc_w.l(init.o)
    0x08000a1c   0x08000a1c   0x00000024   Code   RO         6086    .text               mc_w.l(llsshr.o)
    0x08000a40   0x08000a40   0x00000008   Code   RO         6088    .text               mc_w.l(ctype_o.o)
    0x08000a48   0x08000a48   0x0000009e   Code   RO         6116    .text               mc_w.l(_strtoul.o)
    0x08000ae6   0x08000ae6   0x0000001c   Code   RO         6121    .text               mc_w.l(_chval.o)
    0x08000b02   0x08000b02   0x00000002   PAD
    0x08000b04   0x08000b04   0x000000be   Code   RO         5475    .text.arm_bitreversal_f32  arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08000bc2   0x08000bc2   0x00000040   Code   RO         5452    .text.arm_cfft_radix4_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08000c02   0x08000c02   0x00000002   PAD
    0x08000c04   0x08000c04   0x00000094   Code   RO         5466    .text.arm_cfft_radix4_init_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08000c98   0x08000c98   0x00000154   Code   RO         5433    .text.arm_cmplx_mag_f32  arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08000dec   0x08000dec   0x00000098   Code   RO         5424    .text.arm_cos_f32   arm_cortexM4lf_math.lib(arm_cos_f32.o)
    0x08000e84   0x08000e84   0x0000035a   Code   RO         5456    .text.arm_radix4_butterfly_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x080011de   0x080011de   0x00000002   PAD
    0x080011e0   0x080011e0   0x0000037a   Code   RO         5454    .text.arm_radix4_butterfly_inverse_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800155a   0x0800155a   0x00000074   Code   RO         1960    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x080015ce   0x080015ce   0x00000016   Code   RO         1961    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x080015e4   0x080015e4   0x0000000a   Code   RO         1962    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x080015ee   0x080015ee   0x00000002   PAD
    0x080015f0   0x080015f0   0x0000000c   Code   RO          571    i.ADC_IRQHandler    stm32f4xx_it.o
    0x080015fc   0x080015fc   0x00000130   Code   RO         1963    i.ADC_Init          stm32f4xx_hal_adc.o
    0x0800172c   0x0800172c   0x00000002   Code   RO          572    i.BusFault_Handler  stm32f4xx_it.o
    0x0800172e   0x0800172e   0x0000000a   Code   RO         1691    i.CTRL_INIT         cmd_to_fun.o
    0x08001738   0x08001738   0x00000010   Code   RO         3497    i.DAC_DMAConvCpltCh1  stm32f4xx_hal_dac.o
    0x08001748   0x08001748   0x00000010   Code   RO         3641    i.DAC_DMAConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08001758   0x08001758   0x00000018   Code   RO         3498    i.DAC_DMAErrorCh1   stm32f4xx_hal_dac.o
    0x08001770   0x08001770   0x00000018   Code   RO         3642    i.DAC_DMAErrorCh2   stm32f4xx_hal_dac_ex.o
    0x08001788   0x08001788   0x0000000a   Code   RO         3499    i.DAC_DMAHalfConvCpltCh1  stm32f4xx_hal_dac.o
    0x08001792   0x08001792   0x0000000a   Code   RO         3643    i.DAC_DMAHalfConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x0800179c   0x0800179c   0x000000e0   Code   RO          768    i.DA_Apply_Settings  da_output.o
    0x0800187c   0x0800187c   0x0000000e   Code   RO         1692    i.DA_FPGA_START     cmd_to_fun.o
    0x0800188a   0x0800188a   0x0000000e   Code   RO         1693    i.DA_FPGA_STOP      cmd_to_fun.o
    0x08001898   0x08001898   0x00000038   Code   RO          769    i.DA_Init           da_output.o
    0x080018d0   0x080018d0   0x00000020   Code   RO          770    i.DA_SetConfig      da_output.o
    0x080018f0   0x080018f0   0x0000001c   Code   RO         1154    i.DDS_CalculatePhaseIncrement  software_dds.o
    0x0800190c   0x0800190c   0x00000010   Code   RO         1155    i.DDS_GetConfig     software_dds.o
    0x0800191c   0x0800191c   0x000000e0   Code   RO         1156    i.DDS_Init          software_dds.o
    0x080019fc   0x080019fc   0x0000005c   Code   RO         1158    i.DDS_SetAmplitude  software_dds.o
    0x08001a58   0x08001a58   0x000000ac   Code   RO         1159    i.DDS_SetFrequency  software_dds.o
    0x08001b04   0x08001b04   0x0000001c   Code   RO         1160    i.DDS_SetPhase      software_dds.o
    0x08001b20   0x08001b20   0x0000006c   Code   RO         1162    i.DDS_Setup         software_dds.o
    0x08001b8c   0x08001b8c   0x000000c8   Code   RO         1163    i.DDS_Start         software_dds.o
    0x08001c54   0x08001c54   0x00000048   Code   RO         1164    i.DDS_Stop          software_dds.o
    0x08001c9c   0x08001c9c   0x000000a8   Code   RO         1869    i.DDS_Test_FreqMinus  dds_test.o
    0x08001d44   0x08001d44   0x000000a8   Code   RO         1870    i.DDS_Test_FreqPlus  dds_test.o
    0x08001dec   0x08001dec   0x000000d0   Code   RO         1871    i.DDS_Test_Init     dds_test.o
    0x08001ebc   0x08001ebc   0x000001c0   Code   RO         1872    i.DDS_Test_ProcessCommand  dds_test.o
    0x0800207c   0x0800207c   0x00000048   Code   RO         1873    i.DDS_Test_SetAmplitude  dds_test.o
    0x080020c4   0x080020c4   0x000000c8   Code   RO         1874    i.DDS_Test_SetMode  dds_test.o
    0x0800218c   0x0800218c   0x000001b8   Code   RO         1875    i.DDS_Test_ShowDebugInfo  dds_test.o
    0x08002344   0x08002344   0x00000298   Code   RO         1876    i.DDS_Test_ShowHelp  dds_test.o
    0x080025dc   0x080025dc   0x0000019c   Code   RO         1877    i.DDS_Test_ShowStatus  dds_test.o
    0x08002778   0x08002778   0x00000044   Code   RO         1878    i.DDS_Test_Start    dds_test.o
    0x080027bc   0x080027bc   0x00000038   Code   RO         1879    i.DDS_Test_Stop     dds_test.o
    0x080027f4   0x080027f4   0x0000001c   Code   RO          573    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08002810   0x08002810   0x0000000c   Code   RO          574    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x0800281c   0x0800281c   0x0000000c   Code   RO          575    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08002828   0x08002828   0x00000034   Code   RO         2799    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x0800285c   0x0800285c   0x0000007e   Code   RO         2800    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x080028da   0x080028da   0x00000038   Code   RO         2801    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08002912   0x08002912   0x00000002   Code   RO          576    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002914   0x08002914   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08002918   0x08002918   0x0000004c   Code   RO         3745    i.FMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fmc.o
    0x08002964   0x08002964   0x00000070   Code   RO         3746    i.FMC_NORSRAM_Init  stm32f4xx_ll_fmc.o
    0x080029d4   0x080029d4   0x00000060   Code   RO         3747    i.FMC_NORSRAM_Timing_Init  stm32f4xx_ll_fmc.o
    0x08002a34   0x08002a34   0x00000002   Code   RO         2134    i.HAL_ADCEx_InjectedConvCpltCallback  stm32f4xx_hal_adc_ex.o
    0x08002a36   0x08002a36   0x00000002   PAD
    0x08002a38   0x08002a38   0x0000018c   Code   RO         1965    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08002bc4   0x08002bc4   0x0000001c   Code   RO         1006    i.HAL_ADC_ConvCpltCallback  adc_app.o
    0x08002be0   0x08002be0   0x00000002   Code   RO         1967    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x08002be2   0x08002be2   0x00000002   Code   RO         1969    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x08002be4   0x08002be4   0x0000014e   Code   RO         1973    i.HAL_ADC_IRQHandler  stm32f4xx_hal_adc.o
    0x08002d32   0x08002d32   0x00000056   Code   RO         1974    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08002d88   0x08002d88   0x00000002   Code   RO         1975    i.HAL_ADC_LevelOutOfWindowCallback  stm32f4xx_hal_adc.o
    0x08002d8a   0x08002d8a   0x00000002   PAD
    0x08002d8c   0x08002d8c   0x000000a8   Code   RO          313    i.HAL_ADC_MspInit   adc.o
    0x08002e34   0x08002e34   0x00000194   Code   RO         1981    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x08002fc8   0x08002fc8   0x00000070   Code   RO         1984    i.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x08003038   0x08003038   0x00000002   Code   RO         3644    i.HAL_DACEx_ConvCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x0800303a   0x0800303a   0x00000002   Code   RO         3645    i.HAL_DACEx_ConvHalfCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x0800303c   0x0800303c   0x00000002   Code   RO         3651    i.HAL_DACEx_ErrorCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x0800303e   0x0800303e   0x00000056   Code   RO         3500    i.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x08003094   0x08003094   0x00000002   Code   RO         3501    i.HAL_DAC_ConvCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x08003096   0x08003096   0x00000002   Code   RO         3502    i.HAL_DAC_ConvHalfCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x08003098   0x08003098   0x00000002   Code   RO         3505    i.HAL_DAC_ErrorCallbackCh1  stm32f4xx_hal_dac.o
    0x0800309a   0x0800309a   0x0000002a   Code   RO         3510    i.HAL_DAC_Init      stm32f4xx_hal_dac.o
    0x080030c4   0x080030c4   0x000000a0   Code   RO          355    i.HAL_DAC_MspInit   dac.o
    0x08003164   0x08003164   0x00000026   Code   RO         3513    i.HAL_DAC_SetValue  stm32f4xx_hal_dac.o
    0x0800318a   0x0800318a   0x00000068   Code   RO         3514    i.HAL_DAC_Start     stm32f4xx_hal_dac.o
    0x080031f2   0x080031f2   0x00000002   PAD
    0x080031f4   0x080031f4   0x0000010c   Code   RO         3515    i.HAL_DAC_Start_DMA  stm32f4xx_hal_dac.o
    0x08003300   0x08003300   0x00000056   Code   RO         3517    i.HAL_DAC_Stop_DMA  stm32f4xx_hal_dac.o
    0x08003356   0x08003356   0x000000a2   Code   RO         2802    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080033f8   0x080033f8   0x00000024   Code   RO         2803    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x0800341c   0x0800341c   0x000001ec   Code   RO         2807    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08003608   0x08003608   0x000000ec   Code   RO         2808    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080036f4   0x080036f4   0x00000070   Code   RO         2812    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08003764   0x08003764   0x000000a4   Code   RO          420    i.HAL_FMC_MspInit   fmc.o
    0x08003808   0x08003808   0x0000024c   Code   RO         2695    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08003a54   0x08003a54   0x0000000e   Code   RO         2697    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08003a62   0x08003a62   0x0000000e   Code   RO         2699    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08003a70   0x08003a70   0x0000000c   Code   RO         3242    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003a7c   0x08003a7c   0x00000010   Code   RO         3248    i.HAL_IncTick       stm32f4xx_hal.o
    0x08003a8c   0x08003a8c   0x00000034   Code   RO         3249    i.HAL_Init          stm32f4xx_hal.o
    0x08003ac0   0x08003ac0   0x00000044   Code   RO         3250    i.HAL_InitTick      stm32f4xx_hal.o
    0x08003b04   0x08003b04   0x00000030   Code   RO          689    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08003b34   0x08003b34   0x0000001c   Code   RO         3102    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08003b50   0x08003b50   0x00000068   Code   RO         3108    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08003bb8   0x08003bb8   0x00000024   Code   RO         3109    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08003bdc   0x08003bdc   0x0000007c   Code   RO         3031    i.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x08003c58   0x08003c58   0x00000002   Code   RO         2238    i.HAL_RCC_CSSCallback  stm32f4xx_hal_rcc.o
    0x08003c5a   0x08003c5a   0x00000002   PAD
    0x08003c5c   0x08003c5c   0x0000017c   Code   RO         2239    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08003dd8   0x08003dd8   0x0000000c   Code   RO         2242    i.HAL_RCC_EnableCSS  stm32f4xx_hal_rcc.o
    0x08003de4   0x08003de4   0x0000000c   Code   RO         2244    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08003df0   0x08003df0   0x00000020   Code   RO         2246    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08003e10   0x08003e10   0x00000020   Code   RO         2247    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08003e30   0x08003e30   0x00000070   Code   RO         2248    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003ea0   0x08003ea0   0x00000020   Code   RO         2250    i.HAL_RCC_NMI_IRQHandler  stm32f4xx_hal_rcc.o
    0x08003ec0   0x08003ec0   0x00000482   Code   RO         2251    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08004342   0x08004342   0x00000058   Code   RO         3921    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x0800439a   0x0800439a   0x00000004   Code   RO          422    i.HAL_SRAM_MspInit  fmc.o
    0x0800439e   0x0800439e   0x00000002   PAD
    0x080043a0   0x080043a0   0x00000028   Code   RO         3113    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080043c8   0x080043c8   0x0000009a   Code   RO         4773    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08004462   0x08004462   0x0000005c   Code   RO         4039    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x080044be   0x080044be   0x00000002   PAD
    0x080044c0   0x080044c0   0x00000040   Code   RO          469    i.HAL_TIM_Base_MspInit  tim.o
    0x08004500   0x08004500   0x00000080   Code   RO         4042    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08004580   0x08004580   0x0000008a   Code   RO         4044    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x0800460a   0x0800460a   0x00000026   Code   RO         4045    i.HAL_TIM_Base_Stop  stm32f4xx_hal_tim.o
    0x08004630   0x08004630   0x00000030   Code   RO         4047    i.HAL_TIM_Base_Stop_IT  stm32f4xx_hal_tim.o
    0x08004660   0x08004660   0x000000ea   Code   RO         4048    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x0800474a   0x0800474a   0x0000002a   Code   RO         4070    i.HAL_TIM_GenerateEvent  stm32f4xx_hal_tim.o
    0x08004774   0x08004774   0x00000062   Code   RO         5030    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x080047d6   0x080047d6   0x00000002   PAD
    0x080047d8   0x080047d8   0x00000060   Code   RO         1561    i.HAL_UARTEx_RxEventCallback  my_usart.o
    0x08004838   0x08004838   0x00000070   Code   RO         5044    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x080048a8   0x080048a8   0x00000002   Code   RO         5046    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x080048aa   0x080048aa   0x00000002   PAD
    0x080048ac   0x080048ac   0x00000260   Code   RO         5049    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08004b0c   0x08004b0c   0x00000064   Code   RO         5050    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08004b70   0x08004b70   0x00000190   Code   RO          517    i.HAL_UART_MspInit  usart.o
    0x08004d00   0x08004d00   0x0000002e   Code   RO         5055    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08004d2e   0x08004d2e   0x00000002   PAD
    0x08004d30   0x08004d30   0x000000c8   Code   RO         1562    i.HAL_UART_RxCpltCallback  my_usart.o
    0x08004df8   0x08004df8   0x00000002   Code   RO         5057    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08004dfa   0x08004dfa   0x000000ca   Code   RO         5058    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004ec4   0x08004ec4   0x00000002   Code   RO         5061    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004ec6   0x08004ec6   0x00000002   Code   RO          577    i.HardFault_Handler  stm32f4xx_it.o
    0x08004ec8   0x08004ec8   0x00000060   Code   RO          314    i.MX_ADC1_Init      adc.o
    0x08004f28   0x08004f28   0x00000050   Code   RO          356    i.MX_DAC_Init       dac.o
    0x08004f78   0x08004f78   0x0000005c   Code   RO          396    i.MX_DMA_Init       dma.o
    0x08004fd4   0x08004fd4   0x00000074   Code   RO          423    i.MX_FMC_Init       fmc.o
    0x08005048   0x08005048   0x000001a0   Code   RO          288    i.MX_GPIO_Init      gpio.o
    0x080051e8   0x080051e8   0x00000068   Code   RO          470    i.MX_TIM3_Init      tim.o
    0x08005250   0x08005250   0x00000048   Code   RO          471    i.MX_TIM6_Init      tim.o
    0x08005298   0x08005298   0x00000050   Code   RO          518    i.MX_USART1_UART_Init  usart.o
    0x080052e8   0x080052e8   0x00000038   Code   RO          519    i.MX_USART2_UART_Init  usart.o
    0x08005320   0x08005320   0x00000038   Code   RO          520    i.MX_USART3_UART_Init  usart.o
    0x08005358   0x08005358   0x00000002   Code   RO          578    i.MemManage_Handler  stm32f4xx_it.o
    0x0800535a   0x0800535a   0x00000006   Code   RO          579    i.NMI_Handler       stm32f4xx_it.o
    0x08005360   0x08005360   0x00000050   Code   RO         1825    i.PID_Init          app_pid.o
    0x080053b0   0x080053b0   0x00000002   Code   RO          580    i.PendSV_Handler    stm32f4xx_it.o
    0x080053b2   0x080053b2   0x00000002   Code   RO          581    i.SVC_Handler       stm32f4xx_it.o
    0x080053b4   0x080053b4   0x00000004   Code   RO          582    i.SysTick_Handler   stm32f4xx_it.o
    0x080053b8   0x080053b8   0x000000a0   Code   RO           15    i.SystemClock_Config  main.o
    0x08005458   0x08005458   0x00000010   Code   RO         5389    i.SystemInit        system_stm32f4xx.o
    0x08005468   0x08005468   0x000000cc   Code   RO         4132    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08005534   0x08005534   0x00000016   Code   RO         4143    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x0800554a   0x0800554a   0x00000010   Code   RO         4144    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x0800555a   0x0800555a   0x00000026   Code   RO         4150    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005580   0x08005580   0x00000024   Code   RO         4152    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080055a4   0x080055a4   0x00000010   Code   RO         5063    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080055b4   0x080055b4   0x0000004a   Code   RO         5064    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x080055fe   0x080055fe   0x00000084   Code   RO         5065    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08005682   0x08005682   0x0000001a   Code   RO         5067    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x0800569c   0x0800569c   0x0000004e   Code   RO         5073    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x080056ea   0x080056ea   0x0000001a   Code   RO         5074    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08005704   0x08005704   0x0000001c   Code   RO         5075    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08005720   0x08005720   0x000000c8   Code   RO         5076    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080057e8   0x080057e8   0x000000fc   Code   RO         5077    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080058e4   0x080058e4   0x00000094   Code   RO         5078    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08005978   0x08005978   0x00000036   Code   RO         5079    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x080059ae   0x080059ae   0x0000005e   Code   RO         5080    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08005a0c   0x08005a0c   0x0000008c   Code   RO         5081    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08005a98   0x08005a98   0x0000000c   Code   RO          583    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08005aa4   0x08005aa4   0x0000000c   Code   RO          584    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08005ab0   0x08005ab0   0x0000000c   Code   RO          585    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08005abc   0x08005abc   0x00000002   Code   RO          586    i.UsageFault_Handler  stm32f4xx_it.o
    0x08005abe   0x08005abe   0x00000002   PAD
    0x08005ac0   0x08005ac0   0x00000028   Code   RO         5932    i.__0sprintf        mc_w.l(printfa.o)
    0x08005ae8   0x08005ae8   0x00000034   Code   RO         5935    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08005b1c   0x08005b1c   0x00000026   Code   RO         6000    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08005b42   0x08005b42   0x00000002   PAD
    0x08005b44   0x08005b44   0x00000008   Code   RO         6059    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x08005b4c   0x08005b4c   0x00000150   Code   RO         5608    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x08005c9c   0x08005c9c   0x00000180   Code   RO         5620    i.__hardfp_log10f   m_wm.l(log10f.o)
    0x08005e1c   0x08005e1c   0x0000009a   Code   RO         5672    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x08005eb6   0x08005eb6   0x00000002   PAD
    0x08005eb8   0x08005eb8   0x00000190   Code   RO         5648    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x08006048   0x08006048   0x0000003a   Code   RO         5660    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08006082   0x08006082   0x00000002   PAD
    0x08006084   0x08006084   0x00000014   Code   RO         6002    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08006098   0x08006098   0x00000006   Code   RO         6003    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x0800609e   0x0800609e   0x00000002   PAD
    0x080060a0   0x080060a0   0x00000010   Code   RO         6005    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x080060b0   0x080060b0   0x00000010   Code   RO         6008    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x080060c0   0x080060c0   0x00000154   Code   RO         6019    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x08006214   0x08006214   0x0000000e   Code   RO         6125    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08006222   0x08006222   0x00000002   Code   RO         6126    i.__scatterload_null  mc_w.l(handlers.o)
    0x08006224   0x08006224   0x0000000e   Code   RO         6127    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08006232   0x08006232   0x00000002   PAD
    0x08006234   0x08006234   0x0000000c   Code   RO         6061    i.__set_errno       mc_w.l(errno.o)
    0x08006240   0x08006240   0x00000184   Code   RO         5937    i._fp_digits        mc_w.l(printfa.o)
    0x080063c4   0x080063c4   0x000006b4   Code   RO         5938    i._printf_core      mc_w.l(printfa.o)
    0x08006a78   0x08006a78   0x00000024   Code   RO         5939    i._printf_post_padding  mc_w.l(printfa.o)
    0x08006a9c   0x08006a9c   0x0000002e   Code   RO         5940    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08006aca   0x08006aca   0x00000016   Code   RO         5941    i._snputc           mc_w.l(printfa.o)
    0x08006ae0   0x08006ae0   0x0000000a   Code   RO         5942    i._sputc            mc_w.l(printfa.o)
    0x08006aea   0x08006aea   0x00000002   PAD
    0x08006aec   0x08006aec   0x00000034   Code   RO         1008    i.adc_tim_dma_init  adc_app.o
    0x08006b20   0x08006b20   0x00000088   Code   RO         1434    i.calculate_bandwidth  wave_recognition.o
    0x08006ba8   0x08006ba8   0x00000088   Code   RO         1435    i.calculate_carrier_suppression  wave_recognition.o
    0x08006c30   0x08006c30   0x000000c4   Code   RO         1239    i.calculate_fft_spectrum  my_fft.o
    0x08006cf4   0x08006cf4   0x00000038   Code   RO         1240    i.calculate_sinad   my_fft.o
    0x08006d2c   0x08006d2c   0x0000016c   Code   RO         1241    i.calculate_thd     my_fft.o
    0x08006e98   0x08006e98   0x000000f0   Code   RO         1242    i.calculate_thd_n   my_fft.o
    0x08006f88   0x08006f88   0x00000158   Code   RO         1772    i.circuit_learning_task  scheduler.o
    0x080070e0   0x080070e0   0x00000020   Code   RO         1052    i.dac_app_init      dac_app.o
    0x08007100   0x08007100   0x00000068   Code   RO         1053    i.dac_app_set_amplitude  dac_app.o
    0x08007168   0x08007168   0x00000024   Code   RO         1055    i.dac_app_set_waveform  dac_app.o
    0x0800718c   0x0800718c   0x000000b4   Code   RO         1436    i.detect_symmetry   wave_recognition.o
    0x08007240   0x08007240   0x0000001c   Code   RO         1243    i.fft_init          my_fft.o
    0x0800725c   0x0800725c   0x000000fc   Code   RO         1437    i.find_peaks        wave_recognition.o
    0x08007358   0x08007358   0x00000060   Code   RO         1244    i.generate_hanning_window  my_fft.o
    0x080073b8   0x080073b8   0x000000dc   Code   RO         1057    i.generate_sine     dac_app.o
    0x08007494   0x08007494   0x0000007c   Code   RO         1058    i.generate_square   dac_app.o
    0x08007510   0x08007510   0x00000130   Code   RO         1059    i.generate_triangle  dac_app.o
    0x08007640   0x08007640   0x00000020   Code   RO         1060    i.generate_waveform  dac_app.o
    0x08007660   0x08007660   0x0000000c   Code   RO          864    i.get_current_ad_frequency  key_app.o
    0x0800766c   0x0800766c   0x000000d0   Code   RO         1245    i.get_precise_peak_frequency  my_fft.o
    0x0800773c   0x0800773c   0x00000198   Code   RO          865    i.key_proc          key_app.o
    0x080078d4   0x080078d4   0x00000044   Code   RO          866    i.key_read          key_app.o
    0x08007918   0x08007918   0x0000010c   Code   RO           16    i.main              main.o
    0x08007a24   0x08007a24   0x00000032   Code   RO         1563    i.my_printf         my_usart.o
    0x08007a56   0x08007a56   0x00000002   PAD
    0x08007a58   0x08007a58   0x000002fc   Code   RO         1246    i.output_fft_spectrum  my_fft.o
    0x08007d54   0x08007d54   0x000000ac   Code   RO         1438    i.preprocess_signal  wave_recognition.o
    0x08007e00   0x08007e00   0x0000064c   Code   RO         1564    i.process_uart1_command  my_usart.o
    0x0800844c   0x0800844c   0x0000026c   Code   RO         1565    i.process_uart2_command  my_usart.o
    0x080086b8   0x080086b8   0x0000032c   Code   RO         1439    i.recognize_waveform  wave_recognition.o
    0x080089e4   0x080089e4   0x00000024   Code   RO         1247    i.round_to_nearest_k  my_fft.o
    0x08008a08   0x08008a08   0x0000000c   Code   RO         1773    i.scheduler_init    scheduler.o
    0x08008a14   0x08008a14   0x00000040   Code   RO         1774    i.scheduler_run     scheduler.o
    0x08008a54   0x08008a54   0x000000d0   Code   RO         1775    i.start_circuit_learning  scheduler.o
    0x08008b24   0x08008b24   0x00000038   Code   RO         1061    i.start_dac_dma     dac_app.o
    0x08008b5c   0x08008b5c   0x00000020   Code   RO         1062    i.stop_dac_dma      dac_app.o
    0x08008b7c   0x08008b7c   0x0000015c   Code   RO         1063    i.update_timer_frequency  dac_app.o
    0x08008cd8   0x08008cd8   0x00000008   Data   RO         2814    .constdata          stm32f4xx_hal_dma.o
    0x08008ce0   0x08008ce0   0x00000010   Data   RO         5390    .constdata          system_stm32f4xx.o
    0x08008cf0   0x08008cf0   0x00000008   Data   RO         5391    .constdata          system_stm32f4xx.o
    0x08008cf8   0x08008cf8   0x00000040   Data   RO         5623    .constdata          m_wm.l(log10f.o)
    0x08008d38   0x08008d38   0x00000020   Data   RO         6020    .constdata          m_wm.l(rredf.o)
    0x08008d58   0x08008d58   0x00000081   Data   RO         6089    .constdata          mc_w.l(ctype_o.o)
    0x08008dd9   0x08008dd9   0x00000003   PAD
    0x08008ddc   0x08008ddc   0x00000004   Data   RO         6090    .constdata          mc_w.l(ctype_o.o)
    0x08008de0   0x08008de0   0x00000046   Data   RO         1440    .conststring        wave_recognition.o
    0x08008e26   0x08008e26   0x00000002   PAD
    0x08008e28   0x08008e28   0x0000008f   Data   RO         1880    .conststring        dds_test.o
    0x08008eb7   0x08008eb7   0x00000001   PAD
    0x08008eb8   0x08008eb8   0x00000800   Data   RO         5489    .rodata.armBitRevTable  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080096b8   0x080096b8   0x00000804   Data   RO         5601    .rodata.sinTable_f32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08009ebc   0x08009ebc   0x00008000   Data   RO         5507    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08011ebc   0x08011ebc   0x00000020   Data   RO         6123    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08011f80, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08011edc, Size: 0x0000c350, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08011edc   0x00000008   Data   RW          425    .data               fmc.o
    0x20000008   0x08011ee4   0x0000000c   Data   RW          868    .data               key_app.o
    0x20000014   0x08011ef0   0x00000001   Data   RW         1011    .data               adc_app.o
    0x20000015   0x08011ef1   0x00000003   PAD
    0x20000018   0x08011ef4   0x0000000c   Data   RW         1065    .data               dac_app.o
    0x20000024   0x08011f00   0x00000020   Data   RW         1166    .data               software_dds.o
    0x20000044   0x08011f20   0x0000000a   Data   RW         1570    .data               my_usart.o
    0x2000004e   0x08011f2a   0x00000002   PAD
    0x20000050   0x08011f2c   0x00000034   Data   RW         1779    .data               scheduler.o
    0x20000084   0x08011f60   0x0000000c   Data   RW         1881    .data               dds_test.o
    0x20000090   0x08011f6c   0x0000000c   Data   RW         3256    .data               stm32f4xx_hal.o
    0x2000009c   0x08011f78   0x00000004   Data   RW         5392    .data               system_stm32f4xx.o
    0x200000a0   0x08011f7c   0x00000004   Data   RW         6062    .data               mc_w.l(errno.o)
    0x200000a4        -       0x000000a8   Zero   RW          315    .bss                adc.o
    0x2000014c        -       0x00000074   Zero   RW          357    .bss                dac.o
    0x200001c0        -       0x00000050   Zero   RW          424    .bss                fmc.o
    0x20000210        -       0x00000090   Zero   RW          472    .bss                tim.o
    0x200002a0        -       0x0000018c   Zero   RW          521    .bss                usart.o
    0x2000042c        -       0x00003000   Zero   RW          718    .bss                ad_measure.o
    0x2000342c        -       0x00000018   Zero   RW          772    .bss                da_output.o
    0x20003444        -       0x00003000   Zero   RW         1009    .bss                adc_app.o
    0x20006444        -       0x00000100   Zero   RW         1064    .bss                dac_app.o
    0x20006544        -       0x00000800   Zero   RW         1165    .bss                software_dds.o
    0x20006d44        -       0x00004014   Zero   RW         1248    .bss                my_fft.o
    0x2000ad58        -       0x00000200   Zero   RW         1567    .bss                my_usart.o
    0x2000af58        -       0x00000fd0   Zero   RW         1777    .bss                scheduler.o
    0x2000bf28        -       0x00000024   Zero   RW         1828    .bss                app_pid.o
    0x2000bf4c   0x08011f80   0x00000004   PAD
    0x2000bf50        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0      12288       1148   ad_measure.o
       264         30          0          0        168       1887   adc.o
        80         26          0          1      12288       2092   adc_app.o
        80         20          0          0         36        792   app_pid.o
        38          0          0          0          0       1511   cmd_to_fun.o
       312         28          0          0         24       3433   da_output.o
       240         32          0          0        116       1873   dac.o
      1288        100          0         12        256      10145   dac_app.o
      2904       1838        143         12          0       7970   dds_test.o
        92          4          0          0          0        882   dma.o
       284         34          0          8         80       2374   fmc.o
       416         34          0          0          0       1275   gpio.o
       488        244          0         12          0       2413   key_app.o
       432        116          0          0          0     813340   main.o
      1988        450          0          0      16404       9526   my_fft.o
      2578       1524          0         10        512       6013   my_usart.o
       628        296          0         52       4048       3723   scheduler.o
       940        504          0         32       2048       6731   software_dds.o
        36          8        428          0       1024        864   startup_stm32f429xx.o
       148         24          0         12          0       9113   stm32f4xx_hal.o
      1790         60          0          0          0       9677   stm32f4xx_hal_adc.o
         2          0          0          0          0       1061   stm32f4xx_hal_adc_ex.o
       208         16          0          0          0      33795   stm32f4xx_hal_cortex.o
       680         24          0          0          0       9496   stm32f4xx_hal_dac.o
        56          0          0          0          0       4213   stm32f4xx_hal_dac_ex.o
      1272         18          8          0          0       7742   stm32f4xx_hal_dma.o
       616         24          0          0          0       3183   stm32f4xx_hal_gpio.o
        48          6          0          0          0        910   stm32f4xx_hal_msp.o
       124         14          0          0          0       1356   stm32f4xx_hal_pwr_ex.o
      1768        116          0          0          0       8427   stm32f4xx_hal_rcc.o
        88          0          0          0          0       1276   stm32f4xx_hal_sram.o
      1036         22          0          0          0       9697   stm32f4xx_hal_tim.o
       154          0          0          0          0       1456   stm32f4xx_hal_tim_ex.o
      2440         22          0          0          0      18816   stm32f4xx_hal_uart.o
       124         46          0          0          0       7953   stm32f4xx_it.o
       284          8          0          0          0       3064   stm32f4xx_ll_fmc.o
        16          4         24          4          0       1211   system_stm32f4xx.o
       240         30          0          0        144       2435   tim.o
       592         66          0          0        396       3517   usart.o
      1688        338         70          0          0       8842   wave_recognition.o

    ----------------------------------------------------------------------
     26486       <USER>        <GROUP>        160      49836    1025232   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          3          5          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       190          0          0          0          0       2756   arm_bitreversal.o
      1812          0          0          0          0       9774   arm_cfft_radix4_f32.o
       148         20          0          0          0       1573   arm_cfft_radix4_init_f32.o
       340          0          0          0          0       2270   arm_cmplx_mag_f32.o
         0          0      36868          0          0       6294   arm_common_tables.o
       152         12          0          0          0       1104   arm_cos_f32.o
       336         56          0          0          0        136   cosf.o
        38          0          0          0          0        116   fpclassifyf.o
        58         18          0          0          0        464   funder.o
       384         52         64          0          0        140   log10f.o
       154          0          0          0          0        140   roundf.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
        58          0          0          0          0        136   sqrtf.o
        28          0          0          0          0         68   _chval.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        20         10          0          4          0        136   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2310         92          0          0          0        680   printfa.o
        20          0          0          0          0         68   strchr.o
        28          0          0          0          0         76   strcmp.o
        18          0          0          0          0         68   strcpy.o
        30          0          0          0          0         80   strncmp.o
        36          0          0          0          0         80   strstr.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        44          0          0          0          0         68   ffixul.o
        60          0          0          0          0         76   frnd.o

    ----------------------------------------------------------------------
      9142        <USER>      <GROUP>          4          0      28827   Library Totals
        18          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2642         32      36868          0          0      23771   arm_cortexM4lf_math.lib
      1768        206         96          0          0       1504   m_wm.l
      3196        122        133          4          0       2204   mc_w.l
      1518          0          0          0          0       1348   mf_w.l

    ----------------------------------------------------------------------
      9142        <USER>      <GROUP>          4          0      28827   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     35628       6486      37808        164      49836    1032323   Grand Totals
     35628       6486      37808        164      49836    1032323   ELF Image Totals
     35628       6486      37808        164          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                73436 (  71.71kB)
    Total RW  Size (RW Data + ZI Data)             50000 (  48.83kB)
    Total ROM Size (Code + RO Data + RW Data)      73600 (  71.88kB)

==============================================================================


Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to fmc.o(.text.MX_FMC_Init) for MX_FMC_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(.text.main) refers to dac.o(.text.MX_DAC_Init) for MX_DAC_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM6_Init) for MX_TIM6_Init
    main.o(.text.main) refers to adc.o(.text.MX_ADC1_Init) for MX_ADC1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to usart.o(.bss.huart2) for huart2
    main.o(.text.main) refers to my_usart.o(.bss..L_MergedGlobals) for rxTemp2
    main.o(.text.main) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(.text.main) refers to usart.o(.bss.huart3) for huart3
    main.o(.text.main) refers to cmd_to_fun.o(.text.CTRL_INIT) for CTRL_INIT
    main.o(.text.main) refers to adc_app.o(.text.adc_tim_dma_init) for adc_tim_dma_init
    main.o(.text.main) refers to da_output.o(.text.DA_Init) for DA_Init
    main.o(.text.main) refers to da_output.o(.text.DA_Apply_Settings) for DA_Apply_Settings
    main.o(.text.main) refers to dac_app.o(.text.dac_app_init) for dac_app_init
    main.o(.text.main) refers to dac_app.o(.text.dac_app_set_waveform) for dac_app_set_waveform
    main.o(.text.main) refers to app_pid.o(.text.PID_Init) for PID_Init
    main.o(.text.main) refers to my_fft.o(.text.fft_init) for fft_init
    main.o(.text.main) refers to software_dds.o(.text.DDS_Init) for DDS_Init
    main.o(.text.main) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    main.o(.text.main) refers to dds_test.o(.text.DDS_Test_Init) for DDS_Test_Init
    main.o(.text.main) refers to usart.o(.bss.huart1) for huart1
    main.o(.text.main) refers to my_usart.o(.text.my_printf) for my_printf
    main.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    main.o(.text.main) refers to my_usart.o(.text.process_uart1_command) for process_uart1_command
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for HAL_RCC_EnableCSS
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    adc.o(.text.MX_ADC1_Init) refers to adc.o(.bss.hadc1) for hadc1
    adc.o(.text.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(.text.MX_ADC1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(.ARM.exidx.text.MX_ADC1_Init) refers to adc.o(.text.MX_ADC1_Init) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(.text.HAL_ADC_MspInit) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(.text.HAL_ADC_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    dac.o(.text.MX_DAC_Init) refers to dac.o(.bss.hdac) for hdac
    dac.o(.text.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(.text.MX_DAC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    dac.o(.text.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(.ARM.exidx.text.MX_DAC_Init) refers to dac.o(.text.MX_DAC_Init) for [Anonymous Symbol]
    dac.o(.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(.text.HAL_DAC_MspInit) refers to dac.o(.bss.hdma_dac1) for hdma_dac1
    dac.o(.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    dac.o(.text.HAL_DAC_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    dac.o(.ARM.exidx.text.HAL_DAC_MspInit) refers to dac.o(.text.HAL_DAC_MspInit) for [Anonymous Symbol]
    dac.o(.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit) refers to dac.o(.text.HAL_DAC_MspDeInit) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    fmc.o(.text.MX_FMC_Init) refers to fmc.o(.bss.hsram2) for hsram2
    fmc.o(.text.MX_FMC_Init) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) for HAL_SRAM_Init
    fmc.o(.text.MX_FMC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    fmc.o(.ARM.exidx.text.MX_FMC_Init) refers to fmc.o(.text.MX_FMC_Init) for [Anonymous Symbol]
    fmc.o(.text.HAL_SRAM_MspInit) refers to fmc.o(.bss.FMC_Initialized) for FMC_Initialized
    fmc.o(.text.HAL_SRAM_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    fmc.o(.ARM.exidx.text.HAL_SRAM_MspInit) refers to fmc.o(.text.HAL_SRAM_MspInit) for [Anonymous Symbol]
    fmc.o(.text.HAL_SRAM_MspDeInit) refers to fmc.o(.bss.FMC_DeInitialized) for FMC_DeInitialized
    fmc.o(.text.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fmc.o(.ARM.exidx.text.HAL_SRAM_MspDeInit) refers to fmc.o(.text.HAL_SRAM_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM6_Init) refers to tim.o(.bss.htim6) for htim6
    tim.o(.text.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM6_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM6_Init) refers to tim.o(.text.MX_TIM6_Init) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_USART1_UART_Init) refers to my_usart.o(.bss.uart1_dma_buffer) for uart1_dma_buffer
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART3_UART_Init) refers to usart.o(.bss.huart3) for huart3
    usart.o(.text.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART3_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART3_UART_Init) refers to usart.o(.text.MX_USART3_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.NMI_Handler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for HAL_RCC_NMI_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to dac.o(.bss.hdma_dac1) for hdma_dac1
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.ADC_IRQHandler) refers to adc.o(.bss.hadc1) for hadc1
    stm32f4xx_it.o(.text.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.ADC_IRQHandler) refers to stm32f4xx_it.o(.text.ADC_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.huart2) for huart2
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to usart.o(.bss.huart3) for huart3
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream0_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    ad_measure.o(.ARM.exidx.text.findMinMax) refers to ad_measure.o(.text.findMinMax) for [Anonymous Symbol]
    ad_measure.o(.text.setSamplingFrequency) refers to cmd_to_fun.o(.text.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(.text.setSamplingFrequency) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad_measure.o(.ARM.exidx.text.setSamplingFrequency) refers to ad_measure.o(.text.setSamplingFrequency) for [Anonymous Symbol]
    ad_measure.o(.text.readFIFOData) refers to cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(.text.readFIFOData) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad_measure.o(.text.readFIFOData) refers to cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(.ARM.exidx.text.readFIFOData) refers to ad_measure.o(.text.readFIFOData) for [Anonymous Symbol]
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(.text.vpp_adc_parallel) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE) for AD_FIFO_WRITE_ENABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_DISABLE) for AD_FIFO_WRITE_DISABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data1_f) for fifo_data1_f
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data1) for fifo_data1
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data2_f) for fifo_data2_f
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data2) for fifo_data2
    ad_measure.o(.ARM.exidx.text.vpp_adc_parallel) refers to ad_measure.o(.text.vpp_adc_parallel) for [Anonymous Symbol]
    ad_measure.o(.text.ad_proc) refers to ad_measure.o(.text.vpp_adc_parallel) for vpp_adc_parallel
    ad_measure.o(.ARM.exidx.text.ad_proc) refers to ad_measure.o(.text.ad_proc) for [Anonymous Symbol]
    da_output.o(.text.DA_Init) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.text.DA_Init) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(.text.DA_Init) refers to ffixul.o(.text) for __aeabi_f2ulz
    da_output.o(.text.DA_Init) refers to uldiv.o(.text) for __aeabi_uldivmod
    da_output.o(.text.DA_Init) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(.text.DA_Init) refers to cmd_to_fun.o(.text.DA_FPGA_START) for DA_FPGA_START
    da_output.o(.ARM.exidx.text.DA_Init) refers to da_output.o(.text.DA_Init) for [Anonymous Symbol]
    da_output.o(.text.DA_SetConfig) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_SetConfig) refers to da_output.o(.text.DA_SetConfig) for [Anonymous Symbol]
    da_output.o(.text.DA_Apply_Settings) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(.text.DA_Apply_Settings) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.text.DA_Apply_Settings) refers to ffixul.o(.text) for __aeabi_f2ulz
    da_output.o(.text.DA_Apply_Settings) refers to uldiv.o(.text) for __aeabi_uldivmod
    da_output.o(.text.DA_Apply_Settings) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(.text.DA_Apply_Settings) refers to cmd_to_fun.o(.text.DA_FPGA_START) for DA_FPGA_START
    da_output.o(.ARM.exidx.text.DA_Apply_Settings) refers to da_output.o(.text.DA_Apply_Settings) for [Anonymous Symbol]
    da_output.o(.text.wave_test) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    da_output.o(.text.wave_test) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.text.wave_test) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(.text.wave_test) refers to ffixul.o(.text) for __aeabi_f2ulz
    da_output.o(.text.wave_test) refers to uldiv.o(.text) for __aeabi_uldivmod
    da_output.o(.text.wave_test) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(.text.wave_test) refers to cmd_to_fun.o(.text.DA_FPGA_START) for DA_FPGA_START
    da_output.o(.ARM.exidx.text.wave_test) refers to da_output.o(.text.wave_test) for [Anonymous Symbol]
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_ENABLE) for AD_FREQ_CLR_ENABLE
    freq_measure.o(.text.fre_measure) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_DISABLE) for AD_FREQ_CLR_DISABLE
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_START) for AD_FREQ_START
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_STOP) for AD_FREQ_STOP
    freq_measure.o(.ARM.exidx.text.fre_measure) refers to freq_measure.o(.text.fre_measure) for [Anonymous Symbol]
    freq_measure.o(.text.fre_measure_ad1) refers to freq_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    freq_measure.o(.text.fre_measure_ad1) refers to freq_measure.o(.text.fre_measure) for fre_measure
    freq_measure.o(.ARM.exidx.text.fre_measure_ad1) refers to freq_measure.o(.text.fre_measure_ad1) for [Anonymous Symbol]
    freq_measure.o(.text.fre_measure_ad2) refers to freq_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    freq_measure.o(.text.fre_measure_ad2) refers to freq_measure.o(.text.fre_measure) for fre_measure
    freq_measure.o(.ARM.exidx.text.fre_measure_ad2) refers to freq_measure.o(.text.fre_measure_ad2) for [Anonymous Symbol]
    freq_measure.o(.text.freq_proc) refers to freq_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    freq_measure.o(.text.freq_proc) refers to freq_measure.o(.text.fre_measure) for fre_measure
    freq_measure.o(.text.freq_proc) refers to f2d.o(.text) for __aeabi_f2d
    freq_measure.o(.text.freq_proc) refers to usart.o(.bss.huart1) for huart1
    freq_measure.o(.text.freq_proc) refers to my_usart.o(.text.my_printf) for my_printf
    freq_measure.o(.ARM.exidx.text.freq_proc) refers to freq_measure.o(.text.freq_proc) for [Anonymous Symbol]
    key_app.o(.text.key_read) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(.ARM.exidx.text.key_read) refers to key_app.o(.text.key_read) for [Anonymous Symbol]
    key_app.o(.text.set_current_ad_frequency) refers to key_app.o(.data.current_ad_freq) for current_ad_freq
    key_app.o(.ARM.exidx.text.set_current_ad_frequency) refers to key_app.o(.text.set_current_ad_frequency) for [Anonymous Symbol]
    key_app.o(.text.get_current_ad_frequency) refers to key_app.o(.data.current_ad_freq) for current_ad_freq
    key_app.o(.ARM.exidx.text.get_current_ad_frequency) refers to key_app.o(.text.get_current_ad_frequency) for [Anonymous Symbol]
    key_app.o(.text.key_proc) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(.text.key_proc) refers to key_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    key_app.o(.text.key_proc) refers to dac_app.o(.text.dac_app_set_waveform) for dac_app_set_waveform
    key_app.o(.text.key_proc) refers to usart.o(.bss.huart1) for huart1
    key_app.o(.text.key_proc) refers to key_app.o(.rodata.str1.1) for .L.str.1
    key_app.o(.text.key_proc) refers to my_usart.o(.text.my_printf) for my_printf
    key_app.o(.text.key_proc) refers to da_output.o(.bss..L_MergedGlobals) for da_channels
    key_app.o(.text.key_proc) refers to da_output.o(.text.DA_Apply_Settings) for DA_Apply_Settings
    key_app.o(.text.key_proc) refers to ad_measure.o(.bss.fifo_data1_f) for fifo_data1_f
    key_app.o(.text.key_proc) refers to my_fft.o(.text.calculate_fft_spectrum) for calculate_fft_spectrum
    key_app.o(.text.key_proc) refers to my_fft.o(.text.output_fft_spectrum) for output_fft_spectrum
    key_app.o(.ARM.exidx.text.key_proc) refers to key_app.o(.text.key_proc) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9833.o(.text.AD9833_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(.ARM.exidx.text.AD9833_Init) refers to ad9833.o(.text.AD9833_Init) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_WriteData) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(.ARM.exidx.text.AD9833_WriteData) refers to ad9833.o(.text.AD9833_WriteData) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_SetFrequency) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(.text.AD9833_SetFrequency) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(.text.AD9833_SetFrequency) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(.text.AD9833_SetFrequency) refers to ad9833.o(.text.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(.ARM.exidx.text.AD9833_SetFrequency) refers to ad9833.o(.text.AD9833_SetFrequency) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_SetPhase) refers to ad9833.o(.text.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(.ARM.exidx.text.AD9833_SetPhase) refers to ad9833.o(.text.AD9833_SetPhase) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_SetWave) refers to ad9833.o(.text.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(.ARM.exidx.text.AD9833_SetWave) refers to ad9833.o(.text.AD9833_SetWave) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_Setup) refers to ad9833.o(.text.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(.text.AD9833_Setup) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(.text.AD9833_Setup) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(.text.AD9833_Setup) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(.ARM.exidx.text.AD9833_Setup) refers to ad9833.o(.text.AD9833_Setup) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_Init2) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9833.o(.text.AD9833_Init2) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(.ARM.exidx.text.AD9833_Init2) refers to ad9833.o(.text.AD9833_Init2) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_WriteData2) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(.ARM.exidx.text.AD9833_WriteData2) refers to ad9833.o(.text.AD9833_WriteData2) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_SetFrequency2) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(.text.AD9833_SetFrequency2) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(.text.AD9833_SetFrequency2) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(.text.AD9833_SetFrequency2) refers to ad9833.o(.text.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(.ARM.exidx.text.AD9833_SetFrequency2) refers to ad9833.o(.text.AD9833_SetFrequency2) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_SetPhase2) refers to ad9833.o(.text.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(.ARM.exidx.text.AD9833_SetPhase2) refers to ad9833.o(.text.AD9833_SetPhase2) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_SetWave2) refers to ad9833.o(.text.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(.ARM.exidx.text.AD9833_SetWave2) refers to ad9833.o(.text.AD9833_SetWave2) for [Anonymous Symbol]
    ad9833.o(.text.AD9833_Setup2) refers to ad9833.o(.text.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(.text.AD9833_Setup2) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(.text.AD9833_Setup2) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(.text.AD9833_Setup2) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(.ARM.exidx.text.AD9833_Setup2) refers to ad9833.o(.text.AD9833_Setup2) for [Anonymous Symbol]
    adc_app.o(.text.adc_tim_dma_init) refers to tim.o(.bss.htim3) for htim3
    adc_app.o(.text.adc_tim_dma_init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    adc_app.o(.text.adc_tim_dma_init) refers to adc.o(.bss.hadc1) for hadc1
    adc_app.o(.text.adc_tim_dma_init) refers to adc_app.o(.bss.adc_val_buffer) for adc_val_buffer
    adc_app.o(.text.adc_tim_dma_init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(.text.adc_tim_dma_init) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    adc_app.o(.ARM.exidx.text.adc_tim_dma_init) refers to adc_app.o(.text.adc_tim_dma_init) for [Anonymous Symbol]
    adc_app.o(.text.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss.hadc1) for hadc1
    adc_app.o(.text.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc_app.o(.text.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.bss.AdcConvEnd) for AdcConvEnd
    adc_app.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.text.HAL_ADC_ConvCpltCallback) for [Anonymous Symbol]
    adc_app.o(.text.adc_task) refers to adc_app.o(.bss.AdcConvEnd) for AdcConvEnd
    adc_app.o(.text.adc_task) refers to adc_app.o(.bss.adc_val_buffer) for adc_val_buffer
    adc_app.o(.text.adc_task) refers to adc_app.o(.bss.dac_val_buffer) for dac_val_buffer
    adc_app.o(.text.adc_task) refers to usart.o(.bss.huart1) for huart1
    adc_app.o(.text.adc_task) refers to my_usart.o(.text.my_printf) for my_printf
    adc_app.o(.text.adc_task) refers to memseta.o(.text) for __aeabi_memclr4
    adc_app.o(.text.adc_task) refers to adc.o(.bss.hadc1) for hadc1
    adc_app.o(.text.adc_task) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(.text.adc_task) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    adc_app.o(.ARM.exidx.text.adc_task) refers to adc_app.o(.text.adc_task) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_init) refers to dac_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dac_app.o(.text.dac_app_init) refers to dac.o(.bss.hdac) for hdac
    dac_app.o(.text.dac_app_init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(.text.dac_app_init) refers to tim.o(.bss.htim6) for htim6
    dac_app.o(.text.dac_app_init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(.text.dac_app_init) refers to dac_app.o(.text.generate_waveform) for generate_waveform
    dac_app.o(.text.dac_app_init) refers to dac_app.o(.text.start_dac_dma) for start_dac_dma
    dac_app.o(.ARM.exidx.text.dac_app_init) refers to dac_app.o(.text.dac_app_init) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_set_amplitude) refers to dac.o(.bss.hdac) for hdac
    dac_app.o(.text.dac_app_set_amplitude) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(.text.dac_app_set_amplitude) refers to tim.o(.bss.htim6) for htim6
    dac_app.o(.text.dac_app_set_amplitude) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(.text.dac_app_set_amplitude) refers to dac_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dac_app.o(.text.dac_app_set_amplitude) refers to dac_app.o(.text.generate_waveform) for generate_waveform
    dac_app.o(.text.dac_app_set_amplitude) refers to dac_app.o(.text.start_dac_dma) for start_dac_dma
    dac_app.o(.ARM.exidx.text.dac_app_set_amplitude) refers to dac_app.o(.text.dac_app_set_amplitude) for [Anonymous Symbol]
    dac_app.o(.text.generate_waveform) refers to dac_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    dac_app.o(.text.generate_waveform) refers to dac_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dac_app.o(.text.generate_waveform) refers to dac_app.o(.bss.waveform_buffer) for waveform_buffer
    dac_app.o(.text.generate_waveform) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    dac_app.o(.text.generate_waveform) refers to memseta.o(.text) for __aeabi_memclr4
    dac_app.o(.ARM.exidx.text.generate_waveform) refers to dac_app.o(.text.generate_waveform) for [Anonymous Symbol]
    dac_app.o(.text.start_dac_dma) refers to dac_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dac_app.o(.text.start_dac_dma) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(.text.start_dac_dma) refers to uldiv.o(.text) for __aeabi_uldivmod
    dac_app.o(.text.start_dac_dma) refers to tim.o(.bss.htim6) for htim6
    dac_app.o(.text.start_dac_dma) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(.text.start_dac_dma) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for HAL_TIM_GenerateEvent
    dac_app.o(.text.start_dac_dma) refers to dac.o(.bss.hdac) for hdac
    dac_app.o(.text.start_dac_dma) refers to dac_app.o(.bss.waveform_buffer) for waveform_buffer
    dac_app.o(.text.start_dac_dma) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) for HAL_DAC_Start_DMA
    dac_app.o(.text.start_dac_dma) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    dac_app.o(.ARM.exidx.text.start_dac_dma) refers to dac_app.o(.text.start_dac_dma) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_set_waveform) refers to dac.o(.bss.hdac) for hdac
    dac_app.o(.text.dac_app_set_waveform) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(.text.dac_app_set_waveform) refers to tim.o(.bss.htim6) for htim6
    dac_app.o(.text.dac_app_set_waveform) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(.text.dac_app_set_waveform) refers to dac_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    dac_app.o(.text.dac_app_set_waveform) refers to dac_app.o(.text.generate_waveform) for generate_waveform
    dac_app.o(.text.dac_app_set_waveform) refers to dac_app.o(.text.start_dac_dma) for start_dac_dma
    dac_app.o(.ARM.exidx.text.dac_app_set_waveform) refers to dac_app.o(.text.dac_app_set_waveform) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_set_frequency) refers to dac.o(.bss.hdac) for hdac
    dac_app.o(.text.dac_app_set_frequency) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(.text.dac_app_set_frequency) refers to tim.o(.bss.htim6) for htim6
    dac_app.o(.text.dac_app_set_frequency) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(.text.dac_app_set_frequency) refers to dac_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dac_app.o(.text.dac_app_set_frequency) refers to dac_app.o(.text.start_dac_dma) for start_dac_dma
    dac_app.o(.ARM.exidx.text.dac_app_set_frequency) refers to dac_app.o(.text.dac_app_set_frequency) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_get_amplitude) refers to dac_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dac_app.o(.ARM.exidx.text.dac_app_get_amplitude) refers to dac_app.o(.text.dac_app_get_amplitude) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_set_zero_based) refers to dac.o(.bss.hdac) for hdac
    dac_app.o(.text.dac_app_set_zero_based) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(.text.dac_app_set_zero_based) refers to tim.o(.bss.htim6) for htim6
    dac_app.o(.text.dac_app_set_zero_based) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(.text.dac_app_set_zero_based) refers to dac_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    dac_app.o(.text.dac_app_set_zero_based) refers to dac_app.o(.text.generate_waveform) for generate_waveform
    dac_app.o(.text.dac_app_set_zero_based) refers to dac_app.o(.text.start_dac_dma) for start_dac_dma
    dac_app.o(.ARM.exidx.text.dac_app_set_zero_based) refers to dac_app.o(.text.dac_app_set_zero_based) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_get_zero_based) refers to dac_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    dac_app.o(.ARM.exidx.text.dac_app_get_zero_based) refers to dac_app.o(.text.dac_app_get_zero_based) for [Anonymous Symbol]
    dac_app.o(.text.dac_app_get_actual_frequency) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(.text.dac_app_get_actual_frequency) refers to tim.o(.bss.htim6) for htim6
    dac_app.o(.ARM.exidx.text.dac_app_get_actual_frequency) refers to dac_app.o(.text.dac_app_get_actual_frequency) for [Anonymous Symbol]
    software_dds.o(.text.DDS_Init) refers to software_dds.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    software_dds.o(.text.DDS_Init) refers to software_dds.o(.bss.sine_table) for sine_table
    software_dds.o(.text.DDS_Init) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    software_dds.o(.text.DDS_Init) refers to dac.o(.bss.hdac) for hdac
    software_dds.o(.text.DDS_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start) for HAL_DAC_Start
    software_dds.o(.text.DDS_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(.text.DDS_Init) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.text.DDS_Init) refers to uldiv.o(.text) for __aeabi_uldivmod
    software_dds.o(.text.DDS_Init) refers to usart.o(.bss.huart2) for huart2
    software_dds.o(.text.DDS_Init) refers to my_usart.o(.text.my_printf) for my_printf
    software_dds.o(.ARM.exidx.text.DDS_Init) refers to software_dds.o(.text.DDS_Init) for [Anonymous Symbol]
    software_dds.o(.text.DDS_CalculatePhaseIncrement) refers to uldiv.o(.text) for __aeabi_uldivmod
    software_dds.o(.text.DDS_CalculatePhaseIncrement) refers to software_dds.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    software_dds.o(.ARM.exidx.text.DDS_CalculatePhaseIncrement) refers to software_dds.o(.text.DDS_CalculatePhaseIncrement) for [Anonymous Symbol]
    software_dds.o(.text.DDS_Start) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.text.DDS_Start) refers to software_dds.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    software_dds.o(.text.DDS_Start) refers to tim.o(.bss.htim6) for htim6
    software_dds.o(.text.DDS_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    software_dds.o(.text.DDS_Start) refers to usart.o(.bss.huart2) for huart2
    software_dds.o(.text.DDS_Start) refers to my_usart.o(.text.my_printf) for my_printf
    software_dds.o(.ARM.exidx.text.DDS_Start) refers to software_dds.o(.text.DDS_Start) for [Anonymous Symbol]
    software_dds.o(.text.DDS_Stop) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.text.DDS_Stop) refers to tim.o(.bss.htim6) for htim6
    software_dds.o(.text.DDS_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for HAL_TIM_Base_Stop_IT
    software_dds.o(.text.DDS_Stop) refers to dac.o(.bss.hdac) for hdac
    software_dds.o(.text.DDS_Stop) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(.text.DDS_Stop) refers to usart.o(.bss.huart2) for huart2
    software_dds.o(.text.DDS_Stop) refers to my_usart.o(.text.my_printf) for my_printf
    software_dds.o(.ARM.exidx.text.DDS_Stop) refers to software_dds.o(.text.DDS_Stop) for [Anonymous Symbol]
    software_dds.o(.text.DDS_SetFrequency) refers to usart.o(.bss.huart2) for huart2
    software_dds.o(.text.DDS_SetFrequency) refers to software_dds.o(.rodata.str1.1) for .L.str.4
    software_dds.o(.text.DDS_SetFrequency) refers to my_usart.o(.text.my_printf) for my_printf
    software_dds.o(.text.DDS_SetFrequency) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.text.DDS_SetFrequency) refers to uldiv.o(.text) for __aeabi_uldivmod
    software_dds.o(.text.DDS_SetFrequency) refers to software_dds.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    software_dds.o(.ARM.exidx.text.DDS_SetFrequency) refers to software_dds.o(.text.DDS_SetFrequency) for [Anonymous Symbol]
    software_dds.o(.text.DDS_SetAmplitude) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.text.DDS_SetAmplitude) refers to usart.o(.bss.huart2) for huart2
    software_dds.o(.text.DDS_SetAmplitude) refers to software_dds.o(.rodata.str1.1) for .L.str.6
    software_dds.o(.text.DDS_SetAmplitude) refers to my_usart.o(.text.my_printf) for my_printf
    software_dds.o(.ARM.exidx.text.DDS_SetAmplitude) refers to software_dds.o(.text.DDS_SetAmplitude) for [Anonymous Symbol]
    software_dds.o(.text.DDS_SetPhase) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.ARM.exidx.text.DDS_SetPhase) refers to software_dds.o(.text.DDS_SetPhase) for [Anonymous Symbol]
    software_dds.o(.text.DDS_SetWaveform) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.ARM.exidx.text.DDS_SetWaveform) refers to software_dds.o(.text.DDS_SetWaveform) for [Anonymous Symbol]
    software_dds.o(.text.DDS_Setup) refers to usart.o(.bss.huart2) for huart2
    software_dds.o(.text.DDS_Setup) refers to software_dds.o(.rodata.str1.1) for .L.str.4
    software_dds.o(.text.DDS_Setup) refers to my_usart.o(.text.my_printf) for my_printf
    software_dds.o(.text.DDS_Setup) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.text.DDS_Setup) refers to uldiv.o(.text) for __aeabi_uldivmod
    software_dds.o(.text.DDS_Setup) refers to software_dds.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    software_dds.o(.ARM.exidx.text.DDS_Setup) refers to software_dds.o(.text.DDS_Setup) for [Anonymous Symbol]
    software_dds.o(.text.DDS_GetConfig) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.ARM.exidx.text.DDS_GetConfig) refers to software_dds.o(.text.DDS_GetConfig) for [Anonymous Symbol]
    software_dds.o(.text.DDS_GetInterruptStats) refers to software_dds.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    software_dds.o(.ARM.exidx.text.DDS_GetInterruptStats) refers to software_dds.o(.text.DDS_GetInterruptStats) for [Anonymous Symbol]
    software_dds.o(.text.DDS_OutputSample) refers to software_dds.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    software_dds.o(.text.DDS_OutputSample) refers to software_dds.o(.data.dds_config) for dds_config
    software_dds.o(.text.DDS_OutputSample) refers to software_dds.o(.bss.sine_table) for sine_table
    software_dds.o(.text.DDS_OutputSample) refers to dac.o(.bss.hdac) for hdac
    software_dds.o(.text.DDS_OutputSample) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(.ARM.exidx.text.DDS_OutputSample) refers to software_dds.o(.text.DDS_OutputSample) for [Anonymous Symbol]
    my_fft.o(.text.generate_hanning_window) refers to my_fft.o(.bss.window_buffer) for window_buffer
    my_fft.o(.text.generate_hanning_window) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(.ARM.exidx.text.generate_hanning_window) refers to my_fft.o(.text.generate_hanning_window) for [Anonymous Symbol]
    my_fft.o(.text.fft_init) refers to my_fft.o(.bss.fft_instance) for fft_instance
    my_fft.o(.text.fft_init) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    my_fft.o(.text.fft_init) refers to my_fft.o(.bss.window_buffer) for window_buffer
    my_fft.o(.text.fft_init) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(.ARM.exidx.text.fft_init) refers to my_fft.o(.text.fft_init) for [Anonymous Symbol]
    my_fft.o(.text.calculate_fft_spectrum) refers to my_fft.o(.bss.fft_input_buffer) for fft_input_buffer
    my_fft.o(.text.calculate_fft_spectrum) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(.text.calculate_fft_spectrum) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.text.calculate_fft_spectrum) refers to my_fft.o(.bss.fft_instance) for fft_instance
    my_fft.o(.text.calculate_fft_spectrum) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    my_fft.o(.text.calculate_fft_spectrum) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    my_fft.o(.ARM.exidx.text.calculate_fft_spectrum) refers to my_fft.o(.text.calculate_fft_spectrum) for [Anonymous Symbol]
    my_fft.o(.text.output_fft_spectrum) refers to key_app.o(.text.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(.text.output_fft_spectrum) refers to usart.o(.bss.huart1) for huart1
    my_fft.o(.text.output_fft_spectrum) refers to my_usart.o(.text.my_printf) for my_printf
    my_fft.o(.text.output_fft_spectrum) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.rodata.str1.1) for .L.str.5
    my_fft.o(.text.output_fft_spectrum) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.text.calculate_thd) for calculate_thd
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.text.calculate_sinad) for calculate_sinad
    my_fft.o(.ARM.exidx.text.output_fft_spectrum) refers to my_fft.o(.text.output_fft_spectrum) for [Anonymous Symbol]
    my_fft.o(.text.get_precise_peak_frequency) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.ARM.exidx.text.get_precise_peak_frequency) refers to my_fft.o(.text.get_precise_peak_frequency) for [Anonymous Symbol]
    my_fft.o(.text.round_to_nearest_k) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_fft.o(.ARM.exidx.text.round_to_nearest_k) refers to my_fft.o(.text.round_to_nearest_k) for [Anonymous Symbol]
    my_fft.o(.text.calculate_thd) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.ARM.exidx.text.calculate_thd) refers to my_fft.o(.text.calculate_thd) for [Anonymous Symbol]
    my_fft.o(.text.calculate_thd_n) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.ARM.exidx.text.calculate_thd_n) refers to my_fft.o(.text.calculate_thd_n) for [Anonymous Symbol]
    my_fft.o(.text.calculate_sinad) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.text.calculate_sinad) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    my_fft.o(.ARM.exidx.text.calculate_sinad) refers to my_fft.o(.text.calculate_sinad) for [Anonymous Symbol]
    my_filter.o(.text.arm_fir_f32_lp) refers to my_filter.o(.rodata.B) for B
    my_filter.o(.text.arm_fir_f32_lp) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for arm_fir_init_f32
    my_filter.o(.ARM.exidx.text.arm_fir_f32_lp) refers to my_filter.o(.text.arm_fir_f32_lp) for [Anonymous Symbol]
    phase_measure.o(.text.calculate_phase_diff) refers to phase_measure.o(.bss.phase_diff) for phase_diff
    phase_measure.o(.ARM.exidx.text.calculate_phase_diff) refers to phase_measure.o(.text.calculate_phase_diff) for [Anonymous Symbol]
    kalman.o(.ARM.exidx.text.Kalman_init) refers to kalman.o(.text.Kalman_init) for [Anonymous Symbol]
    kalman.o(.ARM.exidx.text.kalman_filter) refers to kalman.o(.text.kalman_filter) for [Anonymous Symbol]
    kalman.o(.text.kalman) refers to kalman.o(.bss.current) for current
    kalman.o(.text.kalman) refers to kalman.o(.data.last) for last
    kalman.o(.text.kalman) refers to kalman.o(.bss.state) for state
    kalman.o(.ARM.exidx.text.kalman) refers to kalman.o(.text.kalman) for [Anonymous Symbol]
    kalman.o(.text.kalman_thd) refers to kalman.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    kalman.o(.text.kalman_thd) refers to kalman.o(.data.last1) for last1
    kalman.o(.ARM.exidx.text.kalman_thd) refers to kalman.o(.text.kalman_thd) for [Anonymous Symbol]
    wave_recognition.o(.text.find_peaks) refers to f2d.o(.text) for __aeabi_f2d
    wave_recognition.o(.text.find_peaks) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(.text.find_peaks) refers to d2f.o(.text) for __aeabi_d2f
    wave_recognition.o(.ARM.exidx.text.find_peaks) refers to wave_recognition.o(.text.find_peaks) for [Anonymous Symbol]
    wave_recognition.o(.ARM.exidx.text.calculate_bandwidth) refers to wave_recognition.o(.text.calculate_bandwidth) for [Anonymous Symbol]
    wave_recognition.o(.text.calculate_carrier_suppression) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(.text.calculate_carrier_suppression) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(.text.calculate_carrier_suppression) refers to dfixi.o(.text) for __aeabi_d2iz
    wave_recognition.o(.ARM.exidx.text.calculate_carrier_suppression) refers to wave_recognition.o(.text.calculate_carrier_suppression) for [Anonymous Symbol]
    wave_recognition.o(.text.detect_symmetry) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(.text.detect_symmetry) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(.text.detect_symmetry) refers to dfixi.o(.text) for __aeabi_d2iz
    wave_recognition.o(.ARM.exidx.text.detect_symmetry) refers to wave_recognition.o(.text.detect_symmetry) for [Anonymous Symbol]
    wave_recognition.o(.text.recognize_waveform) refers to wave_recognition.o(.text.find_peaks) for find_peaks
    wave_recognition.o(.text.recognize_waveform) refers to f2d.o(.text) for __aeabi_f2d
    wave_recognition.o(.text.recognize_waveform) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(.text.recognize_waveform) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(.text.recognize_waveform) refers to dfixi.o(.text) for __aeabi_d2iz
    wave_recognition.o(.text.recognize_waveform) refers to dcmpge.o(.text) for __aeabi_dcmpge
    wave_recognition.o(.text.recognize_waveform) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    wave_recognition.o(.text.recognize_waveform) refers to dcmple.o(.text) for __aeabi_dcmple
    wave_recognition.o(.text.recognize_waveform) refers to wave_recognition.o(.rodata.str1.1) for .L.str.1
    wave_recognition.o(.text.recognize_waveform) refers to printfa.o(i.__0sprintf) for __2sprintf
    wave_recognition.o(.ARM.exidx.text.recognize_waveform) refers to wave_recognition.o(.text.recognize_waveform) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Send_String) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Send_String) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Send_String) refers to my_hmi.o(.text.HMI_Send_String) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Send_Int) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Send_Int) refers to my_hmi.o(.rodata.str1.1) for .L.str.1
    my_hmi.o(.text.HMI_Send_Int) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Send_Int) refers to my_hmi.o(.text.HMI_Send_Int) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Send_Float) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Send_Float) refers to f2d.o(.text) for __aeabi_f2d
    my_hmi.o(.text.HMI_Send_Float) refers to dflti.o(.text) for __aeabi_i2d
    my_hmi.o(.text.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(.text.HMI_Send_Float) refers to dmul.o(.text) for __aeabi_dmul
    my_hmi.o(.text.HMI_Send_Float) refers to dfixi.o(.text) for __aeabi_d2iz
    my_hmi.o(.text.HMI_Send_Float) refers to my_hmi.o(.rodata.str1.1) for .L.str.1
    my_hmi.o(.text.HMI_Send_Float) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Send_Float) refers to my_hmi.o(.text.HMI_Send_Float) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Wave_Clear) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Wave_Clear) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Wave_Clear) refers to my_hmi.o(.text.HMI_Wave_Clear) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Write_Wave_Low) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Write_Wave_Low) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Low) refers to my_hmi.o(.text.HMI_Write_Wave_Low) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Write_Wave_Fast) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Write_Wave_Fast) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.text.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Fast) refers to my_hmi.o(.text.HMI_Write_Wave_Fast) for [Anonymous Symbol]
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.bss.uart1_cmd_buffer) for uart1_cmd_buffer
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.bss.uart1_dma_buffer) for uart1_dma_buffer
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.bss.uart1_cmd_flag) for uart1_cmd_flag
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.huart1) for huart1
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_usart.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    my_usart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    my_usart.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_usart.o(.text.my_printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(.ARM.exidx.text.my_printf) refers to my_usart.o(.text.my_printf) for [Anonymous Symbol]
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss.rxBuffer3) for rxBuffer3
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to usart.o(.bss.huart3) for huart3
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to usart.o(.bss.huart2) for huart2
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss.rxBuffer2) for rxBuffer2
    my_usart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    my_usart.o(.text.process_uart1_command) refers to my_usart.o(.bss.uart1_cmd_flag) for uart1_cmd_flag
    my_usart.o(.text.process_uart1_command) refers to my_usart.o(.bss.uart1_cmd_buffer) for uart1_cmd_buffer
    my_usart.o(.text.process_uart1_command) refers to usart.o(.bss.huart1) for huart1
    my_usart.o(.text.process_uart1_command) refers to my_usart.o(.text.my_printf) for my_printf
    my_usart.o(.text.process_uart1_command) refers to my_usart.o(.rodata.str1.1) for .L.str.2
    my_usart.o(.text.process_uart1_command) refers to memcmp.o(.text) for memcmp
    my_usart.o(.text.process_uart1_command) refers to memseta.o(.text) for __aeabi_memclr4
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_FreqPlus) for DDS_Test_FreqPlus
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_FreqMinus) for DDS_Test_FreqMinus
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_Start) for DDS_Test_Start
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_Stop) for DDS_Test_Stop
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_SetMode) for DDS_Test_SetMode
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_ShowDebugInfo) for DDS_Test_ShowDebugInfo
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_TestDAC) for DDS_Test_TestDAC
    my_usart.o(.text.process_uart1_command) refers to atoi.o(.text) for atoi
    my_usart.o(.text.process_uart1_command) refers to dds_test.o(.text.DDS_Test_SetAmplitude) for DDS_Test_SetAmplitude
    my_usart.o(.ARM.exidx.text.process_uart1_command) refers to my_usart.o(.text.process_uart1_command) for [Anonymous Symbol]
    my_usart.o(.text.process_uart2_command) refers to my_usart.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart.o(.text.process_uart2_command) refers to my_usart.o(.bss.rxBuffer2) for rxBuffer2
    my_usart.o(.text.process_uart2_command) refers to usart.o(.bss.huart2) for huart2
    my_usart.o(.text.process_uart2_command) refers to my_usart.o(.text.my_printf) for my_printf
    my_usart.o(.text.process_uart2_command) refers to my_usart.o(.rodata.str1.1) for .L.str.20
    my_usart.o(.text.process_uart2_command) refers to strstr.o(.text) for strstr
    my_usart.o(.text.process_uart2_command) refers to scheduler.o(.text.start_circuit_learning) for start_circuit_learning
    my_usart.o(.text.process_uart2_command) refers to memseta.o(.text) for __aeabi_memclr4
    my_usart.o(.text.process_uart2_command) refers to dds_test.o(.text.DDS_Test_ProcessCommand) for DDS_Test_ProcessCommand
    my_usart.o(.ARM.exidx.text.process_uart2_command) refers to my_usart.o(.text.process_uart2_command) for [Anonymous Symbol]
    my_usart_pack.o(.text.SetParseTemplate) refers to my_usart_pack.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart_pack.o(.text.SetParseTemplate) refers to memcpya.o(.text) for __aeabi_memcpy
    my_usart_pack.o(.ARM.exidx.text.SetParseTemplate) refers to my_usart_pack.o(.text.SetParseTemplate) for [Anonymous Symbol]
    my_usart_pack.o(.text.ParseFrame) refers to my_usart_pack.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart_pack.o(.ARM.exidx.text.ParseFrame) refers to my_usart_pack.o(.text.ParseFrame) for [Anonymous Symbol]
    my_usart_pack.o(.text.PrepareFrame) refers to my_usart_pack.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart_pack.o(.ARM.exidx.text.PrepareFrame) refers to my_usart_pack.o(.text.PrepareFrame) for [Anonymous Symbol]
    my_usart_pack.o(.text.SendFrame) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(.ARM.exidx.text.SendFrame) refers to my_usart_pack.o(.text.SendFrame) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.CTRL_INIT) refers to cmd_to_fun.o(.text.CTRL_INIT) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_START) refers to cmd_to_fun.o(.text.DA_FPGA_START) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_STOP) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_ENABLE) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_ENABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_DISABLE) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_DISABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_START) refers to cmd_to_fun.o(.text.AD_FREQ_START) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_STOP) refers to cmd_to_fun.o(.text.AD_FREQ_STOP) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_SET) refers to cmd_to_fun.o(.text.AD_FREQ_SET) for [Anonymous Symbol]
    cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE) refers to cmd_to_fun.o(.rodata..Lswitch.table.AD_FIFO_WRITE_ENABLE) for .Lswitch.table.AD_FIFO_WRITE_ENABLE
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_ENABLE) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_DISABLE) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_DISABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_ENABLE) refers to cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_DISABLE) refers to cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE) for [Anonymous Symbol]
    scheduler.o(.text.uart_proc) refers to ad_measure.o(.bss..L_MergedGlobals) for vol_amp2
    scheduler.o(.text.uart_proc) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(.text.uart_proc) refers to app_pid.o(.data.output) for output
    scheduler.o(.text.uart_proc) refers to usart.o(.bss.huart1) for huart1
    scheduler.o(.text.uart_proc) refers to my_usart.o(.text.my_printf) for my_printf
    scheduler.o(.ARM.exidx.text.uart_proc) refers to scheduler.o(.text.uart_proc) for [Anonymous Symbol]
    scheduler.o(.text.circuit_learning_task) refers to scheduler.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    scheduler.o(.text.circuit_learning_task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.text.circuit_learning_task) refers to scheduler.o(.data.current_freq) for current_freq
    scheduler.o(.text.circuit_learning_task) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    scheduler.o(.text.circuit_learning_task) refers to usart.o(.bss.huart2) for huart2
    scheduler.o(.text.circuit_learning_task) refers to my_usart.o(.text.my_printf) for my_printf
    scheduler.o(.text.circuit_learning_task) refers to scheduler.o(.bss.spectrum_buffer) for spectrum_buffer
    scheduler.o(.text.circuit_learning_task) refers to memseta.o(.text) for __aeabi_memclr4
    scheduler.o(.text.circuit_learning_task) refers to wave_recognition.o(.text.recognize_waveform) for recognize_waveform
    scheduler.o(.text.circuit_learning_task) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(.ARM.exidx.text.circuit_learning_task) refers to scheduler.o(.text.circuit_learning_task) for [Anonymous Symbol]
    scheduler.o(.text.start_circuit_learning) refers to scheduler.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    scheduler.o(.text.start_circuit_learning) refers to scheduler.o(.data.current_freq) for current_freq
    scheduler.o(.text.start_circuit_learning) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.text.start_circuit_learning) refers to software_dds.o(.text.DDS_Start) for DDS_Start
    scheduler.o(.text.start_circuit_learning) refers to usart.o(.bss.huart2) for huart2
    scheduler.o(.text.start_circuit_learning) refers to my_usart.o(.text.my_printf) for my_printf
    scheduler.o(.ARM.exidx.text.start_circuit_learning) refers to scheduler.o(.text.start_circuit_learning) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for scheduler_task
    scheduler.o(.text.scheduler_run) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to key_app.o(.text.key_proc) for key_proc
    scheduler.o(.data.scheduler_task) refers to scheduler.o(.text.circuit_learning_task) for circuit_learning_task
    scheduler.o(.data.scheduler_task) refers to my_usart.o(.text.process_uart2_command) for process_uart2_command
    app_pid.o(.text.PID_Init) refers to app_pid.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    app_pid.o(.ARM.exidx.text.PID_Init) refers to app_pid.o(.text.PID_Init) for [Anonymous Symbol]
    app_pid.o(.ARM.exidx.text.increment_pid_ctrl) refers to app_pid.o(.text.increment_pid_ctrl) for [Anonymous Symbol]
    app_pid.o(.text.Pid_Proc) refers to ad_measure.o(.bss..L_MergedGlobals) for vol_amp2
    app_pid.o(.text.Pid_Proc) refers to app_pid.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    app_pid.o(.text.Pid_Proc) refers to app_pid.o(.data.output) for output
    app_pid.o(.ARM.exidx.text.Pid_Proc) refers to app_pid.o(.text.Pid_Proc) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_Init) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_Init) refers to dds_test.o(.rodata.str1.1) for .L.str
    dds_test.o(.text.DDS_Test_Init) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_Init) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.ARM.exidx.text.DDS_Test_Init) refers to dds_test.o(.text.DDS_Test_Init) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_ShowStatus) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_ShowStatus) refers to dds_test.o(.rodata.str1.1) for .L.str.28
    dds_test.o(.text.DDS_Test_ShowStatus) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_ShowStatus) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_ShowStatus) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_ShowStatus) refers to f2d.o(.text) for __aeabi_f2d
    dds_test.o(.text.DDS_Test_ShowStatus) refers to software_dds.o(.text.DDS_GetConfig) for DDS_GetConfig
    dds_test.o(.ARM.exidx.text.DDS_Test_ShowStatus) refers to dds_test.o(.text.DDS_Test_ShowStatus) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to strchr.o(.text) for strchr
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to strcmp.o(.text) for strcmp
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to strncmp.o(.text) for strncmp
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.rodata.str1.1) for .L.str.18
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to software_dds.o(.text.DDS_Start) for DDS_Start
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to software_dds.o(.text.DDS_Stop) for DDS_Stop
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.text.DDS_Test_ShowHelp) for DDS_Test_ShowHelp
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to atoi.o(.text) for atoi
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.text.DDS_Test_SetAmplitude) for DDS_Test_SetAmplitude
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.text.DDS_Test_ShowDebugInfo) for DDS_Test_ShowDebugInfo
    dds_test.o(.text.DDS_Test_ProcessCommand) refers to dds_test.o(.text.DDS_Test_TestDAC) for DDS_Test_TestDAC
    dds_test.o(.ARM.exidx.text.DDS_Test_ProcessCommand) refers to dds_test.o(.text.DDS_Test_ProcessCommand) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_SetMode) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_SetMode) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_SetMode) refers to dds_test.o(.rodata.str1.1) for .L.str.19
    dds_test.o(.text.DDS_Test_SetMode) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_SetMode) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_SetMode) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    dds_test.o(.text.DDS_Test_SetMode) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.ARM.exidx.text.DDS_Test_SetMode) refers to dds_test.o(.text.DDS_Test_SetMode) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_FreqPlus) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_FreqPlus) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_FreqPlus) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_FreqPlus) refers to dds_test.o(.rodata.str1.1) for .L.str.21
    dds_test.o(.text.DDS_Test_FreqPlus) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_FreqPlus) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.text.DDS_Test_FreqPlus) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    dds_test.o(.ARM.exidx.text.DDS_Test_FreqPlus) refers to dds_test.o(.text.DDS_Test_FreqPlus) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_FreqMinus) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_FreqMinus) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_FreqMinus) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_FreqMinus) refers to dds_test.o(.rodata.str1.1) for .L.str.23
    dds_test.o(.text.DDS_Test_FreqMinus) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_FreqMinus) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.text.DDS_Test_FreqMinus) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    dds_test.o(.ARM.exidx.text.DDS_Test_FreqMinus) refers to dds_test.o(.text.DDS_Test_FreqMinus) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_Start) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_Start) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_Start) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    dds_test.o(.text.DDS_Test_Start) refers to software_dds.o(.text.DDS_Start) for DDS_Start
    dds_test.o(.text.DDS_Test_Start) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_Start) refers to dds_test.o(.rodata.str1.1) for .L.str.25
    dds_test.o(.text.DDS_Test_Start) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_Start) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.ARM.exidx.text.DDS_Test_Start) refers to dds_test.o(.text.DDS_Test_Start) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_Stop) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_Stop) refers to software_dds.o(.text.DDS_Stop) for DDS_Stop
    dds_test.o(.text.DDS_Test_Stop) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_Stop) refers to dds_test.o(.rodata.str1.1) for .L.str.26
    dds_test.o(.text.DDS_Test_Stop) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_Stop) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.ARM.exidx.text.DDS_Test_Stop) refers to dds_test.o(.text.DDS_Test_Stop) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_ShowHelp) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_ShowHelp) refers to dds_test.o(.rodata.str1.1) for .L.str.39
    dds_test.o(.text.DDS_Test_ShowHelp) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.ARM.exidx.text.DDS_Test_ShowHelp) refers to dds_test.o(.text.DDS_Test_ShowHelp) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_SetAmplitude) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_SetAmplitude) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_SetAmplitude) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_SetAmplitude) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_SetAmplitude) refers to software_dds.o(.text.DDS_Setup) for DDS_Setup
    dds_test.o(.text.DDS_Test_SetAmplitude) refers to dds_test.o(.text.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(.ARM.exidx.text.DDS_Test_SetAmplitude) refers to dds_test.o(.text.DDS_Test_SetAmplitude) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to dds_test.o(.rodata.str1.1) for .L.str.55
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to dds_test.o(.bss..L_MergedGlobals.79) for .L_MergedGlobals.79
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to dds_test.o(.data..L_MergedGlobals) for .L_MergedGlobals
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to software_dds.o(.text.DDS_GetConfig) for DDS_GetConfig
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to tim.o(.bss.htim6) for htim6
    dds_test.o(.text.DDS_Test_ShowDebugInfo) refers to software_dds.o(.text.DDS_GetInterruptStats) for DDS_GetInterruptStats
    dds_test.o(.ARM.exidx.text.DDS_Test_ShowDebugInfo) refers to dds_test.o(.text.DDS_Test_ShowDebugInfo) for [Anonymous Symbol]
    dds_test.o(.text.DDS_Test_TestDAC) refers to usart.o(.bss.huart1) for huart1
    dds_test.o(.text.DDS_Test_TestDAC) refers to my_usart.o(.text.my_printf) for my_printf
    dds_test.o(.text.DDS_Test_TestDAC) refers to dds_test.o(.rodata.str1.1) for .L.str.76
    dds_test.o(.text.DDS_Test_TestDAC) refers to dac.o(.bss.hdac) for hdac
    dds_test.o(.text.DDS_Test_TestDAC) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue) for HAL_DAC_SetValue
    dds_test.o(.text.DDS_Test_TestDAC) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    dds_test.o(.ARM.exidx.text.DDS_Test_TestDAC) refers to dds_test.o(.text.DDS_Test_TestDAC) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) refers to adc.o(.text.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForConversion) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForEvent) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to adc_app.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to adc_app.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.ADC_DMAError) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_GetValue) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt) refers to adc_app.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAConvCplt) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_GetBank2WRP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_GetBank2WRP) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterUnderDriveSTOPMode) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterUnderDriveSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableMemorySwappingBank) refers to stm32f4xx_hal.o(.text.HAL_EnableMemorySwappingBank) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableMemorySwappingBank) refers to stm32f4xx_hal.o(.text.HAL_DisableMemorySwappingBank) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) refers to dac.o(.text.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit) refers to dac.o(.text.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DeInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DMAUnderrunCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_SetValue) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvCpltCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvHalfCpltCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ErrorCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetValue) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConfigChannel) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetState) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetError) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStart) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStart) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStop) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStop) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_TriangleWaveGenerate) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_TriangleWaveGenerate) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_NoiseWaveGenerate) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_NoiseWaveGenerate) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualSetValue) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualSetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvCpltCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvHalfCpltCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ErrorCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DMAUnderrunCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualGetValue) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualGetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Extended_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_GetECC) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_IOSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_IOSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SendCommand) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_ProgramRefreshRate) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_ProgramRefreshRate) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SetAutoRefreshNumber) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SetAutoRefreshNumber) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_GetModeStatus) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_GetModeStatus) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to fmc.o(.text.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Init) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) refers to fmc.o(.text.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferCpltCallback) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferErrorCallback) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_8b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_8b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_16b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_16b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_32b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_32b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt) for SRAM_DMACpltProt
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_GetState) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to my_usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to my_usart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to my_usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to my_usart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to my_usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to my_usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_fir_init_f32.o(.text.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    log10f.o(i.__hardfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f.o(i.__hardfp_log10f) refers to errno.o(i.__set_errno) for __set_errno
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    log10f.o(i.__hardfp_log10f) refers to log10f.o(.constdata) for .constdata
    log10f.o(i.__softfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__softfp_log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(i.log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to errno.o(i.__set_errno) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429xx.o(HEAP), (512 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.data.initial_frequency), (4 bytes).
    Removing main.o(.data.initial_peak_amplitude), (2 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.ARM.exidx.text.MX_ADC1_Init), (8 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing adc.o(.text.HAL_ADC_MspDeInit), (62 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing dac.o(.text), (0 bytes).
    Removing dac.o(.ARM.exidx.text.MX_DAC_Init), (8 bytes).
    Removing dac.o(.ARM.exidx.text.HAL_DAC_MspInit), (8 bytes).
    Removing dac.o(.text.HAL_DAC_MspDeInit), (56 bytes).
    Removing dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing fmc.o(.text), (0 bytes).
    Removing fmc.o(.ARM.exidx.text.MX_FMC_Init), (8 bytes).
    Removing fmc.o(.ARM.exidx.text.HAL_SRAM_MspInit), (8 bytes).
    Removing fmc.o(.text.HAL_SRAM_MspDeInit), (94 bytes).
    Removing fmc.o(.ARM.exidx.text.HAL_SRAM_MspDeInit), (8 bytes).
    Removing fmc.o(.bss.FMC_DeInitialized), (1 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM6_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (56 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART3_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (166 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.ADC_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream0_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing ad_measure.o(.text), (0 bytes).
    Removing ad_measure.o(.text.findMinMax), (272 bytes).
    Removing ad_measure.o(.ARM.exidx.text.findMinMax), (8 bytes).
    Removing ad_measure.o(.text.setSamplingFrequency), (172 bytes).
    Removing ad_measure.o(.ARM.exidx.text.setSamplingFrequency), (8 bytes).
    Removing ad_measure.o(.text.readFIFOData), (1108 bytes).
    Removing ad_measure.o(.ARM.exidx.text.readFIFOData), (8 bytes).
    Removing ad_measure.o(.text.vpp_adc_parallel), (1564 bytes).
    Removing ad_measure.o(.ARM.exidx.text.vpp_adc_parallel), (8 bytes).
    Removing ad_measure.o(.text.ad_proc), (24 bytes).
    Removing ad_measure.o(.ARM.exidx.text.ad_proc), (8 bytes).
    Removing ad_measure.o(.bss.fifo_data1), (2048 bytes).
    Removing ad_measure.o(.bss.fifo_data2), (2048 bytes).
    Removing ad_measure.o(.bss.fifo_data2_f), (4096 bytes).
    Removing ad_measure.o(.bss..L_MergedGlobals), (16 bytes).
    Removing da_output.o(.text), (0 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_Init), (8 bytes).
    Removing da_output.o(.text.DA_SetConfig), (34 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_SetConfig), (8 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_Apply_Settings), (8 bytes).
    Removing da_output.o(.text.wave_test), (288 bytes).
    Removing da_output.o(.ARM.exidx.text.wave_test), (8 bytes).
    Removing freq_measure.o(.text), (0 bytes).
    Removing freq_measure.o(.text.fre_measure), (204 bytes).
    Removing freq_measure.o(.ARM.exidx.text.fre_measure), (8 bytes).
    Removing freq_measure.o(.text.fre_measure_ad1), (20 bytes).
    Removing freq_measure.o(.ARM.exidx.text.fre_measure_ad1), (8 bytes).
    Removing freq_measure.o(.text.fre_measure_ad2), (26 bytes).
    Removing freq_measure.o(.ARM.exidx.text.fre_measure_ad2), (8 bytes).
    Removing freq_measure.o(.text.freq_proc), (84 bytes).
    Removing freq_measure.o(.ARM.exidx.text.freq_proc), (8 bytes).
    Removing freq_measure.o(.bss..L_MergedGlobals), (24 bytes).
    Removing key_app.o(.text), (0 bytes).
    Removing key_app.o(.text.key_read), (76 bytes).
    Removing key_app.o(.ARM.exidx.text.key_read), (8 bytes).
    Removing key_app.o(.text.set_current_ad_frequency), (14 bytes).
    Removing key_app.o(.ARM.exidx.text.set_current_ad_frequency), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.get_current_ad_frequency), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.key_proc), (8 bytes).
    Removing key_app.o(.bss.detected_freq), (4 bytes).
    Removing ad9833.o(.text), (0 bytes).
    Removing ad9833.o(.text.AD9833_Init), (112 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_Init), (8 bytes).
    Removing ad9833.o(.text.AD9833_WriteData), (638 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_WriteData), (8 bytes).
    Removing ad9833.o(.text.AD9833_SetFrequency), (88 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_SetFrequency), (8 bytes).
    Removing ad9833.o(.text.AD9833_SetPhase), (6 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_SetPhase), (8 bytes).
    Removing ad9833.o(.text.AD9833_SetWave), (10 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_SetWave), (8 bytes).
    Removing ad9833.o(.text.AD9833_Setup), (168 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_Setup), (8 bytes).
    Removing ad9833.o(.text.AD9833_Init2), (106 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_Init2), (8 bytes).
    Removing ad9833.o(.text.AD9833_WriteData2), (566 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_WriteData2), (8 bytes).
    Removing ad9833.o(.text.AD9833_SetFrequency2), (88 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_SetFrequency2), (8 bytes).
    Removing ad9833.o(.text.AD9833_SetPhase2), (6 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_SetPhase2), (8 bytes).
    Removing ad9833.o(.text.AD9833_SetWave2), (10 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_SetWave2), (8 bytes).
    Removing ad9833.o(.text.AD9833_Setup2), (168 bytes).
    Removing ad9833.o(.ARM.exidx.text.AD9833_Setup2), (8 bytes).
    Removing adc_app.o(.text), (0 bytes).
    Removing adc_app.o(.ARM.exidx.text.adc_tim_dma_init), (8 bytes).
    Removing adc_app.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing adc_app.o(.text.adc_task), (268 bytes).
    Removing adc_app.o(.ARM.exidx.text.adc_task), (8 bytes).
    Removing adc_app.o(.bss.dac_val_buffer), (4096 bytes).
    Removing adc_app.o(.bss.res_val_buffer), (4096 bytes).
    Removing adc_app.o(.bss.voltage), (4 bytes).
    Removing dac_app.o(.text), (0 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_init), (8 bytes).
    Removing dac_app.o(.text.dac_app_set_amplitude), (124 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_set_amplitude), (8 bytes).
    Removing dac_app.o(.ARM.exidx.text.generate_waveform), (8 bytes).
    Removing dac_app.o(.ARM.exidx.text.start_dac_dma), (8 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_set_waveform), (8 bytes).
    Removing dac_app.o(.text.dac_app_set_frequency), (58 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_set_frequency), (8 bytes).
    Removing dac_app.o(.text.dac_app_get_amplitude), (12 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_get_amplitude), (8 bytes).
    Removing dac_app.o(.text.dac_app_set_zero_based), (68 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_set_zero_based), (8 bytes).
    Removing dac_app.o(.text.dac_app_get_zero_based), (12 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_get_zero_based), (8 bytes).
    Removing dac_app.o(.text.dac_app_get_actual_frequency), (50 bytes).
    Removing dac_app.o(.ARM.exidx.text.dac_app_get_actual_frequency), (8 bytes).
    Removing software_dds.o(.text), (0 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_Init), (8 bytes).
    Removing software_dds.o(.text.DDS_CalculatePhaseIncrement), (32 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_CalculatePhaseIncrement), (8 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_Start), (8 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_Stop), (8 bytes).
    Removing software_dds.o(.text.DDS_SetFrequency), (112 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_SetFrequency), (8 bytes).
    Removing software_dds.o(.text.DDS_SetAmplitude), (56 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_SetAmplitude), (8 bytes).
    Removing software_dds.o(.text.DDS_SetPhase), (38 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_SetPhase), (8 bytes).
    Removing software_dds.o(.text.DDS_SetWaveform), (12 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_SetWaveform), (8 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_Setup), (8 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_GetConfig), (8 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_GetInterruptStats), (8 bytes).
    Removing software_dds.o(.text.DDS_OutputSample), (222 bytes).
    Removing software_dds.o(.ARM.exidx.text.DDS_OutputSample), (8 bytes).
    Removing my_fft.o(.text), (0 bytes).
    Removing my_fft.o(.text.generate_hanning_window), (96 bytes).
    Removing my_fft.o(.ARM.exidx.text.generate_hanning_window), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.fft_init), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_fft_spectrum), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.output_fft_spectrum), (8 bytes).
    Removing my_fft.o(.text.get_precise_peak_frequency), (356 bytes).
    Removing my_fft.o(.ARM.exidx.text.get_precise_peak_frequency), (8 bytes).
    Removing my_fft.o(.text.round_to_nearest_k), (32 bytes).
    Removing my_fft.o(.ARM.exidx.text.round_to_nearest_k), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_thd), (8 bytes).
    Removing my_fft.o(.text.calculate_thd_n), (312 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_thd_n), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_sinad), (8 bytes).
    Removing my_filter.o(.text), (0 bytes).
    Removing my_filter.o(.text.arm_fir_f32_lp), (72 bytes).
    Removing my_filter.o(.ARM.exidx.text.arm_fir_f32_lp), (8 bytes).
    Removing my_filter.o(.rodata.BL), (4 bytes).
    Removing my_filter.o(.rodata.B), (204 bytes).
    Removing phase_measure.o(.text), (0 bytes).
    Removing phase_measure.o(.text.calculate_phase_diff), (280 bytes).
    Removing phase_measure.o(.ARM.exidx.text.calculate_phase_diff), (8 bytes).
    Removing phase_measure.o(.bss.phase_diff), (4 bytes).
    Removing kalman.o(.text), (0 bytes).
    Removing kalman.o(.text.Kalman_init), (38 bytes).
    Removing kalman.o(.ARM.exidx.text.Kalman_init), (8 bytes).
    Removing kalman.o(.text.kalman_filter), (102 bytes).
    Removing kalman.o(.ARM.exidx.text.kalman_filter), (8 bytes).
    Removing kalman.o(.text.kalman), (344 bytes).
    Removing kalman.o(.ARM.exidx.text.kalman), (8 bytes).
    Removing kalman.o(.text.kalman_thd), (172 bytes).
    Removing kalman.o(.ARM.exidx.text.kalman_thd), (8 bytes).
    Removing kalman.o(.data.last), (1 bytes).
    Removing kalman.o(.bss.current), (1 bytes).
    Removing kalman.o(.bss.state), (280 bytes).
    Removing kalman.o(.data.last1), (1 bytes).
    Removing kalman.o(.bss..L_MergedGlobals), (32 bytes).
    Removing wave_recognition.o(.text), (0 bytes).
    Removing wave_recognition.o(.ARM.exidx.text.find_peaks), (8 bytes).
    Removing wave_recognition.o(.text.calculate_bandwidth), (496 bytes).
    Removing wave_recognition.o(.ARM.exidx.text.calculate_bandwidth), (8 bytes).
    Removing wave_recognition.o(.text.calculate_carrier_suppression), (296 bytes).
    Removing wave_recognition.o(.ARM.exidx.text.calculate_carrier_suppression), (8 bytes).
    Removing wave_recognition.o(.text.detect_symmetry), (788 bytes).
    Removing wave_recognition.o(.ARM.exidx.text.detect_symmetry), (8 bytes).
    Removing wave_recognition.o(.ARM.exidx.text.recognize_waveform), (8 bytes).
    Removing my_hmi.o(.text), (0 bytes).
    Removing my_hmi.o(.text.HMI_Send_String), (72 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Send_String), (8 bytes).
    Removing my_hmi.o(.text.HMI_Send_Int), (60 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Send_Int), (8 bytes).
    Removing my_hmi.o(.text.HMI_Send_Float), (128 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Send_Float), (8 bytes).
    Removing my_hmi.o(.text.HMI_Wave_Clear), (72 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Wave_Clear), (8 bytes).
    Removing my_hmi.o(.text.HMI_Write_Wave_Low), (76 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Low), (8 bytes).
    Removing my_hmi.o(.text.HMI_Write_Wave_Fast), (132 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Fast), (8 bytes).
    Removing my_hmi.o(.rodata.str1.1), (13 bytes).
    Removing my_usart.o(.text), (0 bytes).
    Removing my_usart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing my_usart.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing my_usart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing my_usart.o(.ARM.exidx.text.process_uart1_command), (8 bytes).
    Removing my_usart.o(.ARM.exidx.text.process_uart2_command), (8 bytes).
    Removing my_usart.o(.bss.rxIndex1), (2 bytes).
    Removing my_usart.o(.bss.commandReceived1), (1 bytes).
    Removing my_usart.o(.bss.frameStarted), (1 bytes).
    Removing my_usart.o(.data.Adjust), (1 bytes).
    Removing my_usart.o(.bss.rxBuffer1), (128 bytes).
    Removing my_usart.o(.bss.rxTemp1), (1 bytes).
    Removing my_usart_pack.o(.text), (0 bytes).
    Removing my_usart_pack.o(.text.SetParseTemplate), (56 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.SetParseTemplate), (8 bytes).
    Removing my_usart_pack.o(.text.ParseFrame), (278 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.ParseFrame), (8 bytes).
    Removing my_usart_pack.o(.text.PrepareFrame), (378 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.PrepareFrame), (8 bytes).
    Removing my_usart_pack.o(.text.SendFrame), (36 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.SendFrame), (8 bytes).
    Removing my_usart_pack.o(.bss..L_MergedGlobals), (52 bytes).
    Removing cmd_to_fun.o(.text), (0 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.CTRL_INIT), (8 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_START), (8 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_STOP), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_CLR_ENABLE), (38 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_ENABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_CLR_DISABLE), (34 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_DISABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_START), (34 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_START), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_STOP), (38 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_STOP), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_SET), (26 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_SET), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE), (34 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_ENABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_WRITE_DISABLE), (36 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_DISABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE), (32 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_ENABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE), (36 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_DISABLE), (8 bytes).
    Removing cmd_to_fun.o(.rodata..Lswitch.table.AD_FIFO_WRITE_ENABLE), (6 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.text.uart_proc), (88 bytes).
    Removing scheduler.o(.ARM.exidx.text.uart_proc), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.circuit_learning_task), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.start_circuit_learning), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing scheduler.o(.bss.i), (4 bytes).
    Removing app_pid.o(.text), (0 bytes).
    Removing app_pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing app_pid.o(.text.increment_pid_ctrl), (118 bytes).
    Removing app_pid.o(.ARM.exidx.text.increment_pid_ctrl), (8 bytes).
    Removing app_pid.o(.text.Pid_Proc), (202 bytes).
    Removing app_pid.o(.ARM.exidx.text.Pid_Proc), (8 bytes).
    Removing app_pid.o(.data.output), (4 bytes).
    Removing dds_test.o(.text), (0 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_Init), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_ShowStatus), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_ProcessCommand), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_SetMode), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_FreqPlus), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_FreqMinus), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_Start), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_Stop), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_ShowHelp), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_SetAmplitude), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_ShowDebugInfo), (8 bytes).
    Removing dds_test.o(.ARM.exidx.text.DDS_Test_TestDAC), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_DeInit), (62 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Start), (300 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop), (56 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForConversion), (190 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForEvent), (136 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_IT), (316 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_IT), (70 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig), (108 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart), (230 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT), (238 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion), (166 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue), (50 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA), (284 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt), (100 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAHalfConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAError), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA), (106 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel), (344 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel), (88 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel), (8 bytes).
    Removing stm32f4xx_ll_adc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (576 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (128 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (108 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI), (108 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLSAI), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLSAI), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (342 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (210 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (242 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (232 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (306 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (392 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (98 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (204 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (370 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (54 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram), (224 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig), (22 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_GetBank2WRP), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (260 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (112 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1526 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (340 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (52 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (70 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (54 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (54 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (194 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableOverDrive), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive), (118 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableOverDrive), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterUnderDriveSTOPMode), (122 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterUnderDriveSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (46 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableMemorySwappingBank), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableMemorySwappingBank), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (154 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Init), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit), (30 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop), (38 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAConvCpltCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAErrorCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler), (102 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DMAUnderrunCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_SetValue), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvCpltCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvHalfCpltCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ErrorCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetValue), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetState), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetError), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStart), (84 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStart), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStop), (36 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStop), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_TriangleWaveGenerate), (74 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_TriangleWaveGenerate), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_NoiseWaveGenerate), (74 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_NoiseWaveGenerate), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualSetValue), (34 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualSetValue), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvCpltCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvHalfCpltCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ErrorCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DMAUnderrunCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualGetValue), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAConvCpltCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAErrorCh2), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text), (0 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Extended_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_Init), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_CommonSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_AttributeSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_DeInit), (72 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Enable), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Disable), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC), (102 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_GetECC), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_Init), (44 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_CommonSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_AttributeSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_IOSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_IOSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Init), (126 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Timing_Init), (216 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Enable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand), (94 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SendCommand), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_ProgramRefreshRate), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_ProgramRefreshRate), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SetAutoRefreshNumber), (24 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SetAutoRefreshNumber), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_GetModeStatus), (14 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_GetModeStatus), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text), (0 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Init), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit), (30 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_8b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_8b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_16b), (114 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_16b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_16b), (130 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_16b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_32b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_32b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA), (110 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.SRAM_DMACplt), (22 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACplt), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt), (22 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACpltProt), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.SRAM_DMAError), (22 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA), (96 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable), (66 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Enable), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable), (68 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Disable), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (252 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAError), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (60 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (284 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (334 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (210 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (190 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (284 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (334 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (210 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (190 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (290 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (344 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (198 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (576 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt), (116 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (174 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (124 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (126 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (142 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init), (178 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (188 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (178 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (236 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (520 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (242 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (428 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (442 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel), (534 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (594 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (510 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (120 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (508 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (120 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (228 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (74 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (220 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (74 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (30 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (194 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (218 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (226 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (278 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (236 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (292 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (528 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (66 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (188 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (236 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (292 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (528 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (188 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (124 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (340 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (60 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (52 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (146 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (36 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (124 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (126 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (274 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (136 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (206 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (96 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (146 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (248 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (154 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_fir_init_f32.o(.text), (0 bytes).
    Removing arm_fir_init_f32.o(.text.arm_fir_init_f32), (32 bytes).
    Removing arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).

1346 unused section(s) (total 957053 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  printfstubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmpge.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    AD9833.c                                 0x00000000   Number         0  ad9833.o ABSOLUTE
    ad_measure.c                             0x00000000   Number         0  ad_measure.o ABSOLUTE
    adc.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    adc_app.c                                0x00000000   Number         0  adc_app.o ABSOLUTE
    app_pid.c                                0x00000000   Number         0  app_pid.o ABSOLUTE
    arm_bitreversal.c                        0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    arm_cfft_radix4_f32.c                    0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    arm_cfft_radix4_init_f32.c               0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    arm_cmplx_mag_f32.c                      0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    arm_cos_f32.c                            0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    arm_fir_init_f32.c                       0x00000000   Number         0  arm_fir_init_f32.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cmd_to_fun.c                             0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    da_output.c                              0x00000000   Number         0  da_output.o ABSOLUTE
    dac.c                                    0x00000000   Number         0  dac.o ABSOLUTE
    dac_app.c                                0x00000000   Number         0  dac_app.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dds_test.c                               0x00000000   Number         0  dds_test.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    fmc.c                                    0x00000000   Number         0  fmc.o ABSOLUTE
    freq_measure.c                           0x00000000   Number         0  freq_measure.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    kalman.c                                 0x00000000   Number         0  kalman.o ABSOLUTE
    key_app.c                                0x00000000   Number         0  key_app.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    my_fft.c                                 0x00000000   Number         0  my_fft.o ABSOLUTE
    my_filter.c                              0x00000000   Number         0  my_filter.o ABSOLUTE
    my_hmi.c                                 0x00000000   Number         0  my_hmi.o ABSOLUTE
    my_usart.c                               0x00000000   Number         0  my_usart.o ABSOLUTE
    my_usart_pack.c                          0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    phase_measure.c                          0x00000000   Number         0  phase_measure.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    software_dds.c                           0x00000000   Number         0  software_dds.o ABSOLUTE
    startup_stm32f429xx.s                    0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_adc.c                      0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    stm32f4xx_hal_adc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dac.c                      0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    stm32f4xx_hal_dac_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_sram.c                     0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_ll_adc.c                       0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    stm32f4xx_ll_fmc.c                       0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    wave_recognition.c                       0x00000000   Number         0  wave_recognition.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x080001c0   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x080001c0   Section       36  startup_stm32f429xx.o(.text)
    .text                                    0x080001e4   Section        0  uldiv.o(.text)
    .text                                    0x08000246   Section        0  memcpya.o(.text)
    .text                                    0x0800026a   Section        0  memseta.o(.text)
    .text                                    0x0800028e   Section        0  strstr.o(.text)
    .text                                    0x080002b2   Section        0  strchr.o(.text)
    .text                                    0x080002c6   Section        0  strcmp.o(.text)
    .text                                    0x080002e2   Section        0  memcmp.o(.text)
    .text                                    0x080002fc   Section        0  strncmp.o(.text)
    .text                                    0x0800031a   Section        0  atoi.o(.text)
    .text                                    0x08000334   Section        0  dmul.o(.text)
    .text                                    0x08000418   Section        0  ddiv.o(.text)
    .text                                    0x080004f6   Section        0  dcmple.o(.text)
    .text                                    0x0800052c   Section        0  dcmpge.o(.text)
    .text                                    0x08000562   Section        0  dflti.o(.text)
    .text                                    0x08000584   Section        0  ffixul.o(.text)
    .text                                    0x080005b0   Section        0  dfixi.o(.text)
    .text                                    0x080005ee   Section        0  f2d.o(.text)
    .text                                    0x08000614   Section        0  d2f.o(.text)
    .text                                    0x0800064c   Section        0  uidiv.o(.text)
    .text                                    0x08000678   Section        0  llshl.o(.text)
    .text                                    0x08000696   Section        0  llushr.o(.text)
    .text                                    0x080006b6   Section        0  strtol.o(.text)
    .text                                    0x08000726   Section        0  iusefp.o(.text)
    .text                                    0x08000726   Section        0  fepilogue.o(.text)
    .text                                    0x08000794   Section        0  frnd.o(.text)
    .text                                    0x080007d0   Section        0  depilogue.o(.text)
    .text                                    0x0800088a   Section        0  dadd.o(.text)
    .text                                    0x080009d8   Section        0  dfixul.o(.text)
    .text                                    0x08000a08   Section       48  cdrcmple.o(.text)
    .text                                    0x08000a38   Section       48  init.o(.text)
    .text                                    0x08000a68   Section        0  llsshr.o(.text)
    .text                                    0x08000a8c   Section        0  ctype_o.o(.text)
    .text                                    0x08000a94   Section        0  _strtoul.o(.text)
    .text                                    0x08000b32   Section        0  _chval.o(.text)
    ADC_DMAConvCplt                          0x08000b51   Thumb Code   108  stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt)
    [Anonymous Symbol]                       0x08000b50   Section        0  stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt)
    ADC_DMAError                             0x08000bbd   Thumb Code    18  stm32f4xx_hal_adc.o(.text.ADC_DMAError)
    [Anonymous Symbol]                       0x08000bbc   Section        0  stm32f4xx_hal_adc.o(.text.ADC_DMAError)
    ADC_DMAHalfConvCplt                      0x08000bd1   Thumb Code     6  stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt)
    [Anonymous Symbol]                       0x08000bd0   Section        0  stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt)
    [Anonymous Symbol]                       0x08000bd8   Section        0  stm32f4xx_it.o(.text.ADC_IRQHandler)
    [Anonymous Symbol]                       0x08000be4   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000be8   Section        0  cmd_to_fun.o(.text.CTRL_INIT)
    [Anonymous Symbol]                       0x08000bf4   Section        0  stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1)
    [Anonymous Symbol]                       0x08000c04   Section        0  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2)
    [Anonymous Symbol]                       0x08000c14   Section        0  stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1)
    [Anonymous Symbol]                       0x08000c2c   Section        0  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2)
    [Anonymous Symbol]                       0x08000c44   Section        0  stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1)
    [Anonymous Symbol]                       0x08000c4c   Section        0  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2)
    [Anonymous Symbol]                       0x08000c54   Section        0  da_output.o(.text.DA_Apply_Settings)
    [Anonymous Symbol]                       0x08000d40   Section        0  cmd_to_fun.o(.text.DA_FPGA_START)
    [Anonymous Symbol]                       0x08000d50   Section        0  cmd_to_fun.o(.text.DA_FPGA_STOP)
    [Anonymous Symbol]                       0x08000d60   Section        0  da_output.o(.text.DA_Init)
    [Anonymous Symbol]                       0x08000e70   Section        0  software_dds.o(.text.DDS_GetConfig)
    [Anonymous Symbol]                       0x08000e88   Section        0  software_dds.o(.text.DDS_GetInterruptStats)
    [Anonymous Symbol]                       0x08000e9c   Section        0  software_dds.o(.text.DDS_Init)
    [Anonymous Symbol]                       0x08001228   Section        0  software_dds.o(.text.DDS_Setup)
    [Anonymous Symbol]                       0x08001334   Section        0  software_dds.o(.text.DDS_Start)
    [Anonymous Symbol]                       0x08001414   Section        0  software_dds.o(.text.DDS_Stop)
    [Anonymous Symbol]                       0x08001464   Section        0  dds_test.o(.text.DDS_Test_FreqMinus)
    [Anonymous Symbol]                       0x080014ec   Section        0  dds_test.o(.text.DDS_Test_FreqPlus)
    [Anonymous Symbol]                       0x0800156c   Section        0  dds_test.o(.text.DDS_Test_Init)
    [Anonymous Symbol]                       0x0800161c   Section        0  dds_test.o(.text.DDS_Test_ProcessCommand)
    [Anonymous Symbol]                       0x080019f8   Section        0  dds_test.o(.text.DDS_Test_SetAmplitude)
    [Anonymous Symbol]                       0x08001a50   Section        0  dds_test.o(.text.DDS_Test_SetMode)
    [Anonymous Symbol]                       0x08001acc   Section        0  dds_test.o(.text.DDS_Test_ShowDebugInfo)
    [Anonymous Symbol]                       0x08001c7c   Section        0  dds_test.o(.text.DDS_Test_ShowHelp)
    [Anonymous Symbol]                       0x08001dd4   Section        0  dds_test.o(.text.DDS_Test_ShowStatus)
    [Anonymous Symbol]                       0x08001f4c   Section        0  dds_test.o(.text.DDS_Test_Start)
    [Anonymous Symbol]                       0x08001f90   Section        0  dds_test.o(.text.DDS_Test_Stop)
    [Anonymous Symbol]                       0x08001fc0   Section        0  dds_test.o(.text.DDS_Test_TestDAC)
    [Anonymous Symbol]                       0x08002144   Section        0  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    [Anonymous Symbol]                       0x08002150   Section        0  stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler)
    [Anonymous Symbol]                       0x0800215c   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08002160   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08002168   Section        0  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init)
    [Anonymous Symbol]                       0x080021b0   Section        0  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init)
    [Anonymous Symbol]                       0x0800225c   Section        0  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init)
    [Anonymous Symbol]                       0x080022b0   Section        0  stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback)
    [Anonymous Symbol]                       0x080022b4   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    [Anonymous Symbol]                       0x080023f8   Section        0  adc_app.o(.text.HAL_ADC_ConvCpltCallback)
    [Anonymous Symbol]                       0x08002428   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback)
    [Anonymous Symbol]                       0x0800242c   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    [Anonymous Symbol]                       0x08002430   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler)
    [Anonymous Symbol]                       0x08002564   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_Init)
    [Anonymous Symbol]                       0x080026b8   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback)
    [Anonymous Symbol]                       0x080026bc   Section        0  adc.o(.text.HAL_ADC_MspInit)
    [Anonymous Symbol]                       0x08002778   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
    [Anonymous Symbol]                       0x080028f0   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA)
    [Anonymous Symbol]                       0x0800296c   Section        0  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2)
    [Anonymous Symbol]                       0x08002970   Section        0  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2)
    [Anonymous Symbol]                       0x08002974   Section        0  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2)
    [Anonymous Symbol]                       0x08002978   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel)
    [Anonymous Symbol]                       0x080029d8   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1)
    [Anonymous Symbol]                       0x080029dc   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1)
    [Anonymous Symbol]                       0x080029e0   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1)
    [Anonymous Symbol]                       0x080029e4   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Init)
    [Anonymous Symbol]                       0x08002a14   Section        0  dac.o(.text.HAL_DAC_MspInit)
    [Anonymous Symbol]                       0x08002acc   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue)
    [Anonymous Symbol]                       0x08002afc   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Start)
    [Anonymous Symbol]                       0x08002b64   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
    [Anonymous Symbol]                       0x08002c68   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA)
    [Anonymous Symbol]                       0x08002cc4   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x08002d44   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08002d68   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x08002ef0   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x08003030   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x080030d0   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x080030f8   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x0800328c   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x08003298   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x080032a4   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x080032b0   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080032cc   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08003304   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08003354   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x0800338c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x080033b0   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08003408   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08003428   Section        0  stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive)
    [Anonymous Symbol]                       0x080034a4   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback)
    [Anonymous Symbol]                       0x080034a8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08003608   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS)
    [Anonymous Symbol]                       0x08003614   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x0800363c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08003664   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x080036cc   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler)
    [Anonymous Symbol]                       0x080036e8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08003a44   Section        0  stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init)
    [Anonymous Symbol]                       0x08003aa0   Section        0  fmc.o(.text.HAL_SRAM_MspInit)
    [Anonymous Symbol]                       0x08003b58   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08003b84   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08003c40   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x08003c9c   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x08003d00   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start)
    [Anonymous Symbol]                       0x08003da0   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT)
    [Anonymous Symbol]                       0x08003e48   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop)
    [Anonymous Symbol]                       0x08003e74   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT)
    [Anonymous Symbol]                       0x08003ea8   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x08004048   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent)
    [Anonymous Symbol]                       0x0800406c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    [Anonymous Symbol]                       0x080040d4   Section        0  my_usart.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08004148   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    [Anonymous Symbol]                       0x08004220   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08004224   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x0800453c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x0800459c   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x080046bc   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    [Anonymous Symbol]                       0x08004710   Section        0  my_usart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x0800480c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x08004810   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x08004904   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x08004908   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x0800490c   Section        0  adc.o(.text.MX_ADC1_Init)
    [Anonymous Symbol]                       0x08004978   Section        0  dac.o(.text.MX_DAC_Init)
    [Anonymous Symbol]                       0x080049dc   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x08004a38   Section        0  fmc.o(.text.MX_FMC_Init)
    [Anonymous Symbol]                       0x08004aac   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08004c6c   Section        0  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x08004ce4   Section        0  tim.o(.text.MX_TIM6_Init)
    [Anonymous Symbol]                       0x08004d38   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08004d98   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x08004dd4   Section        0  usart.o(.text.MX_USART3_UART_Init)
    [Anonymous Symbol]                       0x08004e10   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08004e14   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08004e1c   Section        0  app_pid.o(.text.PID_Init)
    [Anonymous Symbol]                       0x08004e58   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08004e5c   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08004e60   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08004e64   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08004f20   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08004f34   Section        0  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    UART_DMAAbortOnError                     0x08005061   Thumb Code    10  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08005060   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAError                            0x0800506d   Thumb Code   162  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x0800506c   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAReceiveCplt                      0x08005111   Thumb Code   128  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x08005110   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMARxHalfCplt                       0x08005191   Thumb Code    24  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x08005190   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    UART_Receive_IT                          0x080051a9   Thumb Code   192  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x080051a8   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08005269   Thumb Code   222  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08005268   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08005348   Section        0  stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    [Anonymous Symbol]                       0x080053f4   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08005400   Section        0  stm32f4xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x0800540c   Section        0  stm32f4xx_it.o(.text.USART3_IRQHandler)
    [Anonymous Symbol]                       0x08005418   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x0800541c   Section        0  adc_app.o(.text.adc_tim_dma_init)
    [Anonymous Symbol]                       0x08005458   Section        0  arm_bitreversal.o(.text.arm_bitreversal_f32)
    [Anonymous Symbol]                       0x08005516   Section        0  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    [Anonymous Symbol]                       0x08005558   Section        0  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    [Anonymous Symbol]                       0x080055ec   Section        0  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    [Anonymous Symbol]                       0x08005740   Section        0  arm_cos_f32.o(.text.arm_cos_f32)
    [Anonymous Symbol]                       0x080057d8   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    [Anonymous Symbol]                       0x08005b34   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    [Anonymous Symbol]                       0x08005eb0   Section        0  my_fft.o(.text.calculate_fft_spectrum)
    [Anonymous Symbol]                       0x08006058   Section        0  my_fft.o(.text.calculate_sinad)
    [Anonymous Symbol]                       0x080061a8   Section        0  my_fft.o(.text.calculate_thd)
    [Anonymous Symbol]                       0x08006540   Section        0  scheduler.o(.text.circuit_learning_task)
    [Anonymous Symbol]                       0x080066a8   Section        0  dac_app.o(.text.dac_app_init)
    [Anonymous Symbol]                       0x0800673c   Section        0  dac_app.o(.text.dac_app_set_waveform)
    [Anonymous Symbol]                       0x0800677c   Section        0  my_fft.o(.text.fft_init)
    [Anonymous Symbol]                       0x080067f0   Section        0  wave_recognition.o(.text.find_peaks)
    generate_waveform                        0x08006ab5   Thumb Code  4444  dac_app.o(.text.generate_waveform)
    [Anonymous Symbol]                       0x08006ab4   Section        0  dac_app.o(.text.generate_waveform)
    __arm_cp.2_4                             0x08007184   Number         4  dac_app.o(.text.generate_waveform)
    [Anonymous Symbol]                       0x08007c18   Section        0  key_app.o(.text.get_current_ad_frequency)
    [Anonymous Symbol]                       0x08007c28   Section        0  key_app.o(.text.key_proc)
    [Anonymous Symbol]                       0x08007e00   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x08007f18   Section        0  my_usart.o(.text.my_printf)
    [Anonymous Symbol]                       0x08007f58   Section        0  my_fft.o(.text.output_fft_spectrum)
    __arm_cp.3_14                            0x080080e0   Number         4  my_fft.o(.text.output_fft_spectrum)
    [Anonymous Symbol]                       0x080084a4   Section        0  my_usart.o(.text.process_uart1_command)
    [Anonymous Symbol]                       0x080088e8   Section        0  my_usart.o(.text.process_uart2_command)
    [Anonymous Symbol]                       0x08008b58   Section        0  wave_recognition.o(.text.recognize_waveform)
    __arm_cp.4_17                            0x08009118   Number         4  wave_recognition.o(.text.recognize_waveform)
    __arm_cp.4_18                            0x0800911c   Number         4  wave_recognition.o(.text.recognize_waveform)
    __arm_cp.4_19                            0x080094d8   Number         4  wave_recognition.o(.text.recognize_waveform)
    [Anonymous Symbol]                       0x080096c0   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x080096d0   Section        0  scheduler.o(.text.scheduler_run)
    [Anonymous Symbol]                       0x0800971c   Section        0  scheduler.o(.text.start_circuit_learning)
    start_dac_dma                            0x080097f5   Thumb Code   266  dac_app.o(.text.start_dac_dma)
    [Anonymous Symbol]                       0x080097f4   Section        0  dac_app.o(.text.start_dac_dma)
    i.__0sprintf                             0x08009900   Section        0  printfa.o(i.__0sprintf)
    i.__0vsnprintf                           0x08009928   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassifyf                      0x0800995c   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__aeabi_errno_addr                     0x08009984   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__hardfp_cosf                          0x0800998c   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_log10f                        0x08009adc   Section        0  log10f.o(i.__hardfp_log10f)
    i.__hardfp_roundf                        0x08009c5c   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sinf                          0x08009cf8   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sqrtf                         0x08009e88   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x08009ec4   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08009ed8   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x08009ee0   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x08009ef0   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x08009f00   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__scatterload_copy                     0x0800a054   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800a062   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800a064   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800a074   Section        0  errno.o(i.__set_errno)
    _fp_digits                               0x0800a081   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x0800a080   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x0800a205   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x0800a204   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x0800a8e1   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x0800a8e0   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x0800a905   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x0800a904   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x0800a933   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x0800a932   Section        0  printfa.o(i._snputc)
    _sputc                                   0x0800a949   Thumb Code    10  printfa.o(i._sputc)
    i._sputc                                 0x0800a948   Section        0  printfa.o(i._sputc)
    logahi                                   0x0800a954   Data          32  log10f.o(.constdata)
    .constdata                               0x0800a954   Section       64  log10f.o(.constdata)
    logalo                                   0x0800a974   Data          32  log10f.o(.constdata)
    twooverpi                                0x0800a994   Data          32  rredf.o(.constdata)
    .constdata                               0x0800a994   Section       32  rredf.o(.constdata)
    .constdata                               0x0800a9b4   Section      129  ctype_o.o(.constdata)
    table                                    0x0800aa38   Data           4  ctype_o.o(.constdata)
    .constdata                               0x0800aa38   Section        4  ctype_o.o(.constdata)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x0800b254   Data           8  stm32f4xx_hal_dma.o(.rodata.cst8)
    [Anonymous Symbol]                       0x0800b254   Section        0  stm32f4xx_hal_dma.o(.rodata.cst8)
    .L.str                                   0x0800ba60   Data          39  key_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800ba60   Section        0  key_app.o(.rodata.str1.1)
    .L.str.1                                 0x0800ba87   Data          41  key_app.o(.rodata.str1.1)
    .L.str.6                                 0x0800bab0   Data          50  software_dds.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800bab0   Section        0  software_dds.o(.rodata.str1.1)
    .L.str.4                                 0x0800bae2   Data          48  software_dds.o(.rodata.str1.1)
    .L.str.5                                 0x0800bb12   Data          58  software_dds.o(.rodata.str1.1)
    .L.str.7                                 0x0800bb4c   Data          14  my_fft.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800bb4c   Section        0  my_fft.o(.rodata.str1.1)
    .L.str.8                                 0x0800bb5a   Data          16  my_fft.o(.rodata.str1.1)
    .L.str.5                                 0x0800bb6a   Data          24  my_fft.o(.rodata.str1.1)
    .L.str.11                                0x0800bb82   Data          26  my_fft.o(.rodata.str1.1)
    .L.str.6                                 0x0800bb9c   Data          54  my_fft.o(.rodata.str1.1)
    .L.str.10                                0x0800bbd2   Data          22  my_fft.o(.rodata.str1.1)
    .L.str.1                                 0x0800bbe8   Data          37  wave_recognition.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800bbe8   Section        0  wave_recognition.o(.rodata.str1.1)
    .L.str.2                                 0x0800bc0d   Data          42  wave_recognition.o(.rodata.str1.1)
    .L.str.3                                 0x0800bc37   Data          70  wave_recognition.o(.rodata.str1.1)
    .L.str.12                                0x0800bc7d   Data          47  my_usart.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800bc7d   Section        0  my_usart.o(.rodata.str1.1)
    .L.str.21                                0x0800bcac   Data          31  my_usart.o(.rodata.str1.1)
    .L.str.37                                0x0800bccb   Data          31  my_usart.o(.rodata.str1.1)
    .L.str.48                                0x0800bcea   Data          39  my_usart.o(.rodata.str1.1)
    .L.str.16                                0x0800bd11   Data          26  my_usart.o(.rodata.str1.1)
    .L.str.29                                0x0800bd2b   Data          21  my_usart.o(.rodata.str1.1)
    .L.str.25                                0x0800bd40   Data          22  my_usart.o(.rodata.str1.1)
    .L.str.23                                0x0800bd56   Data          22  my_usart.o(.rodata.str1.1)
    .L.str.35                                0x0800bd6c   Data          32  my_usart.o(.rodata.str1.1)
    .L.str.33                                0x0800bd8c   Data          31  my_usart.o(.rodata.str1.1)
    .L.str.27                                0x0800bdab   Data          21  my_usart.o(.rodata.str1.1)
    .L.str.14                                0x0800bdc0   Data          41  my_usart.o(.rodata.str1.1)
    .L.str.13                                0x0800bde9   Data          40  my_usart.o(.rodata.str1.1)
    .L.str.31                                0x0800be11   Data          25  my_usart.o(.rodata.str1.1)
    .L.str.18                                0x0800be2a   Data          40  my_usart.o(.rodata.str1.1)
    .L.str.10                                0x0800be52   Data          42  my_usart.o(.rodata.str1.1)
    .L.str.17                                0x0800be7c   Data          32  my_usart.o(.rodata.str1.1)
    .L.str.4                                 0x0800be9c   Data          32  my_usart.o(.rodata.str1.1)
    .L.str.43                                0x0800bebc   Data          22  my_usart.o(.rodata.str1.1)
    .L.str.19                                0x0800bed2   Data          35  my_usart.o(.rodata.str1.1)
    .L.str.44                                0x0800bef5   Data          51  my_usart.o(.rodata.str1.1)
    .L.str.9                                 0x0800bf28   Data          37  my_usart.o(.rodata.str1.1)
    .L.str.8                                 0x0800bf4d   Data          33  my_usart.o(.rodata.str1.1)
    .L.str.7                                 0x0800bf6e   Data          34  my_usart.o(.rodata.str1.1)
    .L.str.11                                0x0800bf90   Data          40  my_usart.o(.rodata.str1.1)
    .L.str.6                                 0x0800bfb8   Data          36  my_usart.o(.rodata.str1.1)
    .L.str.5                                 0x0800bfdc   Data          36  my_usart.o(.rodata.str1.1)
    .L.str.40                                0x0800c000   Data           5  my_usart.o(.rodata.str1.1)
    .L.str.36                                0x0800c005   Data           6  my_usart.o(.rodata.str1.1)
    .L.str.20                                0x0800c00b   Data           6  my_usart.o(.rodata.str1.1)
    .L.str.2                                 0x0800c011   Data           9  my_usart.o(.rodata.str1.1)
    .L.str.15                                0x0800c015   Data           5  my_usart.o(.rodata.str1.1)
    .L.str.28                                0x0800c01a   Data           5  my_usart.o(.rodata.str1.1)
    .L.str.34                                0x0800c01f   Data          10  my_usart.o(.rodata.str1.1)
    .L.str.32                                0x0800c029   Data           9  my_usart.o(.rodata.str1.1)
    .L.str.22                                0x0800c032   Data           5  my_usart.o(.rodata.str1.1)
    .L.str.24                                0x0800c037   Data           6  my_usart.o(.rodata.str1.1)
    .L.str.30                                0x0800c03d   Data           7  my_usart.o(.rodata.str1.1)
    .L.str.26                                0x0800c044   Data           6  my_usart.o(.rodata.str1.1)
    .L.str.50                                0x0800c04a   Data          29  dds_test.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800c04a   Section        0  dds_test.o(.rodata.str1.1)
    .L.str.26                                0x0800c067   Data          20  dds_test.o(.rodata.str1.1)
    .L.str.25                                0x0800c07b   Data          20  dds_test.o(.rodata.str1.1)
    .L.str.14                                0x0800c08f   Data          35  dds_test.o(.rodata.str1.1)
    .L.str.34                                0x0800c0b2   Data          18  dds_test.o(.rodata.str1.1)
    .L.str.44                                0x0800c0c4   Data          46  dds_test.o(.rodata.str1.1)
    .L.str.20                                0x0800c0f2   Data          42  dds_test.o(.rodata.str1.1)
    .L.str.22                                0x0800c11c   Data          42  dds_test.o(.rodata.str1.1)
    .L.str.18                                0x0800c146   Data          55  dds_test.o(.rodata.str1.1)
    .L.str.40                                0x0800c17d   Data          72  dds_test.o(.rodata.str1.1)
    .L.str.19                                0x0800c1c5   Data          54  dds_test.o(.rodata.str1.1)
    .L.str.41                                0x0800c1fb   Data          71  dds_test.o(.rodata.str1.1)
    .L.str.31                                0x0800c242   Data          23  dds_test.o(.rodata.str1.1)
    .L.str.76                                0x0800c259   Data          28  dds_test.o(.rodata.str1.1)
    .L.str.58                                0x0800c275   Data          14  dds_test.o(.rodata.str1.1)
    .L.str.56                                0x0800c283   Data          15  dds_test.o(.rodata.str1.1)
    .L.str                                   0x0800c292   Data          37  dds_test.o(.rodata.str1.1)
    .L.str.55                                0x0800c2b7   Data          27  dds_test.o(.rodata.str1.1)
    .L.str.39                                0x0800c2d2   Data          30  dds_test.o(.rodata.str1.1)
    .L.str.3                                 0x0800c2d4   Data          28  dds_test.o(.rodata.str1.1)
    .L.str.59                                0x0800c2f0   Data          64  dds_test.o(.rodata.str1.1)
    .L.str.61                                0x0800c330   Data          30  dds_test.o(.rodata.str1.1)
    .L.str.57                                0x0800c34e   Data          50  dds_test.o(.rodata.str1.1)
    .L.str.48                                0x0800c380   Data          41  dds_test.o(.rodata.str1.1)
    .L.str.43                                0x0800c3a9   Data          47  dds_test.o(.rodata.str1.1)
    .L.str.42                                0x0800c3d8   Data          47  dds_test.o(.rodata.str1.1)
    .L.str.49                                0x0800c407   Data          31  dds_test.o(.rodata.str1.1)
    .L.str.47                                0x0800c426   Data          36  dds_test.o(.rodata.str1.1)
    .L.str.46                                0x0800c44a   Data          32  dds_test.o(.rodata.str1.1)
    .L.str.45                                0x0800c46a   Data          33  dds_test.o(.rodata.str1.1)
    .L.str.62                                0x0800c48b   Data          25  dds_test.o(.rodata.str1.1)
    .L.str.23                                0x0800c4a4   Data          39  dds_test.o(.rodata.str1.1)
    .L.str.21                                0x0800c4cb   Data          39  dds_test.o(.rodata.str1.1)
    .L.str.33                                0x0800c4f2   Data          20  dds_test.o(.rodata.str1.1)
    .L.str.30                                0x0800c506   Data           8  dds_test.o(.rodata.str1.1)
    .L.str.29                                0x0800c50e   Data           8  dds_test.o(.rodata.str1.1)
    .L.str.28                                0x0800c516   Data           5  dds_test.o(.rodata.str1.1)
    _errno                                   0x20000000   Data           4  errno.o(.data)
    .data                                    0x20000000   Section        4  errno.o(.data)
    .L_MergedGlobals                         0x20000004   Data           8  dac_app.o(.data..L_MergedGlobals)
    current_peak_amplitude_mv                0x20000004   Data           2  dac_app.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000004   Section        0  dac_app.o(.data..L_MergedGlobals)
    dac_amplitude_raw                        0x20000006   Data           2  dac_app.o(.data..L_MergedGlobals)
    current_frequency_hz                     0x20000008   Data           4  dac_app.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x2000000c   Data           8  dds_test.o(.data..L_MergedGlobals)
    test_config.2                            0x2000000c   Data           1  dds_test.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x2000000c   Section        0  dds_test.o(.data..L_MergedGlobals)
    test_config.1                            0x20000010   Data           4  dds_test.o(.data..L_MergedGlobals)
    current_ad_freq                          0x20000018   Data           4  key_app.o(.data.current_ad_freq)
    [Anonymous Symbol]                       0x20000018   Section        0  key_app.o(.data.current_ad_freq)
    current_freq                             0x2000001c   Data           4  scheduler.o(.data.current_freq)
    [Anonymous Symbol]                       0x2000001c   Section        0  scheduler.o(.data.current_freq)
    dds_config                               0x20000020   Data          12  software_dds.o(.data.dds_config)
    [Anonymous Symbol]                       0x20000020   Section        0  software_dds.o(.data.dds_config)
    scheduler_task                           0x2000002c   Data          36  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x2000002c   Section        0  scheduler.o(.data.scheduler_task)
    .L_MergedGlobals                         0x20000058   Data          32  da_output.o(.bss..L_MergedGlobals)
    wave_test.current_waveform               0x20000058   Data           1  da_output.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000058   Section        0  da_output.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000078   Data           8  key_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000078   Section        0  key_app.o(.bss..L_MergedGlobals)
    key_proc.waveform_type                   0x2000007c   Data           1  key_app.o(.bss..L_MergedGlobals)
    key_proc.current_phase                   0x2000007e   Data           2  key_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000080   Data          24  software_dds.o(.bss..L_MergedGlobals)
    table_initialized                        0x20000080   Data           1  software_dds.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000080   Section        0  software_dds.o(.bss..L_MergedGlobals)
    phase_accumulator                        0x20000084   Data           4  software_dds.o(.bss..L_MergedGlobals)
    phase_increment                          0x20000088   Data           4  software_dds.o(.bss..L_MergedGlobals)
    dds_interrupt_counter                    0x2000008c   Data           4  software_dds.o(.bss..L_MergedGlobals)
    dds_sample_counter                       0x20000090   Data           4  software_dds.o(.bss..L_MergedGlobals)
    dds_debug_counter                        0x20000094   Data           4  software_dds.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000098   Data           8  my_usart.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000098   Section        0  my_usart.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x200000a0   Data           8  scheduler.o(.bss..L_MergedGlobals)
    learning_active                          0x200000a0   Data           1  scheduler.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x200000a0   Section        0  scheduler.o(.bss..L_MergedGlobals)
    learning_step                            0x200000a1   Data           1  scheduler.o(.bss..L_MergedGlobals)
    circuit_learning_task.step_start_time    0x200000a4   Data           4  scheduler.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x200000a8   Data          40  app_pid.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x200000a8   Section        0  app_pid.o(.bss..L_MergedGlobals)
    .L_MergedGlobals.1                       0x200000d0   Data           2  dac_app.o(.bss..L_MergedGlobals.1)
    current_waveform                         0x200000d0   Data           1  dac_app.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x200000d0   Section        0  dac_app.o(.bss..L_MergedGlobals.1)
    zero_based_waveform                      0x200000d1   Data           1  dac_app.o(.bss..L_MergedGlobals.1)
    .L_MergedGlobals.79                      0x200000d4   Data           5  dds_test.o(.bss..L_MergedGlobals.79)
    test_config.0                            0x200000d4   Data           1  dds_test.o(.bss..L_MergedGlobals.79)
    [Anonymous Symbol]                       0x200000d4   Section        0  dds_test.o(.bss..L_MergedGlobals.79)
    test_config.3                            0x200000d8   Data           1  dds_test.o(.bss..L_MergedGlobals.79)
    FMC_Initialized                          0x200000dc   Data           1  fmc.o(.bss.FMC_Initialized)
    [Anonymous Symbol]                       0x200000dc   Section        0  fmc.o(.bss.FMC_Initialized)
    sine_table                               0x20006528   Data        2048  software_dds.o(.bss.sine_table)
    [Anonymous Symbol]                       0x20006528   Section        0  software_dds.o(.bss.sine_table)
    spectrum_buffer                          0x20006d28   Data        2000  scheduler.o(.bss.spectrum_buffer)
    [Anonymous Symbol]                       0x20006d28   Section        0  scheduler.o(.bss.spectrum_buffer)
    waveform_buffer                          0x20007604   Data         256  dac_app.o(.bss.waveform_buffer)
    [Anonymous Symbol]                       0x20007604   Section        0  dac_app.o(.bss.waveform_buffer)
    STACK                                    0x20008708   Section     1024  startup_stm32f429xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM7_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    __aeabi_uldivmod                         0x080001e5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000247   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000247   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000247   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800026b   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800026b   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800026b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000279   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000279   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000279   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800027d   Thumb Code    18  memseta.o(.text)
    strstr                                   0x0800028f   Thumb Code    36  strstr.o(.text)
    strchr                                   0x080002b3   Thumb Code    20  strchr.o(.text)
    strcmp                                   0x080002c7   Thumb Code    28  strcmp.o(.text)
    memcmp                                   0x080002e3   Thumb Code    26  memcmp.o(.text)
    strncmp                                  0x080002fd   Thumb Code    30  strncmp.o(.text)
    atoi                                     0x0800031b   Thumb Code    26  atoi.o(.text)
    __aeabi_dmul                             0x08000335   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000419   Thumb Code   222  ddiv.o(.text)
    __aeabi_dcmple                           0x080004f7   Thumb Code    54  dcmple.o(.text)
    __aeabi_dcmpge                           0x0800052d   Thumb Code    54  dcmpge.o(.text)
    __aeabi_i2d                              0x08000563   Thumb Code    34  dflti.o(.text)
    __aeabi_f2ulz                            0x08000585   Thumb Code    44  ffixul.o(.text)
    __aeabi_d2iz                             0x080005b1   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x080005ef   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000615   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x0800064d   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800064d   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x08000679   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000679   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000697   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000697   Thumb Code     0  llushr.o(.text)
    strtol                                   0x080006b7   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x08000727   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000727   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000739   Thumb Code    92  fepilogue.o(.text)
    _frnd                                    0x08000795   Thumb Code    60  frnd.o(.text)
    _double_round                            0x080007d1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080007ef   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x0800088b   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080009cd   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080009d3   Thumb Code     6  dadd.o(.text)
    __aeabi_d2ulz                            0x080009d9   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000a09   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000a39   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x08000a39   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000a69   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000a69   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x08000a8d   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x08000a95   Thumb Code   158  _strtoul.o(.text)
    _chval                                   0x08000b33   Thumb Code    28  _chval.o(.text)
    ADC_IRQHandler                           0x08000bd9   Thumb Code    12  stm32f4xx_it.o(.text.ADC_IRQHandler)
    BusFault_Handler                         0x08000be5   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    CTRL_INIT                                0x08000be9   Thumb Code    12  cmd_to_fun.o(.text.CTRL_INIT)
    DAC_DMAConvCpltCh1                       0x08000bf5   Thumb Code    16  stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1)
    DAC_DMAConvCpltCh2                       0x08000c05   Thumb Code    16  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2)
    DAC_DMAErrorCh1                          0x08000c15   Thumb Code    24  stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1)
    DAC_DMAErrorCh2                          0x08000c2d   Thumb Code    24  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2)
    DAC_DMAHalfConvCpltCh1                   0x08000c45   Thumb Code     6  stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1)
    DAC_DMAHalfConvCpltCh2                   0x08000c4d   Thumb Code     6  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2)
    DA_Apply_Settings                        0x08000c55   Thumb Code   228  da_output.o(.text.DA_Apply_Settings)
    DA_FPGA_START                            0x08000d41   Thumb Code    16  cmd_to_fun.o(.text.DA_FPGA_START)
    DA_FPGA_STOP                             0x08000d51   Thumb Code    16  cmd_to_fun.o(.text.DA_FPGA_STOP)
    DA_Init                                  0x08000d61   Thumb Code   264  da_output.o(.text.DA_Init)
    DDS_GetConfig                            0x08000e71   Thumb Code    22  software_dds.o(.text.DDS_GetConfig)
    DDS_GetInterruptStats                    0x08000e89   Thumb Code    18  software_dds.o(.text.DDS_GetInterruptStats)
    DDS_Init                                 0x08000e9d   Thumb Code   840  software_dds.o(.text.DDS_Init)
    DDS_Setup                                0x08001229   Thumb Code   220  software_dds.o(.text.DDS_Setup)
    DDS_Start                                0x08001335   Thumb Code   104  software_dds.o(.text.DDS_Start)
    DDS_Stop                                 0x08001415   Thumb Code    64  software_dds.o(.text.DDS_Stop)
    DDS_Test_FreqMinus                       0x08001465   Thumb Code   134  dds_test.o(.text.DDS_Test_FreqMinus)
    DDS_Test_FreqPlus                        0x080014ed   Thumb Code   128  dds_test.o(.text.DDS_Test_FreqPlus)
    DDS_Test_Init                            0x0800156d   Thumb Code    56  dds_test.o(.text.DDS_Test_Init)
    DDS_Test_ProcessCommand                  0x0800161d   Thumb Code   820  dds_test.o(.text.DDS_Test_ProcessCommand)
    DDS_Test_SetAmplitude                    0x080019f9   Thumb Code    64  dds_test.o(.text.DDS_Test_SetAmplitude)
    DDS_Test_SetMode                         0x08001a51   Thumb Code   122  dds_test.o(.text.DDS_Test_SetMode)
    DDS_Test_ShowDebugInfo                   0x08001acd   Thumb Code   280  dds_test.o(.text.DDS_Test_ShowDebugInfo)
    DDS_Test_ShowHelp                        0x08001c7d   Thumb Code   216  dds_test.o(.text.DDS_Test_ShowHelp)
    DDS_Test_ShowStatus                      0x08001dd5   Thumb Code   220  dds_test.o(.text.DDS_Test_ShowStatus)
    DDS_Test_Start                           0x08001f4d   Thumb Code    66  dds_test.o(.text.DDS_Test_Start)
    DDS_Test_Stop                            0x08001f91   Thumb Code    46  dds_test.o(.text.DDS_Test_Stop)
    DDS_Test_TestDAC                         0x08001fc1   Thumb Code   244  dds_test.o(.text.DDS_Test_TestDAC)
    DMA1_Stream5_IRQHandler                  0x08002145   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    DMA2_Stream0_IRQHandler                  0x08002151   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler)
    DebugMon_Handler                         0x0800215d   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Error_Handler                            0x08002161   Thumb Code     6  main.o(.text.Error_Handler)
    FMC_NORSRAM_Extended_Timing_Init         0x08002169   Thumb Code    72  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init)
    FMC_NORSRAM_Init                         0x080021b1   Thumb Code   170  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init)
    FMC_NORSRAM_Timing_Init                  0x0800225d   Thumb Code    84  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init)
    HAL_ADCEx_InjectedConvCpltCallback       0x080022b1   Thumb Code     2  stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADC_ConfigChannel                    0x080022b5   Thumb Code   324  stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x080023f9   Thumb Code    46  adc_app.o(.text.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08002429   Thumb Code     2  stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x0800242d   Thumb Code     2  stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x08002431   Thumb Code   308  stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08002565   Thumb Code   338  stm32f4xx_hal_adc.o(.text.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x080026b9   Thumb Code     2  stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x080026bd   Thumb Code   188  adc.o(.text.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08002779   Thumb Code   376  stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x080028f1   Thumb Code   122  stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA)
    HAL_DACEx_ConvCpltCallbackCh2            0x0800296d   Thumb Code     2  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2)
    HAL_DACEx_ConvHalfCpltCallbackCh2        0x08002971   Thumb Code     2  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2)
    HAL_DACEx_ErrorCallbackCh2               0x08002975   Thumb Code     2  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2)
    HAL_DAC_ConfigChannel                    0x08002979   Thumb Code    96  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel)
    HAL_DAC_ConvCpltCallbackCh1              0x080029d9   Thumb Code     2  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1)
    HAL_DAC_ConvHalfCpltCallbackCh1          0x080029dd   Thumb Code     2  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1)
    HAL_DAC_ErrorCallbackCh1                 0x080029e1   Thumb Code     2  stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1)
    HAL_DAC_Init                             0x080029e5   Thumb Code    46  stm32f4xx_hal_dac.o(.text.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x08002a15   Thumb Code   182  dac.o(.text.HAL_DAC_MspInit)
    HAL_DAC_SetValue                         0x08002acd   Thumb Code    46  stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue)
    HAL_DAC_Start                            0x08002afd   Thumb Code   102  stm32f4xx_hal_dac.o(.text.HAL_DAC_Start)
    HAL_DAC_Start_DMA                        0x08002b65   Thumb Code   260  stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
    HAL_DAC_Stop_DMA                         0x08002c69   Thumb Code    90  stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA)
    HAL_DMA_Abort                            0x08002cc5   Thumb Code   128  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002d45   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002d69   Thumb Code   392  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002ef1   Thumb Code   318  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08003031   Thumb Code   160  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_Delay                                0x080030d1   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x080030f9   Thumb Code   404  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x0800328d   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08003299   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080032a5   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x080032b1   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080032cd   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08003305   Thumb Code    80  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08003355   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800338d   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080033b1   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003409   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x08003429   Thumb Code   122  stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive)
    HAL_RCC_CSSCallback                      0x080034a5   Thumb Code     2  stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback)
    HAL_RCC_ClockConfig                      0x080034a9   Thumb Code   352  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_EnableCSS                        0x08003609   Thumb Code    12  stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS)
    HAL_RCC_GetPCLK1Freq                     0x08003615   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800363d   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003665   Thumb Code   104  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_NMI_IRQHandler                   0x080036cd   Thumb Code    28  stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler)
    HAL_RCC_OscConfig                        0x080036e9   Thumb Code   860  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x08003a45   Thumb Code    92  stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x08003aa1   Thumb Code   184  fmc.o(.text.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x08003b59   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08003b85   Thumb Code   188  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003c41   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003c9d   Thumb Code    98  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x08003d01   Thumb Code   160  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x08003da1   Thumb Code   168  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT)
    HAL_TIM_Base_Stop                        0x08003e49   Thumb Code    42  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop)
    HAL_TIM_Base_Stop_IT                     0x08003e75   Thumb Code    50  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT)
    HAL_TIM_ConfigClockSource                0x08003ea9   Thumb Code   416  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_GenerateEvent                    0x08004049   Thumb Code    36  stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent)
    HAL_UARTEx_ReceiveToIdle_DMA             0x0800406d   Thumb Code   104  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080040d5   Thumb Code   114  my_usart.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08004149   Thumb Code   214  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08004221   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08004225   Thumb Code   790  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800453d   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x0800459d   Thumb Code   286  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080046bd   Thumb Code    84  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004711   Thumb Code   244  my_usart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x0800480d   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08004811   Thumb Code   244  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004905   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004909   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MX_ADC1_Init                             0x0800490d   Thumb Code   108  adc.o(.text.MX_ADC1_Init)
    MX_DAC_Init                              0x08004979   Thumb Code    98  dac.o(.text.MX_DAC_Init)
    MX_DMA_Init                              0x080049dd   Thumb Code    92  dma.o(.text.MX_DMA_Init)
    MX_FMC_Init                              0x08004a39   Thumb Code   114  fmc.o(.text.MX_FMC_Init)
    MX_GPIO_Init                             0x08004aad   Thumb Code   448  gpio.o(.text.MX_GPIO_Init)
    MX_TIM3_Init                             0x08004c6d   Thumb Code   120  tim.o(.text.MX_TIM3_Init)
    MX_TIM6_Init                             0x08004ce5   Thumb Code    84  tim.o(.text.MX_TIM6_Init)
    MX_USART1_UART_Init                      0x08004d39   Thumb Code    96  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004d99   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004dd5   Thumb Code    60  usart.o(.text.MX_USART3_UART_Init)
    MemManage_Handler                        0x08004e11   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x08004e15   Thumb Code     6  stm32f4xx_it.o(.text.NMI_Handler)
    PID_Init                                 0x08004e1d   Thumb Code    60  app_pid.o(.text.PID_Init)
    PendSV_Handler                           0x08004e59   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x08004e5d   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x08004e61   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x08004e65   Thumb Code   188  main.o(.text.SystemClock_Config)
    SystemInit                               0x08004f21   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    TIM_Base_SetConfig                       0x08004f35   Thumb Code   300  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    UART_Start_Receive_DMA                   0x08005349   Thumb Code   170  stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x080053f5   Thumb Code    12  stm32f4xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x08005401   Thumb Code    12  stm32f4xx_it.o(.text.USART2_IRQHandler)
    USART3_IRQHandler                        0x0800540d   Thumb Code    12  stm32f4xx_it.o(.text.USART3_IRQHandler)
    UsageFault_Handler                       0x08005419   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    adc_tim_dma_init                         0x0800541d   Thumb Code    58  adc_app.o(.text.adc_tim_dma_init)
    arm_bitreversal_f32                      0x08005459   Thumb Code   190  arm_bitreversal.o(.text.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x08005517   Thumb Code    64  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x08005559   Thumb Code   148  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x080055ed   Thumb Code   340  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    arm_cos_f32                              0x08005741   Thumb Code   152  arm_cos_f32.o(.text.arm_cos_f32)
    arm_radix4_butterfly_f32                 0x080057d9   Thumb Code   858  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x08005b35   Thumb Code   890  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    calculate_fft_spectrum                   0x08005eb1   Thumb Code   420  my_fft.o(.text.calculate_fft_spectrum)
    calculate_sinad                          0x08006059   Thumb Code   316  my_fft.o(.text.calculate_sinad)
    calculate_thd                            0x080061a9   Thumb Code   896  my_fft.o(.text.calculate_thd)
    circuit_learning_task                    0x08006541   Thumb Code   244  scheduler.o(.text.circuit_learning_task)
    dac_app_init                             0x080066a9   Thumb Code   140  dac_app.o(.text.dac_app_init)
    dac_app_set_waveform                     0x0800673d   Thumb Code    62  dac_app.o(.text.dac_app_set_waveform)
    fft_init                                 0x0800677d   Thumb Code   108  my_fft.o(.text.fft_init)
    find_peaks                               0x080067f1   Thumb Code   696  wave_recognition.o(.text.find_peaks)
    get_current_ad_frequency                 0x08007c19   Thumb Code    14  key_app.o(.text.get_current_ad_frequency)
    key_proc                                 0x08007c29   Thumb Code   340  key_app.o(.text.key_proc)
    main                                     0x08007e01   Thumb Code   196  main.o(.text.main)
    my_printf                                0x08007f19   Thumb Code    62  my_usart.o(.text.my_printf)
    output_fft_spectrum                      0x08007f59   Thumb Code  1172  my_fft.o(.text.output_fft_spectrum)
    process_uart1_command                    0x080084a5   Thumb Code   960  my_usart.o(.text.process_uart1_command)
    process_uart2_command                    0x080088e9   Thumb Code   476  my_usart.o(.text.process_uart2_command)
    recognize_waveform                       0x08008b59   Thumb Code  2744  wave_recognition.o(.text.recognize_waveform)
    scheduler_init                           0x080096c1   Thumb Code    14  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x080096d1   Thumb Code    74  scheduler.o(.text.scheduler_run)
    start_circuit_learning                   0x0800971d   Thumb Code    80  scheduler.o(.text.start_circuit_learning)
    __0sprintf                               0x08009901   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08009901   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08009901   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08009901   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08009901   Thumb Code     0  printfa.o(i.__0sprintf)
    __0vsnprintf                             0x08009929   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08009929   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08009929   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08009929   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08009929   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassifyf                        0x0800995d   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __aeabi_errno_addr                       0x08009985   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08009985   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __hardfp_cosf                            0x0800998d   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_log10f                          0x08009add   Thumb Code   332  log10f.o(i.__hardfp_log10f)
    __hardfp_roundf                          0x08009c5d   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sinf                            0x08009cf9   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sqrtf                           0x08009e89   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x08009ec5   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08009ed9   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x08009ee1   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x08009ef1   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x08009f01   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __scatterload_copy                       0x0800a055   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800a063   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800a065   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800a075   Thumb Code     6  errno.o(i.__set_errno)
    __ctype_table                            0x0800a9b4   Data         129  ctype_o.o(.constdata)
    AHBPrescTable                            0x0800aa3c   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x0800aa4c   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    armBitRevTable                           0x0800aa54   Data        2048  arm_common_tables.o(.rodata.armBitRevTable)
    sinTable_f32                             0x0800b25c   Data        2052  arm_common_tables.o(.rodata.sinTable_f32)
    twiddleCoef_4096                         0x0800c51c   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    Region$$Table$$Base                      0x0801451c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0801453c   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000014   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    uwTickFreq                               0x20000050   Data           1  stm32f4xx_hal.o(.data.uwTickFreq)
    uwTickPrio                               0x20000054   Data           4  stm32f4xx_hal.o(.data.uwTickPrio)
    wave_tick                                0x2000005c   Data           4  da_output.o(.bss..L_MergedGlobals)
    da_channels                              0x20000060   Data          24  da_output.o(.bss..L_MergedGlobals)
    key_val                                  0x20000078   Data           1  key_app.o(.bss..L_MergedGlobals)
    key_old                                  0x20000079   Data           1  key_app.o(.bss..L_MergedGlobals)
    key_down                                 0x2000007a   Data           1  key_app.o(.bss..L_MergedGlobals)
    key_up                                   0x2000007b   Data           1  key_app.o(.bss..L_MergedGlobals)
    commandReceived2                         0x20000098   Data           1  my_usart.o(.bss..L_MergedGlobals)
    commandReceived3                         0x20000099   Data           1  my_usart.o(.bss..L_MergedGlobals)
    rxTemp3                                  0x2000009a   Data           1  my_usart.o(.bss..L_MergedGlobals)
    rxTemp2                                  0x2000009b   Data           1  my_usart.o(.bss..L_MergedGlobals)
    rxIndex3                                 0x2000009c   Data           2  my_usart.o(.bss..L_MergedGlobals)
    rxIndex2                                 0x2000009e   Data           2  my_usart.o(.bss..L_MergedGlobals)
    vin                                      0x200000a8   Data           4  app_pid.o(.bss..L_MergedGlobals)
    PID                                      0x200000ac   Data          36  app_pid.o(.bss..L_MergedGlobals)
    AdcConvEnd                               0x200000d9   Data           1  adc_app.o(.bss.AdcConvEnd)
    adc_val_buffer                           0x200000e0   Data        8192  adc_app.o(.bss.adc_val_buffer)
    fft_input_buffer                         0x200020e0   Data        8192  my_fft.o(.bss.fft_input_buffer)
    fft_instance                             0x200040e0   Data          20  my_fft.o(.bss.fft_instance)
    fft_magnitude                            0x200040f4   Data        4096  my_fft.o(.bss.fft_magnitude)
    fifo_data1_f                             0x200050f4   Data        4096  ad_measure.o(.bss.fifo_data1_f)
    hadc1                                    0x200060f4   Data          72  adc.o(.bss.hadc1)
    hdac                                     0x2000613c   Data          20  dac.o(.bss.hdac)
    hdma_adc1                                0x20006150   Data          96  adc.o(.bss.hdma_adc1)
    hdma_dac1                                0x200061b0   Data          96  dac.o(.bss.hdma_dac1)
    hdma_usart1_rx                           0x20006210   Data          96  usart.o(.bss.hdma_usart1_rx)
    hsram2                                   0x20006270   Data          80  fmc.o(.bss.hsram2)
    htim3                                    0x200062c0   Data          72  tim.o(.bss.htim3)
    htim6                                    0x20006308   Data          72  tim.o(.bss.htim6)
    huart1                                   0x20006350   Data          72  usart.o(.bss.huart1)
    huart2                                   0x20006398   Data          72  usart.o(.bss.huart2)
    huart3                                   0x200063e0   Data          72  usart.o(.bss.huart3)
    rxBuffer2                                0x20006428   Data         128  my_usart.o(.bss.rxBuffer2)
    rxBuffer3                                0x200064a8   Data         128  my_usart.o(.bss.rxBuffer3)
    task_num                                 0x200074f8   Data           1  scheduler.o(.bss.task_num)
    uart1_cmd_buffer                         0x200074fc   Data         128  my_usart.o(.bss.uart1_cmd_buffer)
    uart1_cmd_flag                           0x2000757c   Data           1  my_usart.o(.bss.uart1_cmd_flag)
    uart1_dma_buffer                         0x20007580   Data         128  my_usart.o(.bss.uart1_dma_buffer)
    uwTick                                   0x20007600   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    window_buffer                            0x20007704   Data        4096  my_fft.o(.bss.window_buffer)
    __initial_sp                             0x20008b08   Data           0  startup_stm32f429xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00014598, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0001453c, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         2020  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         2372    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         2375    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2377    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2379    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         2380    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         2382    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         2384    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         2373    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO            4    .text               startup_stm32f429xx.o
    0x080001e4   0x080001e4   0x00000062   Code   RO         2023    .text               mc_w.l(uldiv.o)
    0x08000246   0x08000246   0x00000024   Code   RO         2025    .text               mc_w.l(memcpya.o)
    0x0800026a   0x0800026a   0x00000024   Code   RO         2027    .text               mc_w.l(memseta.o)
    0x0800028e   0x0800028e   0x00000024   Code   RO         2029    .text               mc_w.l(strstr.o)
    0x080002b2   0x080002b2   0x00000014   Code   RO         2031    .text               mc_w.l(strchr.o)
    0x080002c6   0x080002c6   0x0000001c   Code   RO         2033    .text               mc_w.l(strcmp.o)
    0x080002e2   0x080002e2   0x0000001a   Code   RO         2035    .text               mc_w.l(memcmp.o)
    0x080002fc   0x080002fc   0x0000001e   Code   RO         2037    .text               mc_w.l(strncmp.o)
    0x0800031a   0x0800031a   0x0000001a   Code   RO         2302    .text               mc_w.l(atoi.o)
    0x08000334   0x08000334   0x000000e4   Code   RO         2304    .text               mf_w.l(dmul.o)
    0x08000418   0x08000418   0x000000de   Code   RO         2306    .text               mf_w.l(ddiv.o)
    0x080004f6   0x080004f6   0x00000036   Code   RO         2308    .text               mf_w.l(dcmple.o)
    0x0800052c   0x0800052c   0x00000036   Code   RO         2310    .text               mf_w.l(dcmpge.o)
    0x08000562   0x08000562   0x00000022   Code   RO         2312    .text               mf_w.l(dflti.o)
    0x08000584   0x08000584   0x0000002c   Code   RO         2314    .text               mf_w.l(ffixul.o)
    0x080005b0   0x080005b0   0x0000003e   Code   RO         2316    .text               mf_w.l(dfixi.o)
    0x080005ee   0x080005ee   0x00000026   Code   RO         2318    .text               mf_w.l(f2d.o)
    0x08000614   0x08000614   0x00000038   Code   RO         2320    .text               mf_w.l(d2f.o)
    0x0800064c   0x0800064c   0x0000002c   Code   RO         2400    .text               mc_w.l(uidiv.o)
    0x08000678   0x08000678   0x0000001e   Code   RO         2402    .text               mc_w.l(llshl.o)
    0x08000696   0x08000696   0x00000020   Code   RO         2404    .text               mc_w.l(llushr.o)
    0x080006b6   0x080006b6   0x00000070   Code   RO         2413    .text               mc_w.l(strtol.o)
    0x08000726   0x08000726   0x00000000   Code   RO         2415    .text               mc_w.l(iusefp.o)
    0x08000726   0x08000726   0x0000006e   Code   RO         2416    .text               mf_w.l(fepilogue.o)
    0x08000794   0x08000794   0x0000003c   Code   RO         2418    .text               mf_w.l(frnd.o)
    0x080007d0   0x080007d0   0x000000ba   Code   RO         2420    .text               mf_w.l(depilogue.o)
    0x0800088a   0x0800088a   0x0000014e   Code   RO         2422    .text               mf_w.l(dadd.o)
    0x080009d8   0x080009d8   0x00000030   Code   RO         2428    .text               mf_w.l(dfixul.o)
    0x08000a08   0x08000a08   0x00000030   Code   RO         2430    .text               mf_w.l(cdrcmple.o)
    0x08000a38   0x08000a38   0x00000030   Code   RO         2432    .text               mc_w.l(init.o)
    0x08000a68   0x08000a68   0x00000024   Code   RO         2435    .text               mc_w.l(llsshr.o)
    0x08000a8c   0x08000a8c   0x00000008   Code   RO         2437    .text               mc_w.l(ctype_o.o)
    0x08000a94   0x08000a94   0x0000009e   Code   RO         2465    .text               mc_w.l(_strtoul.o)
    0x08000b32   0x08000b32   0x0000001c   Code   RO         2470    .text               mc_w.l(_chval.o)
    0x08000b4e   0x08000b4e   0x00000002   PAD
    0x08000b50   0x08000b50   0x0000006c   Code   RO          673    .text.ADC_DMAConvCplt  stm32f4xx_hal_adc.o
    0x08000bbc   0x08000bbc   0x00000012   Code   RO          677    .text.ADC_DMAError  stm32f4xx_hal_adc.o
    0x08000bce   0x08000bce   0x00000002   PAD
    0x08000bd0   0x08000bd0   0x00000006   Code   RO          675    .text.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x08000bd6   0x08000bd6   0x00000002   PAD
    0x08000bd8   0x08000bd8   0x0000000c   Code   RO          154    .text.ADC_IRQHandler  stm32f4xx_it.o
    0x08000be4   0x08000be4   0x00000002   Code   RO          140    .text.BusFault_Handler  stm32f4xx_it.o
    0x08000be6   0x08000be6   0x00000002   PAD
    0x08000be8   0x08000be8   0x0000000c   Code   RO          535    .text.CTRL_INIT     cmd_to_fun.o
    0x08000bf4   0x08000bf4   0x00000010   Code   RO         1164    .text.DAC_DMAConvCpltCh1  stm32f4xx_hal_dac.o
    0x08000c04   0x08000c04   0x00000010   Code   RO         1220    .text.DAC_DMAConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08000c14   0x08000c14   0x00000018   Code   RO         1168    .text.DAC_DMAErrorCh1  stm32f4xx_hal_dac.o
    0x08000c2c   0x08000c2c   0x00000018   Code   RO         1224    .text.DAC_DMAErrorCh2  stm32f4xx_hal_dac_ex.o
    0x08000c44   0x08000c44   0x00000006   Code   RO         1166    .text.DAC_DMAHalfConvCpltCh1  stm32f4xx_hal_dac.o
    0x08000c4a   0x08000c4a   0x00000002   PAD
    0x08000c4c   0x08000c4c   0x00000006   Code   RO         1222    .text.DAC_DMAHalfConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08000c52   0x08000c52   0x00000002   PAD
    0x08000c54   0x08000c54   0x000000ec   Code   RO          206    .text.DA_Apply_Settings  da_output.o
    0x08000d40   0x08000d40   0x00000010   Code   RO          537    .text.DA_FPGA_START  cmd_to_fun.o
    0x08000d50   0x08000d50   0x00000010   Code   RO          539    .text.DA_FPGA_STOP  cmd_to_fun.o
    0x08000d60   0x08000d60   0x00000110   Code   RO          202    .text.DA_Init       da_output.o
    0x08000e70   0x08000e70   0x00000016   Code   RO          356    .text.DDS_GetConfig  software_dds.o
    0x08000e86   0x08000e86   0x00000002   PAD
    0x08000e88   0x08000e88   0x00000012   Code   RO          358    .text.DDS_GetInterruptStats  software_dds.o
    0x08000e9a   0x08000e9a   0x00000002   PAD
    0x08000e9c   0x08000e9c   0x0000038c   Code   RO          338    .text.DDS_Init      software_dds.o
    0x08001228   0x08001228   0x0000010c   Code   RO          354    .text.DDS_Setup     software_dds.o
    0x08001334   0x08001334   0x000000e0   Code   RO          342    .text.DDS_Start     software_dds.o
    0x08001414   0x08001414   0x00000050   Code   RO          344    .text.DDS_Stop      software_dds.o
    0x08001464   0x08001464   0x00000086   Code   RO          618    .text.DDS_Test_FreqMinus  dds_test.o
    0x080014ea   0x080014ea   0x00000002   PAD
    0x080014ec   0x080014ec   0x00000080   Code   RO          616    .text.DDS_Test_FreqPlus  dds_test.o
    0x0800156c   0x0800156c   0x000000b0   Code   RO          608    .text.DDS_Test_Init  dds_test.o
    0x0800161c   0x0800161c   0x000003dc   Code   RO          612    .text.DDS_Test_ProcessCommand  dds_test.o
    0x080019f8   0x080019f8   0x00000058   Code   RO          626    .text.DDS_Test_SetAmplitude  dds_test.o
    0x08001a50   0x08001a50   0x0000007a   Code   RO          614    .text.DDS_Test_SetMode  dds_test.o
    0x08001aca   0x08001aca   0x00000002   PAD
    0x08001acc   0x08001acc   0x000001b0   Code   RO          628    .text.DDS_Test_ShowDebugInfo  dds_test.o
    0x08001c7c   0x08001c7c   0x00000158   Code   RO          624    .text.DDS_Test_ShowHelp  dds_test.o
    0x08001dd4   0x08001dd4   0x00000178   Code   RO          610    .text.DDS_Test_ShowStatus  dds_test.o
    0x08001f4c   0x08001f4c   0x00000042   Code   RO          620    .text.DDS_Test_Start  dds_test.o
    0x08001f8e   0x08001f8e   0x00000002   PAD
    0x08001f90   0x08001f90   0x0000002e   Code   RO          622    .text.DDS_Test_Stop  dds_test.o
    0x08001fbe   0x08001fbe   0x00000002   PAD
    0x08001fc0   0x08001fc0   0x00000184   Code   RO          630    .text.DDS_Test_TestDAC  dds_test.o
    0x08002144   0x08002144   0x0000000c   Code   RO          152    .text.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08002150   0x08002150   0x0000000c   Code   RO          162    .text.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x0800215c   0x0800215c   0x00000002   Code   RO          146    .text.DebugMon_Handler  stm32f4xx_it.o
    0x0800215e   0x0800215e   0x00000002   PAD
    0x08002160   0x08002160   0x00000006   Code   RO           15    .text.Error_Handler  main.o
    0x08002166   0x08002166   0x00000002   PAD
    0x08002168   0x08002168   0x00000048   Code   RO         1240    .text.FMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fmc.o
    0x080021b0   0x080021b0   0x000000aa   Code   RO         1234    .text.FMC_NORSRAM_Init  stm32f4xx_ll_fmc.o
    0x0800225a   0x0800225a   0x00000002   PAD
    0x0800225c   0x0800225c   0x00000054   Code   RO         1238    .text.FMC_NORSRAM_Timing_Init  stm32f4xx_ll_fmc.o
    0x080022b0   0x080022b0   0x00000002   Code   RO          725    .text.HAL_ADCEx_InjectedConvCpltCallback  stm32f4xx_hal_adc_ex.o
    0x080022b2   0x080022b2   0x00000002   PAD
    0x080022b4   0x080022b4   0x00000144   Code   RO          685    .text.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x080023f8   0x080023f8   0x0000002e   Code   RO          290    .text.HAL_ADC_ConvCpltCallback  adc_app.o
    0x08002426   0x08002426   0x00000002   PAD
    0x08002428   0x08002428   0x00000002   Code   RO          683    .text.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x0800242a   0x0800242a   0x00000002   PAD
    0x0800242c   0x0800242c   0x00000002   Code   RO          669    .text.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x0800242e   0x0800242e   0x00000002   PAD
    0x08002430   0x08002430   0x00000134   Code   RO          663    .text.HAL_ADC_IRQHandler  stm32f4xx_hal_adc.o
    0x08002564   0x08002564   0x00000152   Code   RO          643    .text.HAL_ADC_Init  stm32f4xx_hal_adc.o
    0x080026b6   0x080026b6   0x00000002   PAD
    0x080026b8   0x080026b8   0x00000002   Code   RO          667    .text.HAL_ADC_LevelOutOfWindowCallback  stm32f4xx_hal_adc.o
    0x080026ba   0x080026ba   0x00000002   PAD
    0x080026bc   0x080026bc   0x000000bc   Code   RO           38    .text.HAL_ADC_MspInit  adc.o
    0x08002778   0x08002778   0x00000178   Code   RO          671    .text.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x080028f0   0x080028f0   0x0000007a   Code   RO          679    .text.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x0800296a   0x0800296a   0x00000002   PAD
    0x0800296c   0x0800296c   0x00000002   Code   RO         1210    .text.HAL_DACEx_ConvCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x0800296e   0x0800296e   0x00000002   PAD
    0x08002970   0x08002970   0x00000002   Code   RO         1212    .text.HAL_DACEx_ConvHalfCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x08002972   0x08002972   0x00000002   PAD
    0x08002974   0x08002974   0x00000002   Code   RO         1214    .text.HAL_DACEx_ErrorCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x08002976   0x08002976   0x00000002   PAD
    0x08002978   0x08002978   0x00000060   Code   RO         1186    .text.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x080029d8   0x080029d8   0x00000002   Code   RO         1178    .text.HAL_DAC_ConvCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x080029da   0x080029da   0x00000002   PAD
    0x080029dc   0x080029dc   0x00000002   Code   RO         1180    .text.HAL_DAC_ConvHalfCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x080029de   0x080029de   0x00000002   PAD
    0x080029e0   0x080029e0   0x00000002   Code   RO         1182    .text.HAL_DAC_ErrorCallbackCh1  stm32f4xx_hal_dac.o
    0x080029e2   0x080029e2   0x00000002   PAD
    0x080029e4   0x080029e4   0x0000002e   Code   RO         1150    .text.HAL_DAC_Init  stm32f4xx_hal_dac.o
    0x08002a12   0x08002a12   0x00000002   PAD
    0x08002a14   0x08002a14   0x000000b6   Code   RO           54    .text.HAL_DAC_MspInit  dac.o
    0x08002aca   0x08002aca   0x00000002   PAD
    0x08002acc   0x08002acc   0x0000002e   Code   RO         1176    .text.HAL_DAC_SetValue  stm32f4xx_hal_dac.o
    0x08002afa   0x08002afa   0x00000002   PAD
    0x08002afc   0x08002afc   0x00000066   Code   RO         1158    .text.HAL_DAC_Start  stm32f4xx_hal_dac.o
    0x08002b62   0x08002b62   0x00000002   PAD
    0x08002b64   0x08002b64   0x00000104   Code   RO         1162    .text.HAL_DAC_Start_DMA  stm32f4xx_hal_dac.o
    0x08002c68   0x08002c68   0x0000005a   Code   RO         1170    .text.HAL_DAC_Stop_DMA  stm32f4xx_hal_dac.o
    0x08002cc2   0x08002cc2   0x00000002   PAD
    0x08002cc4   0x08002cc4   0x00000080   Code   RO          910    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x08002d44   0x08002d44   0x00000024   Code   RO          912    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002d68   0x08002d68   0x00000188   Code   RO          916    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002ef0   0x08002ef0   0x0000013e   Code   RO          902    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x0800302e   0x0800302e   0x00000002   PAD
    0x08003030   0x08003030   0x000000a0   Code   RO          908    .text.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x080030d0   0x080030d0   0x00000028   Code   RO         1075    .text.HAL_Delay     stm32f4xx_hal.o
    0x080030f8   0x080030f8   0x00000194   Code   RO          864    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x0800328c   0x0800328c   0x0000000a   Code   RO          868    .text.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08003296   0x08003296   0x00000002   PAD
    0x08003298   0x08003298   0x0000000a   Code   RO          870    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080032a2   0x080032a2   0x00000002   PAD
    0x080032a4   0x080032a4   0x0000000c   Code   RO         1067    .text.HAL_GetTick   stm32f4xx_hal.o
    0x080032b0   0x080032b0   0x0000001a   Code   RO         1065    .text.HAL_IncTick   stm32f4xx_hal.o
    0x080032ca   0x080032ca   0x00000002   PAD
    0x080032cc   0x080032cc   0x00000036   Code   RO         1055    .text.HAL_Init      stm32f4xx_hal.o
    0x08003302   0x08003302   0x00000002   PAD
    0x08003304   0x08003304   0x00000050   Code   RO         1057    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08003354   0x08003354   0x00000038   Code   RO          171    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x0800338c   0x0800338c   0x00000022   Code   RO         1007    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x080033ae   0x080033ae   0x00000002   PAD
    0x080033b0   0x080033b0   0x00000056   Code   RO         1005    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08003406   0x08003406   0x00000002   PAD
    0x08003408   0x08003408   0x00000020   Code   RO         1003    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08003428   0x08003428   0x0000007a   Code   RO          989    .text.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x080034a2   0x080034a2   0x00000002   PAD
    0x080034a4   0x080034a4   0x00000002   Code   RO          766    .text.HAL_RCC_CSSCallback  stm32f4xx_hal_rcc.o
    0x080034a6   0x080034a6   0x00000002   PAD
    0x080034a8   0x080034a8   0x00000160   Code   RO          744    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08003608   0x08003608   0x0000000c   Code   RO          750    .text.HAL_RCC_EnableCSS  stm32f4xx_hal_rcc.o
    0x08003614   0x08003614   0x00000026   Code   RO          756    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800363a   0x0800363a   0x00000002   PAD
    0x0800363c   0x0800363c   0x00000026   Code   RO          758    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08003662   0x08003662   0x00000002   PAD
    0x08003664   0x08003664   0x00000068   Code   RO          746    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080036cc   0x080036cc   0x0000001c   Code   RO          764    .text.HAL_RCC_NMI_IRQHandler  stm32f4xx_hal_rcc.o
    0x080036e8   0x080036e8   0x0000035c   Code   RO          742    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08003a44   0x08003a44   0x0000005c   Code   RO         1296    .text.HAL_SRAM_Init  stm32f4xx_hal_sram.o
    0x08003aa0   0x08003aa0   0x000000b8   Code   RO           79    .text.HAL_SRAM_MspInit  fmc.o
    0x08003b58   0x08003b58   0x0000002c   Code   RO         1015    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003b84   0x08003b84   0x000000bc   Code   RO         1639    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003c40   0x08003c40   0x0000005a   Code   RO         1344    .text.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08003c9a   0x08003c9a   0x00000002   PAD
    0x08003c9c   0x08003c9c   0x00000062   Code   RO           98    .text.HAL_TIM_Base_MspInit  tim.o
    0x08003cfe   0x08003cfe   0x00000002   PAD
    0x08003d00   0x08003d00   0x000000a0   Code   RO         1354    .text.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08003da0   0x08003da0   0x000000a8   Code   RO         1358    .text.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08003e48   0x08003e48   0x0000002a   Code   RO         1356    .text.HAL_TIM_Base_Stop  stm32f4xx_hal_tim.o
    0x08003e72   0x08003e72   0x00000002   PAD
    0x08003e74   0x08003e74   0x00000032   Code   RO         1360    .text.HAL_TIM_Base_Stop_IT  stm32f4xx_hal_tim.o
    0x08003ea6   0x08003ea6   0x00000002   PAD
    0x08003ea8   0x08003ea8   0x000001a0   Code   RO         1524    .text.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08004048   0x08004048   0x00000024   Code   RO         1518    .text.HAL_TIM_GenerateEvent  stm32f4xx_hal_tim.o
    0x0800406c   0x0800406c   0x00000068   Code   RO         1711    .text.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x080040d4   0x080040d4   0x00000072   Code   RO          487    .text.HAL_UARTEx_RxEventCallback  my_usart.o
    0x08004146   0x08004146   0x00000002   PAD
    0x08004148   0x08004148   0x000000d6   Code   RO         1705    .text.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x0800421e   0x0800421e   0x00000002   PAD
    0x08004220   0x08004220   0x00000002   Code   RO         1747    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08004222   0x08004222   0x00000002   PAD
    0x08004224   0x08004224   0x00000316   Code   RO         1741    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x0800453a   0x0800453a   0x00000002   PAD
    0x0800453c   0x0800453c   0x00000060   Code   RO         1663    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x0800459c   0x0800459c   0x0000011e   Code   RO          118    .text.HAL_UART_MspInit  usart.o
    0x080046ba   0x080046ba   0x00000002   PAD
    0x080046bc   0x080046bc   0x00000054   Code   RO         1685    .text.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08004710   0x08004710   0x000000fc   Code   RO          491    .text.HAL_UART_RxCpltCallback  my_usart.o
    0x0800480c   0x0800480c   0x00000002   Code   RO         1757    .text.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x0800480e   0x0800480e   0x00000002   PAD
    0x08004810   0x08004810   0x000000f4   Code   RO         1679    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004904   0x08004904   0x00000002   Code   RO         1751    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004906   0x08004906   0x00000002   PAD
    0x08004908   0x08004908   0x00000002   Code   RO          136    .text.HardFault_Handler  stm32f4xx_it.o
    0x0800490a   0x0800490a   0x00000002   PAD
    0x0800490c   0x0800490c   0x0000006c   Code   RO           36    .text.MX_ADC1_Init  adc.o
    0x08004978   0x08004978   0x00000062   Code   RO           52    .text.MX_DAC_Init   dac.o
    0x080049da   0x080049da   0x00000002   PAD
    0x080049dc   0x080049dc   0x0000005c   Code   RO           68    .text.MX_DMA_Init   dma.o
    0x08004a38   0x08004a38   0x00000072   Code   RO           77    .text.MX_FMC_Init   fmc.o
    0x08004aaa   0x08004aaa   0x00000002   PAD
    0x08004aac   0x08004aac   0x000001c0   Code   RO           27    .text.MX_GPIO_Init  gpio.o
    0x08004c6c   0x08004c6c   0x00000078   Code   RO           94    .text.MX_TIM3_Init  tim.o
    0x08004ce4   0x08004ce4   0x00000054   Code   RO           96    .text.MX_TIM6_Init  tim.o
    0x08004d38   0x08004d38   0x00000060   Code   RO          112    .text.MX_USART1_UART_Init  usart.o
    0x08004d98   0x08004d98   0x0000003c   Code   RO          114    .text.MX_USART2_UART_Init  usart.o
    0x08004dd4   0x08004dd4   0x0000003c   Code   RO          116    .text.MX_USART3_UART_Init  usart.o
    0x08004e10   0x08004e10   0x00000002   Code   RO          138    .text.MemManage_Handler  stm32f4xx_it.o
    0x08004e12   0x08004e12   0x00000002   PAD
    0x08004e14   0x08004e14   0x00000006   Code   RO          134    .text.NMI_Handler   stm32f4xx_it.o
    0x08004e1a   0x08004e1a   0x00000002   PAD
    0x08004e1c   0x08004e1c   0x0000003c   Code   RO          592    .text.PID_Init      app_pid.o
    0x08004e58   0x08004e58   0x00000002   Code   RO          148    .text.PendSV_Handler  stm32f4xx_it.o
    0x08004e5a   0x08004e5a   0x00000002   PAD
    0x08004e5c   0x08004e5c   0x00000002   Code   RO          144    .text.SVC_Handler   stm32f4xx_it.o
    0x08004e5e   0x08004e5e   0x00000002   PAD
    0x08004e60   0x08004e60   0x00000004   Code   RO          150    .text.SysTick_Handler  stm32f4xx_it.o
    0x08004e64   0x08004e64   0x000000bc   Code   RO           13    .text.SystemClock_Config  main.o
    0x08004f20   0x08004f20   0x00000012   Code   RO         1785    .text.SystemInit    system_stm32f4xx.o
    0x08004f32   0x08004f32   0x00000002   PAD
    0x08004f34   0x08004f34   0x0000012c   Code   RO         1348    .text.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08005060   0x08005060   0x0000000a   Code   RO         1745    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800506a   0x0800506a   0x00000002   PAD
    0x0800506c   0x0800506c   0x000000a2   Code   RO         1695    .text.UART_DMAError  stm32f4xx_hal_uart.o
    0x0800510e   0x0800510e   0x00000002   PAD
    0x08005110   0x08005110   0x00000080   Code   RO         1773    .text.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08005190   0x08005190   0x00000018   Code   RO         1775    .text.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x080051a8   0x080051a8   0x000000c0   Code   RO         1743    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08005268   0x08005268   0x000000de   Code   RO         1667    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x08005346   0x08005346   0x00000002   PAD
    0x08005348   0x08005348   0x000000aa   Code   RO         1699    .text.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080053f2   0x080053f2   0x00000002   PAD
    0x080053f4   0x080053f4   0x0000000c   Code   RO          156    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x08005400   0x08005400   0x0000000c   Code   RO          158    .text.USART2_IRQHandler  stm32f4xx_it.o
    0x0800540c   0x0800540c   0x0000000c   Code   RO          160    .text.USART3_IRQHandler  stm32f4xx_it.o
    0x08005418   0x08005418   0x00000002   Code   RO          142    .text.UsageFault_Handler  stm32f4xx_it.o
    0x0800541a   0x0800541a   0x00000002   PAD
    0x0800541c   0x0800541c   0x0000003a   Code   RO          288    .text.adc_tim_dma_init  adc_app.o
    0x08005456   0x08005456   0x00000002   PAD
    0x08005458   0x08005458   0x000000be   Code   RO         1851    .text.arm_bitreversal_f32  arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08005516   0x08005516   0x00000040   Code   RO         1828    .text.arm_cfft_radix4_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08005556   0x08005556   0x00000002   PAD
    0x08005558   0x08005558   0x00000094   Code   RO         1842    .text.arm_cfft_radix4_init_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x080055ec   0x080055ec   0x00000154   Code   RO         1809    .text.arm_cmplx_mag_f32  arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08005740   0x08005740   0x00000098   Code   RO         1800    .text.arm_cos_f32   arm_cortexM4lf_math.lib(arm_cos_f32.o)
    0x080057d8   0x080057d8   0x0000035a   Code   RO         1832    .text.arm_radix4_butterfly_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08005b32   0x08005b32   0x00000002   PAD
    0x08005b34   0x08005b34   0x0000037a   Code   RO         1830    .text.arm_radix4_butterfly_inverse_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08005eae   0x08005eae   0x00000002   PAD
    0x08005eb0   0x08005eb0   0x000001a8   Code   RO          378    .text.calculate_fft_spectrum  my_fft.o
    0x08006058   0x08006058   0x00000150   Code   RO          390    .text.calculate_sinad  my_fft.o
    0x080061a8   0x080061a8   0x00000398   Code   RO          386    .text.calculate_thd  my_fft.o
    0x08006540   0x08006540   0x00000168   Code   RO          570    .text.circuit_learning_task  scheduler.o
    0x080066a8   0x080066a8   0x00000094   Code   RO          307    .text.dac_app_init  dac_app.o
    0x0800673c   0x0800673c   0x0000003e   Code   RO          315    .text.dac_app_set_waveform  dac_app.o
    0x0800677a   0x0800677a   0x00000002   PAD
    0x0800677c   0x0800677c   0x00000074   Code   RO          376    .text.fft_init      my_fft.o
    0x080067f0   0x080067f0   0x000002c4   Code   RO          447    .text.find_peaks    wave_recognition.o
    0x08006ab4   0x08006ab4   0x00001164   Code   RO          311    .text.generate_waveform  dac_app.o
    0x08007c18   0x08007c18   0x0000000e   Code   RO          240    .text.get_current_ad_frequency  key_app.o
    0x08007c26   0x08007c26   0x00000002   PAD
    0x08007c28   0x08007c28   0x000001d8   Code   RO          242    .text.key_proc      key_app.o
    0x08007e00   0x08007e00   0x00000118   Code   RO           11    .text.main          main.o
    0x08007f18   0x08007f18   0x0000003e   Code   RO          489    .text.my_printf     my_usart.o
    0x08007f56   0x08007f56   0x00000002   PAD
    0x08007f58   0x08007f58   0x0000054c   Code   RO          380    .text.output_fft_spectrum  my_fft.o
    0x080084a4   0x080084a4   0x00000444   Code   RO          493    .text.process_uart1_command  my_usart.o
    0x080088e8   0x080088e8   0x00000270   Code   RO          495    .text.process_uart2_command  my_usart.o
    0x08008b58   0x08008b58   0x00000b68   Code   RO          455    .text.recognize_waveform  wave_recognition.o
    0x080096c0   0x080096c0   0x0000000e   Code   RO          574    .text.scheduler_init  scheduler.o
    0x080096ce   0x080096ce   0x00000002   PAD
    0x080096d0   0x080096d0   0x0000004a   Code   RO          576    .text.scheduler_run  scheduler.o
    0x0800971a   0x0800971a   0x00000002   PAD
    0x0800971c   0x0800971c   0x000000d8   Code   RO          572    .text.start_circuit_learning  scheduler.o
    0x080097f4   0x080097f4   0x0000010a   Code   RO          313    .text.start_dac_dma  dac_app.o
    0x080098fe   0x080098fe   0x00000002   PAD
    0x08009900   0x08009900   0x00000028   Code   RO         2276    i.__0sprintf        mc_w.l(printfa.o)
    0x08009928   0x08009928   0x00000034   Code   RO         2279    i.__0vsnprintf      mc_w.l(printfa.o)
    0x0800995c   0x0800995c   0x00000026   Code   RO         2344    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08009982   0x08009982   0x00000002   PAD
    0x08009984   0x08009984   0x00000008   Code   RO         2406    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x0800998c   0x0800998c   0x00000150   Code   RO         1984    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x08009adc   0x08009adc   0x00000180   Code   RO         1990    i.__hardfp_log10f   m_wm.l(log10f.o)
    0x08009c5c   0x08009c5c   0x0000009a   Code   RO         2016    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x08009cf6   0x08009cf6   0x00000002   PAD
    0x08009cf8   0x08009cf8   0x00000190   Code   RO         2004    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x08009e88   0x08009e88   0x0000003a   Code   RO         2010    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08009ec2   0x08009ec2   0x00000002   PAD
    0x08009ec4   0x08009ec4   0x00000014   Code   RO         2346    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08009ed8   0x08009ed8   0x00000006   Code   RO         2347    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08009ede   0x08009ede   0x00000002   PAD
    0x08009ee0   0x08009ee0   0x00000010   Code   RO         2349    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08009ef0   0x08009ef0   0x00000010   Code   RO         2352    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08009f00   0x08009f00   0x00000154   Code   RO         2363    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x0800a054   0x0800a054   0x0000000e   Code   RO         2474    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800a062   0x0800a062   0x00000002   Code   RO         2475    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800a064   0x0800a064   0x0000000e   Code   RO         2476    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800a072   0x0800a072   0x00000002   PAD
    0x0800a074   0x0800a074   0x0000000c   Code   RO         2408    i.__set_errno       mc_w.l(errno.o)
    0x0800a080   0x0800a080   0x00000184   Code   RO         2281    i._fp_digits        mc_w.l(printfa.o)
    0x0800a204   0x0800a204   0x000006dc   Code   RO         2282    i._printf_core      mc_w.l(printfa.o)
    0x0800a8e0   0x0800a8e0   0x00000024   Code   RO         2283    i._printf_post_padding  mc_w.l(printfa.o)
    0x0800a904   0x0800a904   0x0000002e   Code   RO         2284    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800a932   0x0800a932   0x00000016   Code   RO         2285    i._snputc           mc_w.l(printfa.o)
    0x0800a948   0x0800a948   0x0000000a   Code   RO         2286    i._sputc            mc_w.l(printfa.o)
    0x0800a952   0x0800a952   0x00000002   PAD
    0x0800a954   0x0800a954   0x00000040   Data   RO         1993    .constdata          m_wm.l(log10f.o)
    0x0800a994   0x0800a994   0x00000020   Data   RO         2364    .constdata          m_wm.l(rredf.o)
    0x0800a9b4   0x0800a9b4   0x00000081   Data   RO         2438    .constdata          mc_w.l(ctype_o.o)
    0x0800aa35   0x0800aa35   0x00000003   PAD
    0x0800aa38   0x0800aa38   0x00000004   Data   RO         2439    .constdata          mc_w.l(ctype_o.o)
    0x0800aa3c   0x0800aa3c   0x00000010   Data   RO         1790    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x0800aa4c   0x0800aa4c   0x00000008   Data   RO         1791    .rodata.APBPrescTable  system_stm32f4xx.o
    0x0800aa54   0x0800aa54   0x00000800   Data   RO         1865    .rodata.armBitRevTable  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800b254   0x0800b254   0x00000008   Data   RO          926    .rodata.cst8        stm32f4xx_hal_dma.o
    0x0800b25c   0x0800b25c   0x00000804   Data   RO         1977    .rodata.sinTable_f32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800ba60   0x0800ba60   0x00000050   Data   RO          246    .rodata.str1.1      key_app.o
    0x0800bab0   0x0800bab0   0x0000009c   Data   RO          363    .rodata.str1.1      software_dds.o
    0x0800bb4c   0x0800bb4c   0x0000009c   Data   RO          396    .rodata.str1.1      my_fft.o
    0x0800bbe8   0x0800bbe8   0x00000095   Data   RO          457    .rodata.str1.1      wave_recognition.o
    0x0800bc7d   0x0800bc7d   0x000003cd   Data   RO          506    .rodata.str1.1      my_usart.o
    0x0800c04a   0x0800c04a   0x000004d1   Data   RO          632    .rodata.str1.1      dds_test.o
    0x0800c51b   0x0800c51b   0x00000001   PAD
    0x0800c51c   0x0800c51c   0x00008000   Data   RO         1883    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801451c   0x0801451c   0x00000020   Data   RO         2473    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08014598, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08014540, Size: 0x00008b08, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08014540   0x00000004   Data   RW         2409    .data               mc_w.l(errno.o)
    0x20000004   0x08014544   0x00000008   Data   RW          328    .data..L_MergedGlobals  dac_app.o
    0x2000000c   0x0801454c   0x00000008   Data   RW          633    .data..L_MergedGlobals  dds_test.o
    0x20000014   0x08014554   0x00000004   Data   RW         1789    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000018   0x08014558   0x00000004   Data   RW          245    .data.current_ad_freq  key_app.o
    0x2000001c   0x0801455c   0x00000004   Data   RW          579    .data.current_freq  scheduler.o
    0x20000020   0x08014560   0x0000000c   Data   RW          362    .data.dds_config    software_dds.o
    0x2000002c   0x0801456c   0x00000024   Data   RW          582    .data.scheduler_task  scheduler.o
    0x20000050   0x08014590   0x00000001   Data   RW         1114    .data.uwTickFreq    stm32f4xx_hal.o
    0x20000051   0x08014591   0x00000003   PAD
    0x20000054   0x08014594   0x00000004   Data   RW         1113    .data.uwTickPrio    stm32f4xx_hal.o
    0x20000058        -       0x00000020   Zero   RW          210    .bss..L_MergedGlobals  da_output.o
    0x20000078        -       0x00000008   Zero   RW          247    .bss..L_MergedGlobals  key_app.o
    0x20000080        -       0x00000018   Zero   RW          365    .bss..L_MergedGlobals  software_dds.o
    0x20000098        -       0x00000008   Zero   RW          509    .bss..L_MergedGlobals  my_usart.o
    0x200000a0        -       0x00000008   Zero   RW          583    .bss..L_MergedGlobals  scheduler.o
    0x200000a8        -       0x00000028   Zero   RW          599    .bss..L_MergedGlobals  app_pid.o
    0x200000d0        -       0x00000002   Zero   RW          329    .bss..L_MergedGlobals.1  dac_app.o
    0x200000d2   0x08014598   0x00000002   PAD
    0x200000d4        -       0x00000005   Zero   RW          634    .bss..L_MergedGlobals.79  dds_test.o
    0x200000d9        -       0x00000001   Zero   RW          294    .bss.AdcConvEnd     adc_app.o
    0x200000da   0x08014598   0x00000002   PAD
    0x200000dc        -       0x00000001   Zero   RW           84    .bss.FMC_Initialized  fmc.o
    0x200000dd   0x08014598   0x00000003   PAD
    0x200000e0        -       0x00002000   Zero   RW          295    .bss.adc_val_buffer  adc_app.o
    0x200020e0        -       0x00002000   Zero   RW          394    .bss.fft_input_buffer  my_fft.o
    0x200040e0        -       0x00000014   Zero   RW          393    .bss.fft_instance   my_fft.o
    0x200040f4        -       0x00001000   Zero   RW          395    .bss.fft_magnitude  my_fft.o
    0x200050f4        -       0x00001000   Zero   RW          190    .bss.fifo_data1_f   ad_measure.o
    0x200060f4        -       0x00000048   Zero   RW           42    .bss.hadc1          adc.o
    0x2000613c        -       0x00000014   Zero   RW           58    .bss.hdac           dac.o
    0x20006150        -       0x00000060   Zero   RW           43    .bss.hdma_adc1      adc.o
    0x200061b0        -       0x00000060   Zero   RW           59    .bss.hdma_dac1      dac.o
    0x20006210        -       0x00000060   Zero   RW          123    .bss.hdma_usart1_rx  usart.o
    0x20006270        -       0x00000050   Zero   RW           83    .bss.hsram2         fmc.o
    0x200062c0        -       0x00000048   Zero   RW          102    .bss.htim3          tim.o
    0x20006308        -       0x00000048   Zero   RW          103    .bss.htim6          tim.o
    0x20006350        -       0x00000048   Zero   RW          122    .bss.huart1         usart.o
    0x20006398        -       0x00000048   Zero   RW          124    .bss.huart2         usart.o
    0x200063e0        -       0x00000048   Zero   RW          125    .bss.huart3         usart.o
    0x20006428        -       0x00000080   Zero   RW          505    .bss.rxBuffer2      my_usart.o
    0x200064a8        -       0x00000080   Zero   RW          504    .bss.rxBuffer3      my_usart.o
    0x20006528        -       0x00000800   Zero   RW          364    .bss.sine_table     software_dds.o
    0x20006d28        -       0x000007d0   Zero   RW          580    .bss.spectrum_buffer  scheduler.o
    0x200074f8        -       0x00000001   Zero   RW          581    .bss.task_num       scheduler.o
    0x200074f9   0x08014598   0x00000003   PAD
    0x200074fc        -       0x00000080   Zero   RW          501    .bss.uart1_cmd_buffer  my_usart.o
    0x2000757c        -       0x00000001   Zero   RW          500    .bss.uart1_cmd_flag  my_usart.o
    0x2000757d   0x08014598   0x00000003   PAD
    0x20007580        -       0x00000080   Zero   RW          502    .bss.uart1_dma_buffer  my_usart.o
    0x20007600        -       0x00000004   Zero   RW         1115    .bss.uwTick         stm32f4xx_hal.o
    0x20007604        -       0x00000100   Zero   RW          327    .bss.waveform_buffer  dac_app.o
    0x20007704        -       0x00001000   Zero   RW          392    .bss.window_buffer  my_fft.o
    0x20008704   0x08014598   0x00000004   PAD
    0x20008708        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0       4096       8151   ad_measure.o
       296          0          0          0        168       7366   adc.o
       104          0          0          0       8193       5284   adc_app.o
        60          0          0          0         40       1703   app_pid.o
        44          0          0          0          0       2704   cmd_to_fun.o
       508         16          0          0         32       3547   da_output.o
       280          0          0          0        116       4856   dac.o
      4928         24          0          8        258      17024   dac_app.o
      3288        892       1233          8          5      10345   dds_test.o
        92          0          0          0          0       3726   dma.o
       298          0          0          0         81       5883   fmc.o
       448          0          0          0          0       2502   gpio.o
       486        136         80          4          8       5250   key_app.o
       474         84          0          0          0       3998   main.o
      3152        244        156          0      16404      14245   my_fft.o
      2144        288        973          0        521       7033   my_usart.o
       664        260          0         40       2009       5699   scheduler.o
      1520        252        156         12       2072       9146   software_dds.o
        36          8        428          0       1024        856   startup_stm32f429xx.o
       212          0          0          5          4       7711   stm32f4xx_hal.o
      1606          0          0          0          0      11593   stm32f4xx_hal_adc.o
         2          0          0          0          0       9476   stm32f4xx_hal_adc_ex.o
       196          0          0          0          0      10894   stm32f4xx_hal_cortex.o
       692          0          0          0          0       7862   stm32f4xx_hal_dac.o
        52          0          0          0          0       5705   stm32f4xx_hal_dac_ex.o
      1034          4          8          0          0       9676   stm32f4xx_hal_dma.o
       424          0          0          0          0       5153   stm32f4xx_hal_gpio.o
        56          0          0          0          0       1516   stm32f4xx_hal_msp.o
       122          0          0          0          0       4610   stm32f4xx_hal_pwr_ex.o
      1434          0          0          0          0       7377   stm32f4xx_hal_rcc.o
        92          0          0          0          0      10414   stm32f4xx_hal_sram.o
      1262          0          0          0          0      57770   stm32f4xx_hal_tim.o
       188          0          0          0          0      22058   stm32f4xx_hal_tim_ex.o
      2446          0          0          0          0      26096   stm32f4xx_hal_uart.o
        96          0          0          0          0       5592   stm32f4xx_it.o
       326          0          0          0          0      10904   stm32f4xx_ll_fmc.o
        18          0         24          4          0       2705   system_stm32f4xx.o
       302          0          0          0        144       5623   tim.o
       502          0          0          0        312       8000   usart.o
      3628        224        149          0          0      14925   wave_recognition.o

    ----------------------------------------------------------------------
     33660       <USER>       <GROUP>         84      35504     364978   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       148          0          1          3         17          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       190          0          0          0          0       2756   arm_bitreversal.o
      1812          0          0          0          0       9774   arm_cfft_radix4_f32.o
       148         20          0          0          0       1573   arm_cfft_radix4_init_f32.o
       340          0          0          0          0       2270   arm_cmplx_mag_f32.o
         0          0      36868          0          0       6294   arm_common_tables.o
       152         12          0          0          0       1104   arm_cos_f32.o
       336         56          0          0          0        136   cosf.o
        38          0          0          0          0        116   fpclassifyf.o
        58         18          0          0          0        464   funder.o
       384         52         64          0          0        140   log10f.o
       154          0          0          0          0        140   roundf.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
        58          0          0          0          0        136   sqrtf.o
        28          0          0          0          0         68   _chval.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        20         10          0          4          0        136   errno.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2350         92          0          0          0        668   printfa.o
        20          0          0          0          0         68   strchr.o
        28          0          0          0          0         76   strcmp.o
        30          0          0          0          0         80   strncmp.o
        36          0          0          0          0         80   strstr.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
        54          0          0          0          0         80   dcmpge.o
        54          0          0          0          0         80   dcmple.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        44          0          0          0          0         68   ffixul.o
        60          0          0          0          0         76   frnd.o

    ----------------------------------------------------------------------
      9260        <USER>      <GROUP>          4          0      28919   Library Totals
        20          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2642         32      36868          0          0      23771   arm_cortexM4lf_math.lib
      1768        206         96          0          0       1504   m_wm.l
      3252        124        133          4          0       2204   mc_w.l
      1578          0          0          0          0       1440   mf_w.l

    ----------------------------------------------------------------------
      9260        <USER>      <GROUP>          4          0      28919   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     42920       2794      40340         88      35504     390269   Grand Totals
     42920       2794      40340         88      35504     390269   ELF Image Totals
     42920       2794      40340         88          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                83260 (  81.31kB)
    Total RW  Size (RW Data + ZI Data)             35592 (  34.76kB)
    Total ROM Size (Code + RO Data + RW Data)      83348 (  81.39kB)

==============================================================================


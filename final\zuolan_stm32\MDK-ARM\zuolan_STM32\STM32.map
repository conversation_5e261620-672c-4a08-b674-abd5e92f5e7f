Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS) for HAL_RCC_EnableCSS
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to fmc.o(i.MX_FMC_Init) for MX_FMC_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to dac.o(i.MX_DAC_Init) for MX_DAC_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to cmd_to_fun.o(i.CTRL_INIT) for CTRL_INIT
    main.o(i.main) refers to adc_app.o(i.adc_tim_dma_init) for adc_tim_dma_init
    main.o(i.main) refers to da_output.o(i.DA_Init) for DA_Init
    main.o(i.main) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    main.o(i.main) refers to dac_app.o(i.dac_app_init) for dac_app_init
    main.o(i.main) refers to dac_app.o(i.dac_app_set_waveform) for dac_app_set_waveform
    main.o(i.main) refers to app_pid.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to my_fft.o(i.fft_init) for fft_init
    main.o(i.main) refers to software_dds.o(i.DDS_Init) for DDS_Init
    main.o(i.main) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    main.o(i.main) refers to dds_test.o(i.DDS_Test_Init) for DDS_Test_Init
    main.o(i.main) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to my_usart.o(i.Check_UART_Status) for Check_UART_Status
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to my_usart.o(i.process_uart1_command) for process_uart1_command
    main.o(i.main) refers to my_usart.o(.data) for rxTemp2
    main.o(i.main) refers to usart.o(.bss) for huart2
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    dac.o(i.HAL_DAC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.HAL_DAC_MspInit) refers to dac.o(.bss) for .bss
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC_Init) refers to dac.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    fmc.o(i.HAL_FMC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.HAL_FMC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fmc.o(i.HAL_FMC_MspInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fmc.o(i.HAL_SRAM_MspDeInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspInit) refers to fmc.o(i.HAL_FMC_MspInit) for HAL_FMC_MspInit
    fmc.o(i.MX_FMC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.MX_FMC_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    fmc.o(i.MX_FMC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    fmc.o(i.MX_FMC_Init) refers to fmc.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to my_usart.o(.bss) for uart1_dma_buffer
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to dac.o(.bss) for hdma_dac1
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.NMI_Handler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) for HAL_RCC_NMI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    ad_measure.o(i.ad_proc) refers to ad_measure.o(i.vpp_adc_parallel) for vpp_adc_parallel
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(i.readFIFOData) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(i.setSamplingFrequency) refers to cmd_to_fun.o(i.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(i.setSamplingFrequency) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.setSamplingFrequency) for setSamplingFrequency
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE) for AD_FIFO_WRITE_ENABLE
    ad_measure.o(i.vpp_adc_parallel) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE) for AD_FIFO_WRITE_DISABLE
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.readFIFOData) for readFIFOData
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.findMinMax) for findMinMax
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.data) for .data
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.bss) for .bss
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(i.DA_Apply_Settings) refers to ffixul.o(.text) for __aeabi_f2ulz
    da_output.o(i.DA_Apply_Settings) refers to uldiv.o(.text) for __aeabi_uldivmod
    da_output.o(i.DA_Apply_Settings) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_START) for DA_FPGA_START
    da_output.o(i.DA_Apply_Settings) refers to da_output.o(.bss) for .bss
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.DA_SetConfig) refers to da_output.o(.bss) for .bss
    da_output.o(i.wave_test) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.wave_test) refers to stm32f4xx_hal.o(.data) for uwTick
    da_output.o(i.wave_test) refers to da_output.o(.data) for .data
    da_output.o(i.wave_test) refers to da_output.o(.bss) for .bss
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE) for AD_FREQ_CLR_ENABLE
    freq_measure.o(i.fre_measure) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE) for AD_FREQ_CLR_DISABLE
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_START) for AD_FREQ_START
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_STOP) for AD_FREQ_STOP
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad1) for fre_measure_ad1
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad2) for fre_measure_ad2
    freq_measure.o(i.freq_proc) refers to f2d.o(.text) for __aeabi_f2d
    freq_measure.o(i.freq_proc) refers to my_usart.o(i.my_printf) for my_printf
    freq_measure.o(i.freq_proc) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.get_current_ad_frequency) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_proc) refers to my_usart.o(i.my_printf) for my_printf
    key_app.o(i.key_proc) refers to my_fft.o(i.calculate_fft_spectrum) for calculate_fft_spectrum
    key_app.o(i.key_proc) refers to my_fft.o(i.output_fft_spectrum) for output_fft_spectrum
    key_app.o(i.key_proc) refers to dac_app.o(i.dac_app_set_waveform) for dac_app_set_waveform
    key_app.o(i.key_proc) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    key_app.o(i.key_proc) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.key_proc) refers to ad_measure.o(.bss) for fifo_data1_f
    key_app.o(i.key_proc) refers to da_output.o(.bss) for da_channels
    key_app.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.set_current_ad_frequency) refers to key_app.o(.data) for .data
    ad9833.o(i.AD9833_Init) refers to memseta.o(.text) for __aeabi_memclr4
    ad9833.o(i.AD9833_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9833.o(i.AD9833_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_Init2) refers to memseta.o(.text) for __aeabi_memclr4
    ad9833.o(i.AD9833_Init2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9833.o(i.AD9833_Init2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_SetFrequency) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_SetFrequency) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_SetFrequency) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_SetFrequency) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_SetFrequency2) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_SetFrequency2) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_SetFrequency2) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_SetFrequency2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_SetPhase) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_SetPhase2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_SetWave) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_SetWave2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_WriteData) for AD9833_WriteData
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_SetFrequency) for AD9833_SetFrequency
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_SetPhase) for AD9833_SetPhase
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_SetWave) for AD9833_SetWave
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_WriteData2) for AD9833_WriteData2
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_SetFrequency2) for AD9833_SetFrequency2
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_SetPhase2) for AD9833_SetPhase2
    ad9833.o(i.AD9833_Setup2) refers to ad9833.o(i.AD9833_SetWave2) for AD9833_SetWave2
    ad9833.o(i.AD9833_WriteData) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_WriteData) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    ad9833.o(i.AD9833_WriteData2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_WriteData2) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for hadc1
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to my_usart.o(i.my_printf) for my_printf
    adc_app.o(i.adc_task) refers to memseta.o(.text) for __aeabi_memclr4
    adc_app.o(i.adc_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_task) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_task) refers to usart.o(.bss) for huart1
    adc_app.o(i.adc_task) refers to adc.o(.bss) for hadc1
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_tim_dma_init) refers to tim.o(.bss) for htim3
    adc_app.o(i.adc_tim_dma_init) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_tim_dma_init) refers to adc.o(.bss) for hadc1
    dac_app.o(i.dac_app_get_actual_frequency) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(i.dac_app_get_actual_frequency) refers to tim.o(.bss) for htim6
    dac_app.o(i.dac_app_get_amplitude) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_get_zero_based) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.dac_app_set_amplitude) for dac_app_set_amplitude
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_init) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_sine) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    dac_app.o(i.generate_sine) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_sine) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_square) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_square) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_triangle) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_triangle) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_sine) for generate_sine
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_square) for generate_square
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_triangle) for generate_triangle
    dac_app.o(i.generate_waveform) refers to dac_app.o(.data) for .data
    dac_app.o(i.start_dac_dma) refers to dac_app.o(i.update_timer_frequency) for update_timer_frequency
    dac_app.o(i.start_dac_dma) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) for HAL_DAC_Start_DMA
    dac_app.o(i.start_dac_dma) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    dac_app.o(i.start_dac_dma) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.start_dac_dma) refers to dac.o(.bss) for hdac
    dac_app.o(i.start_dac_dma) refers to tim.o(.bss) for htim6
    dac_app.o(i.stop_dac_dma) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(i.stop_dac_dma) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(i.stop_dac_dma) refers to dac.o(.bss) for hdac
    dac_app.o(i.stop_dac_dma) refers to tim.o(.bss) for htim6
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(i.update_timer_frequency) refers to uldiv.o(.text) for __aeabi_uldivmod
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent) for HAL_TIM_GenerateEvent
    dac_app.o(i.update_timer_frequency) refers to dac_app.o(.data) for .data
    dac_app.o(i.update_timer_frequency) refers to tim.o(.bss) for htim6
    software_dds.o(i.DDS_CalculatePhaseIncrement) refers to uldiv.o(.text) for __aeabi_uldivmod
    software_dds.o(i.DDS_CalculatePhaseIncrement) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_GetConfig) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_GetInterruptStats) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Init) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    software_dds.o(i.DDS_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    software_dds.o(i.DDS_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(i.DDS_Init) refers to software_dds.o(i.DDS_CalculatePhaseIncrement) for DDS_CalculatePhaseIncrement
    software_dds.o(i.DDS_Init) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Init) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Init) refers to software_dds.o(.bss) for .bss
    software_dds.o(i.DDS_Init) refers to dac.o(.bss) for hdac
    software_dds.o(i.DDS_Init) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_OutputSample) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(i.DDS_OutputSample) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_OutputSample) refers to software_dds.o(.bss) for .bss
    software_dds.o(i.DDS_OutputSample) refers to dac.o(.bss) for hdac
    software_dds.o(i.DDS_SetAmplitude) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_SetAmplitude) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_SetAmplitude) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_SetFrequency) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_SetFrequency) refers to software_dds.o(i.DDS_CalculatePhaseIncrement) for DDS_CalculatePhaseIncrement
    software_dds.o(i.DDS_SetFrequency) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_SetFrequency) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_SetPhase) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_SetWaveform) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Setup) refers to software_dds.o(i.DDS_SetFrequency) for DDS_SetFrequency
    software_dds.o(i.DDS_Setup) refers to software_dds.o(i.DDS_SetAmplitude) for DDS_SetAmplitude
    software_dds.o(i.DDS_Setup) refers to software_dds.o(i.DDS_SetPhase) for DDS_SetPhase
    software_dds.o(i.DDS_Setup) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Setup) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Setup) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_Start) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    software_dds.o(i.DDS_Start) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Start) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Start) refers to tim.o(.bss) for htim6
    software_dds.o(i.DDS_Start) refers to usart.o(.bss) for huart2
    software_dds.o(i.DDS_Stop) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT) for HAL_TIM_Base_Stop_IT
    software_dds.o(i.DDS_Stop) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    software_dds.o(i.DDS_Stop) refers to my_usart.o(i.my_printf) for my_printf
    software_dds.o(i.DDS_Stop) refers to software_dds.o(.data) for .data
    software_dds.o(i.DDS_Stop) refers to tim.o(.bss) for htim6
    software_dds.o(i.DDS_Stop) refers to dac.o(.bss) for hdac
    software_dds.o(i.DDS_Stop) refers to usart.o(.bss) for huart2
    my_fft.o(i.calculate_fft_spectrum) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    my_fft.o(i.calculate_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_sinad) refers to my_fft.o(i.calculate_thd_n) for calculate_thd_n
    my_fft.o(i.calculate_sinad) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    my_fft.o(i.calculate_thd) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_thd_n) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd_n) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.fft_init) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    my_fft.o(i.fft_init) refers to my_fft.o(i.generate_hanning_window) for generate_hanning_window
    my_fft.o(i.fft_init) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.generate_hanning_window) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(i.generate_hanning_window) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.get_precise_peak_frequency) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.output_fft_spectrum) refers to key_app.o(i.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.output_fft_spectrum) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.get_precise_peak_frequency) for get_precise_peak_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.round_to_nearest_k) for round_to_nearest_k
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd) for calculate_thd
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd_n) for calculate_thd_n
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_sinad) for calculate_sinad
    my_fft.o(i.output_fft_spectrum) refers to usart.o(.bss) for huart1
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.round_to_nearest_k) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_filter.o(i.arm_fir_f32_lp) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for arm_fir_init_f32
    my_filter.o(i.arm_fir_f32_lp) refers to my_filter.o(.constdata) for .constdata
    phase_measure.o(i.calculate_phase_diff) refers to phase_measure.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman) refers to kalman.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(.bss) for .bss
    kalman.o(i.kalman_thd) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman_thd) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman_thd) refers to kalman.o(.data) for .data
    kalman.o(i.kalman_thd) refers to kalman.o(.bss) for .bss
    wave_recognition.o(i.calculate_carrier_suppression) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(i.calculate_carrier_suppression) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.calculate_carrier_suppression) refers to dfixi.o(.text) for __aeabi_d2iz
    wave_recognition.o(i.detect_symmetry) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(i.detect_symmetry) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.detect_symmetry) refers to dfixi.o(.text) for __aeabi_d2iz
    wave_recognition.o(i.find_peaks) refers to f2d.o(.text) for __aeabi_f2d
    wave_recognition.o(i.find_peaks) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.find_peaks) refers to d2f.o(.text) for __aeabi_d2f
    wave_recognition.o(i.preprocess_signal) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    wave_recognition.o(i.recognize_waveform) refers to strcpy.o(.text) for strcpy
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.preprocess_signal) for preprocess_signal
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.find_peaks) for find_peaks
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.calculate_bandwidth) for calculate_bandwidth
    wave_recognition.o(i.recognize_waveform) refers to dflti.o(.text) for __aeabi_i2d
    wave_recognition.o(i.recognize_waveform) refers to dmul.o(.text) for __aeabi_dmul
    wave_recognition.o(i.recognize_waveform) refers to f2d.o(.text) for __aeabi_f2d
    wave_recognition.o(i.recognize_waveform) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    wave_recognition.o(i.recognize_waveform) refers to printfa.o(i.__0sprintf) for __2sprintf
    wave_recognition.o(i.recognize_waveform) refers to memcpya.o(.text) for __aeabi_memcpy4
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.calculate_carrier_suppression) for calculate_carrier_suppression
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.detect_symmetry) for detect_symmetry
    wave_recognition.o(i.recognize_waveform) refers to cdcmple.o(.text) for __aeabi_cdcmple
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(.conststring) for .conststring
    my_hmi.o(i.HMI_Send_Float) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Float) refers to dflti.o(.text) for __aeabi_i2d
    my_hmi.o(i.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(i.HMI_Send_Float) refers to f2d.o(.text) for __aeabi_f2d
    my_hmi.o(i.HMI_Send_Float) refers to dmul.o(.text) for __aeabi_dmul
    my_hmi.o(i.HMI_Send_Float) refers to dfixi.o(.text) for __aeabi_d2iz
    my_hmi.o(i.HMI_Send_Float) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_Int) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Int) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_String) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_String) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Wave_Clear) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Wave_Clear) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hmi.o(i.HMI_Write_Wave_Low) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Low) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.Check_UART_Status) refers to strlen.o(.text) for strlen
    my_usart.o(i.Check_UART_Status) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.Check_UART_Status) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) for HAL_UART_DeInit
    my_usart.o(i.Check_UART_Status) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    my_usart.o(i.Check_UART_Status) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.Check_UART_Status) refers to usart.o(.bss) for huart1
    my_usart.o(i.Check_UART_Status) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart3
    my_usart.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.process_uart1_command) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.process_uart1_command) refers to strncmp.o(.text) for strncmp
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_FreqPlus) for DDS_Test_FreqPlus
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_FreqMinus) for DDS_Test_FreqMinus
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_Start) for DDS_Test_Start
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_Stop) for DDS_Test_Stop
    my_usart.o(i.process_uart1_command) refers to my_usart.o(.data) for .data
    my_usart.o(i.process_uart1_command) refers to usart.o(.bss) for huart1
    my_usart.o(i.process_uart1_command) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_SetMode) for DDS_Test_SetMode
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_ShowDebugInfo) for DDS_Test_ShowDebugInfo
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_TestDAC) for DDS_Test_TestDAC
    my_usart.o(i.process_uart1_command) refers to atoi.o(.text) for atoi
    my_usart.o(i.process_uart1_command) refers to dds_test.o(i.DDS_Test_SetAmplitude) for DDS_Test_SetAmplitude
    my_usart.o(i.process_uart1_command) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.process_uart2_command) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.process_uart2_command) refers to strstr.o(.text) for strstr
    my_usart.o(i.process_uart2_command) refers to scheduler.o(i.start_circuit_learning) for start_circuit_learning
    my_usart.o(i.process_uart2_command) refers to dds_test.o(i.DDS_Test_ProcessCommand) for DDS_Test_ProcessCommand
    my_usart.o(i.process_uart2_command) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.process_uart2_command) refers to my_usart.o(.data) for .data
    my_usart.o(i.process_uart2_command) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.process_uart2_command) refers to usart.o(.bss) for huart2
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.ParseFrame) refers to my_usart_pack.o(i.ParseDataToVariables) for ParseDataToVariables
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.SendFrame) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.data) for .data
    scheduler.o(i.circuit_learning_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.circuit_learning_task) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    scheduler.o(i.circuit_learning_task) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.circuit_learning_task) refers to wave_recognition.o(i.recognize_waveform) for recognize_waveform
    scheduler.o(i.circuit_learning_task) refers to memcpya.o(.text) for __aeabi_memcpy4
    scheduler.o(i.circuit_learning_task) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(i.circuit_learning_task) refers to scheduler.o(.data) for .data
    scheduler.o(i.circuit_learning_task) refers to usart.o(.bss) for huart2
    scheduler.o(i.circuit_learning_task) refers to scheduler.o(.bss) for .bss
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(i.start_circuit_learning) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.start_circuit_learning) refers to software_dds.o(i.DDS_Start) for DDS_Start
    scheduler.o(i.start_circuit_learning) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.start_circuit_learning) refers to scheduler.o(.data) for .data
    scheduler.o(i.start_circuit_learning) refers to usart.o(.bss) for huart2
    scheduler.o(i.uart_proc) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(i.uart_proc) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.uart_proc) refers to ad_measure.o(.data) for vol_amp2
    scheduler.o(i.uart_proc) refers to app_pid.o(.data) for output
    scheduler.o(i.uart_proc) refers to usart.o(.bss) for huart1
    scheduler.o(.data) refers to key_app.o(i.key_proc) for key_proc
    scheduler.o(.data) refers to scheduler.o(i.circuit_learning_task) for circuit_learning_task
    scheduler.o(.data) refers to my_usart.o(i.process_uart2_command) for process_uart2_command
    app_pid.o(i.PID_Init) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.Pid_Proc) refers to app_pid.o(i.increment_pid_ctrl) for increment_pid_ctrl
    app_pid.o(i.Pid_Proc) refers to ad_measure.o(.data) for vol_amp2
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.data) for .data
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.bss) for .bss
    dds_test.o(i.DDS_Test_FreqMinus) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_FreqMinus) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_FreqMinus) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_FreqMinus) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_FreqMinus) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_FreqPlus) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_FreqPlus) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_FreqPlus) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_FreqPlus) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_FreqPlus) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_Init) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_Init) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_Init) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ProcessCommand) refers to strchr.o(.text) for strchr
    dds_test.o(i.DDS_Test_ProcessCommand) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ProcessCommand) refers to strcmp.o(.text) for strcmp
    dds_test.o(i.DDS_Test_ProcessCommand) refers to strncmp.o(.text) for strncmp
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_SetMode) for DDS_Test_SetMode
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_FreqPlus) for DDS_Test_FreqPlus
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_FreqMinus) for DDS_Test_FreqMinus
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_Start) for DDS_Test_Start
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_Stop) for DDS_Test_Stop
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_ShowHelp) for DDS_Test_ShowHelp
    dds_test.o(i.DDS_Test_ProcessCommand) refers to atoi.o(.text) for atoi
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_SetAmplitude) for DDS_Test_SetAmplitude
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_ShowDebugInfo) for DDS_Test_ShowDebugInfo
    dds_test.o(i.DDS_Test_ProcessCommand) refers to dds_test.o(i.DDS_Test_TestDAC) for DDS_Test_TestDAC
    dds_test.o(i.DDS_Test_ProcessCommand) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_SetAmplitude) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_SetAmplitude) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_SetAmplitude) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_SetAmplitude) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_SetAmplitude) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_SetMode) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_SetMode) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_SetMode) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_SetMode) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_SetMode) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to software_dds.o(i.DDS_GetConfig) for DDS_GetConfig
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to software_dds.o(i.DDS_GetInterruptStats) for DDS_GetInterruptStats
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_ShowDebugInfo) refers to tim.o(.bss) for htim6
    dds_test.o(i.DDS_Test_ShowHelp) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ShowHelp) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_ShowHelp) refers to dds_test.o(.conststring) for .conststring
    dds_test.o(i.DDS_Test_ShowStatus) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_ShowStatus) refers to f2d.o(.text) for __aeabi_f2d
    dds_test.o(i.DDS_Test_ShowStatus) refers to software_dds.o(i.DDS_GetConfig) for DDS_GetConfig
    dds_test.o(i.DDS_Test_ShowStatus) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_ShowStatus) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_Start) refers to software_dds.o(i.DDS_Setup) for DDS_Setup
    dds_test.o(i.DDS_Test_Start) refers to software_dds.o(i.DDS_Start) for DDS_Start
    dds_test.o(i.DDS_Test_Start) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_Start) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_Start) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_Start) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_Stop) refers to software_dds.o(i.DDS_Stop) for DDS_Stop
    dds_test.o(i.DDS_Test_Stop) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_Stop) refers to dds_test.o(i.DDS_Test_ShowStatus) for DDS_Test_ShowStatus
    dds_test.o(i.DDS_Test_Stop) refers to dds_test.o(.data) for .data
    dds_test.o(i.DDS_Test_Stop) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_TestDAC) refers to my_usart.o(i.my_printf) for my_printf
    dds_test.o(i.DDS_Test_TestDAC) refers to memcpya.o(.text) for __aeabi_memcpy4
    dds_test.o(i.DDS_Test_TestDAC) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    dds_test.o(i.DDS_Test_TestDAC) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    dds_test.o(i.DDS_Test_TestDAC) refers to usart.o(.bss) for huart1
    dds_test.o(i.DDS_Test_TestDAC) refers to dds_test.o(.constdata) for .constdata
    dds_test.o(i.DDS_Test_TestDAC) refers to dac.o(.bss) for hdac
    dds_test.o(.constdata) refers to dds_test.o(.conststring) for .conststring
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) for FLASH_OB_DisablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) for FLASH_OB_BootConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) for FLASH_OB_EnablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to fmc.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to fmc.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) for SRAM_DMACpltProt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_fir_init_f32.o(.text.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    log10f.o(i.__hardfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f.o(i.__hardfp_log10f) refers to errno.o(i.__set_errno) for __set_errno
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    log10f.o(i.__hardfp_log10f) refers to log10f.o(.constdata) for .constdata
    log10f.o(i.__softfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__softfp_log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(i.log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f_x.o(i.____hardfp_log10f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log10f_x.o(i.____hardfp_log10f$lsc) refers to log10f_x.o(.constdata) for .constdata
    log10f_x.o(i.____softfp_log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____softfp_log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(i.__log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.__log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to errno.o(i.__set_errno) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (2 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (60 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.HAL_DAC_MspDeInit), (56 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing fmc.o(.rev16_text), (4 bytes).
    Removing fmc.o(.revsh_text), (4 bytes).
    Removing fmc.o(.rrx_text), (6 bytes).
    Removing fmc.o(i.HAL_SRAM_MspDeInit), (92 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(.rev16_text), (4 bytes).
    Removing ad_measure.o(.revsh_text), (4 bytes).
    Removing ad_measure.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(i.ad_proc), (24 bytes).
    Removing ad_measure.o(i.findMinMax), (48 bytes).
    Removing ad_measure.o(i.readFIFOData), (124 bytes).
    Removing ad_measure.o(i.setSamplingFrequency), (160 bytes).
    Removing ad_measure.o(i.vpp_adc_parallel), (292 bytes).
    Removing ad_measure.o(.data), (16 bytes).
    Removing da_output.o(.rev16_text), (4 bytes).
    Removing da_output.o(.revsh_text), (4 bytes).
    Removing da_output.o(.rrx_text), (6 bytes).
    Removing da_output.o(i.wave_test), (76 bytes).
    Removing da_output.o(.data), (8 bytes).
    Removing freq_measure.o(.rev16_text), (4 bytes).
    Removing freq_measure.o(.revsh_text), (4 bytes).
    Removing freq_measure.o(.rrx_text), (6 bytes).
    Removing freq_measure.o(i.fre_measure), (192 bytes).
    Removing freq_measure.o(i.fre_measure_ad1), (20 bytes).
    Removing freq_measure.o(i.fre_measure_ad2), (20 bytes).
    Removing freq_measure.o(i.freq_proc), (60 bytes).
    Removing freq_measure.o(.data), (24 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(i.set_current_ad_frequency), (12 bytes).
    Removing key_app.o(.data), (4 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing ad9833.o(.rrx_text), (6 bytes).
    Removing ad9833.o(i.AD9833_Delay), (12 bytes).
    Removing ad9833.o(i.AD9833_Init), (100 bytes).
    Removing ad9833.o(i.AD9833_Init2), (100 bytes).
    Removing ad9833.o(i.AD9833_SetFrequency), (96 bytes).
    Removing ad9833.o(i.AD9833_SetFrequency2), (96 bytes).
    Removing ad9833.o(i.AD9833_SetPhase), (6 bytes).
    Removing ad9833.o(i.AD9833_SetPhase2), (6 bytes).
    Removing ad9833.o(i.AD9833_SetWave), (10 bytes).
    Removing ad9833.o(i.AD9833_SetWave2), (10 bytes).
    Removing ad9833.o(i.AD9833_Setup), (104 bytes).
    Removing ad9833.o(i.AD9833_Setup2), (104 bytes).
    Removing ad9833.o(i.AD9833_WriteData), (128 bytes).
    Removing ad9833.o(i.AD9833_WriteData2), (120 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing adc_app.o(i.adc_task), (152 bytes).
    Removing adc_app.o(.bss), (4096 bytes).
    Removing adc_app.o(.data), (4 bytes).
    Removing dac_app.o(.rev16_text), (4 bytes).
    Removing dac_app.o(.revsh_text), (4 bytes).
    Removing dac_app.o(.rrx_text), (6 bytes).
    Removing dac_app.o(i.dac_app_get_actual_frequency), (52 bytes).
    Removing dac_app.o(i.dac_app_get_amplitude), (12 bytes).
    Removing dac_app.o(i.dac_app_get_zero_based), (12 bytes).
    Removing dac_app.o(i.dac_app_set_frequency), (32 bytes).
    Removing dac_app.o(i.dac_app_set_zero_based), (44 bytes).
    Removing software_dds.o(.rev16_text), (4 bytes).
    Removing software_dds.o(.revsh_text), (4 bytes).
    Removing software_dds.o(.rrx_text), (6 bytes).
    Removing software_dds.o(i.DDS_OutputSample), (196 bytes).
    Removing software_dds.o(i.DDS_SetWaveform), (12 bytes).
    Removing my_fft.o(.rev16_text), (4 bytes).
    Removing my_fft.o(.revsh_text), (4 bytes).
    Removing my_fft.o(.rrx_text), (6 bytes).
    Removing my_filter.o(.rev16_text), (4 bytes).
    Removing my_filter.o(.revsh_text), (4 bytes).
    Removing my_filter.o(.rrx_text), (6 bytes).
    Removing my_filter.o(i.arm_fir_f32_lp), (40 bytes).
    Removing my_filter.o(.constdata), (4 bytes).
    Removing my_filter.o(.constdata), (204 bytes).
    Removing phase_measure.o(.rev16_text), (4 bytes).
    Removing phase_measure.o(.revsh_text), (4 bytes).
    Removing phase_measure.o(.rrx_text), (6 bytes).
    Removing phase_measure.o(i.calculate_phase_diff), (208 bytes).
    Removing phase_measure.o(.data), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rrx_text), (6 bytes).
    Removing kalman.o(i.Kalman_init), (48 bytes).
    Removing kalman.o(i.kalman), (96 bytes).
    Removing kalman.o(i.kalman_filter), (82 bytes).
    Removing kalman.o(i.kalman_thd), (64 bytes).
    Removing kalman.o(.bss), (308 bytes).
    Removing kalman.o(.data), (4 bytes).
    Removing wave_recognition.o(.rev16_text), (4 bytes).
    Removing wave_recognition.o(.revsh_text), (4 bytes).
    Removing wave_recognition.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(.rev16_text), (4 bytes).
    Removing my_hmi.o(.revsh_text), (4 bytes).
    Removing my_hmi.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(i.HMI_Send_Float), (192 bytes).
    Removing my_hmi.o(i.HMI_Send_Int), (124 bytes).
    Removing my_hmi.o(i.HMI_Send_String), (124 bytes).
    Removing my_hmi.o(i.HMI_Wave_Clear), (124 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Fast), (184 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Low), (132 bytes).
    Removing my_usart.o(.rev16_text), (4 bytes).
    Removing my_usart.o(.revsh_text), (4 bytes).
    Removing my_usart.o(.rrx_text), (6 bytes).
    Removing my_usart.o(.bss), (128 bytes).
    Removing my_usart.o(.data), (2 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart_pack.o(.rev16_text), (4 bytes).
    Removing my_usart_pack.o(.revsh_text), (4 bytes).
    Removing my_usart_pack.o(.rrx_text), (6 bytes).
    Removing my_usart_pack.o(i.ParseDataToVariables), (160 bytes).
    Removing my_usart_pack.o(i.ParseFrame), (66 bytes).
    Removing my_usart_pack.o(i.PrepareFrame), (232 bytes).
    Removing my_usart_pack.o(i.SendFrame), (24 bytes).
    Removing my_usart_pack.o(i.SetParseTemplate), (52 bytes).
    Removing my_usart_pack.o(.bss), (52 bytes).
    Removing my_usart_pack.o(.data), (2 bytes).
    Removing cmd_to_fun.o(.rev16_text), (4 bytes).
    Removing cmd_to_fun.o(.revsh_text), (4 bytes).
    Removing cmd_to_fun.o(.rrx_text), (6 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_READ_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_READ_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE), (42 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_SET), (42 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_START), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_STOP), (30 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler.o(i.uart_proc), (76 bytes).
    Removing scheduler.o(.data), (4 bytes).
    Removing app_pid.o(.rev16_text), (4 bytes).
    Removing app_pid.o(.revsh_text), (4 bytes).
    Removing app_pid.o(.rrx_text), (6 bytes).
    Removing app_pid.o(i.Pid_Proc), (84 bytes).
    Removing app_pid.o(i.increment_pid_ctrl), (118 bytes).
    Removing app_pid.o(.data), (8 bytes).
    Removing dds_test.o(.rev16_text), (4 bytes).
    Removing dds_test.o(.revsh_text), (4 bytes).
    Removing dds_test.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (64 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (198 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (136 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (296 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (312 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (524 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (64 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (166 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (268 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (276 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (100 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (300 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (100 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (220 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (684 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (448 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (52 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (136 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (208 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (68 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (24 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (52 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (62 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (96 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (24 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (42 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (34 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (98 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2980 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (106 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (310 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (88 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (74 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (148 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive), (124 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode), (100 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (136 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (180 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (40 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (232 bytes).
    Removing stm32f4xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit), (32 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler), (112 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop), (34 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (32 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (120 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (36 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (78 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (78 bytes).
    Removing stm32f4xx_ll_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_AttributeSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_CommonSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_DeInit), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC), (110 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_Init), (76 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_AttributeSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_CommonSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_IOSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_Init), (36 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_GetModeStatus), (22 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init), (116 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand), (84 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SetAutoRefreshNumber), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init), (146 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Enable), (16 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (32 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (108 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (112 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (62 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (114 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACplt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMAError), (26 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (238 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (210 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (180 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (528 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (176 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (544 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (338 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (348 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (88 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (154 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (102 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (148 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (188 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (266 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (92 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (150 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (172 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (100 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (256 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (116 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (188 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (148 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_fir_init_f32.o(.text), (0 bytes).
    Removing arm_fir_init_f32.o(.text.arm_fir_init_f32), (32 bytes).
    Removing arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).

801 unused section(s) (total 932225 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f_x.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ..\MY_APP\app_pid.c                      0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\MY_APP\dds_test.c                     0x00000000   Number         0  dds_test.o ABSOLUTE
    ..\MY_APP\scheduler.c                    0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\MY_Algorithms\Src\kalman.c            0x00000000   Number         0  kalman.o ABSOLUTE
    ..\MY_Algorithms\Src\my_fft.c            0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\MY_Algorithms\Src\my_filter.c         0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\MY_Algorithms\Src\phase_measure.c     0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\MY_Algorithms\Src\wave_recognition.c  0x00000000   Number         0  wave_recognition.o ABSOLUTE
    ..\MY_Communication\Src\my_hmi.c         0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\MY_Communication\Src\my_usart.c       0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\MY_Communication\Src\my_usart_pack.c  0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\AD9833.c      0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\ad_measure.c  0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\adc_app.c     0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\da_output.c   0x00000000   Number         0  da_output.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\dac_app.c     0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\key_app.c     0x00000000   Number         0  key_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\software_dds.c 0x00000000   Number         0  software_dds.o ABSOLUTE
    ..\MY_Utilities\Src\cmd_to_fun.c         0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    ..\\MY_APP\\app_pid.c                    0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\\MY_APP\\dds_test.c                   0x00000000   Number         0  dds_test.o ABSOLUTE
    ..\\MY_APP\\scheduler.c                  0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\kalman.c         0x00000000   Number         0  kalman.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_fft.c         0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_filter.c      0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\phase_measure.c  0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\wave_recognition.c 0x00000000   Number         0  wave_recognition.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_hmi.c      0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart.c    0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart_pack.c 0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\AD9833.c   0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\ad_measure.c 0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\adc_app.c  0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\da_output.c 0x00000000   Number         0  da_output.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\dac_app.c  0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\key_app.c  0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\software_dds.c 0x00000000   Number         0  software_dds.o ABSOLUTE
    ..\\MY_Utilities\\Src\\cmd_to_fun.c      0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    arm_bitreversal.c                        0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    arm_cfft_radix4_f32.c                    0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    arm_cfft_radix4_init_f32.c               0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    arm_cmplx_mag_f32.c                      0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    arm_cos_f32.c                            0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    arm_fir_init_f32.c                       0x00000000   Number         0  arm_fir_init_f32.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f429xx.s                    0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c4   Section       36  startup_stm32f429xx.o(.text)
    $v0                                      0x080001c4   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x080001e8   Section        0  uldiv.o(.text)
    .text                                    0x0800024a   Section        0  memcpya.o(.text)
    .text                                    0x0800026e   Section        0  memseta.o(.text)
    .text                                    0x08000292   Section        0  strstr.o(.text)
    .text                                    0x080002b6   Section        0  strchr.o(.text)
    .text                                    0x080002ca   Section        0  strlen.o(.text)
    .text                                    0x080002d8   Section        0  strcmp.o(.text)
    .text                                    0x080002f4   Section        0  strcpy.o(.text)
    .text                                    0x08000306   Section        0  strncmp.o(.text)
    .text                                    0x08000324   Section        0  atoi.o(.text)
    .text                                    0x0800033e   Section        0  dmul.o(.text)
    .text                                    0x08000422   Section        0  ddiv.o(.text)
    .text                                    0x08000500   Section        0  dflti.o(.text)
    .text                                    0x08000522   Section        0  ffixul.o(.text)
    .text                                    0x0800054e   Section        0  dfixi.o(.text)
    .text                                    0x0800058c   Section        0  f2d.o(.text)
    .text                                    0x080005b4   Section       48  cdcmple.o(.text)
    .text                                    0x080005e4   Section       48  cdrcmple.o(.text)
    .text                                    0x08000614   Section        0  d2f.o(.text)
    .text                                    0x0800064c   Section        0  uidiv.o(.text)
    .text                                    0x08000678   Section        0  llshl.o(.text)
    .text                                    0x08000696   Section        0  llushr.o(.text)
    .text                                    0x080006b6   Section        0  strtol.o(.text)
    .text                                    0x08000726   Section        0  fepilogue.o(.text)
    .text                                    0x08000726   Section        0  iusefp.o(.text)
    .text                                    0x08000794   Section        0  frnd.o(.text)
    .text                                    0x080007d0   Section        0  depilogue.o(.text)
    .text                                    0x0800088a   Section        0  dadd.o(.text)
    .text                                    0x080009d8   Section        0  dfixul.o(.text)
    .text                                    0x08000a08   Section       36  init.o(.text)
    .text                                    0x08000a2c   Section        0  llsshr.o(.text)
    .text                                    0x08000a50   Section        0  ctype_o.o(.text)
    .text                                    0x08000a58   Section        0  _strtoul.o(.text)
    .text                                    0x08000af6   Section        0  _chval.o(.text)
    [Anonymous Symbol]                       0x08000b14   Section        0  arm_bitreversal.o(.text.arm_bitreversal_f32)
    [Anonymous Symbol]                       0x08000bd2   Section        0  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    [Anonymous Symbol]                       0x08000c14   Section        0  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    [Anonymous Symbol]                       0x08000ca8   Section        0  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    [Anonymous Symbol]                       0x08000dfc   Section        0  arm_cos_f32.o(.text.arm_cos_f32)
    [Anonymous Symbol]                       0x08000e94   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    [Anonymous Symbol]                       0x080011f0   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    i.ADC_DMAConvCplt                        0x0800156a   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x0800156b   Thumb Code   116  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x080015de   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x080015df   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x080015f4   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x080015f5   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_IRQHandler                         0x08001600   Section        0  stm32f4xx_it.o(i.ADC_IRQHandler)
    i.ADC_Init                               0x0800160c   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x0800160d   Thumb Code   298  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x0800173c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CTRL_INIT                              0x0800173e   Section        0  cmd_to_fun.o(i.CTRL_INIT)
    i.Check_UART_Status                      0x08001748   Section        0  my_usart.o(i.Check_UART_Status)
    i.DAC_DMAConvCpltCh1                     0x080017f8   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1)
    i.DAC_DMAConvCpltCh2                     0x08001808   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2)
    i.DAC_DMAErrorCh1                        0x08001818   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1)
    i.DAC_DMAErrorCh2                        0x08001830   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2)
    i.DAC_DMAHalfConvCpltCh1                 0x08001848   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1)
    i.DAC_DMAHalfConvCpltCh2                 0x08001852   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2)
    i.DA_Apply_Settings                      0x0800185c   Section        0  da_output.o(i.DA_Apply_Settings)
    i.DA_FPGA_START                          0x0800193c   Section        0  cmd_to_fun.o(i.DA_FPGA_START)
    i.DA_FPGA_STOP                           0x0800194a   Section        0  cmd_to_fun.o(i.DA_FPGA_STOP)
    i.DA_Init                                0x08001958   Section        0  da_output.o(i.DA_Init)
    i.DA_SetConfig                           0x08001990   Section        0  da_output.o(i.DA_SetConfig)
    i.DDS_CalculatePhaseIncrement            0x080019b0   Section        0  software_dds.o(i.DDS_CalculatePhaseIncrement)
    i.DDS_GetConfig                          0x080019cc   Section        0  software_dds.o(i.DDS_GetConfig)
    i.DDS_GetInterruptStats                  0x080019dc   Section        0  software_dds.o(i.DDS_GetInterruptStats)
    i.DDS_Init                               0x080019ec   Section        0  software_dds.o(i.DDS_Init)
    i.DDS_SetAmplitude                       0x08001acc   Section        0  software_dds.o(i.DDS_SetAmplitude)
    i.DDS_SetFrequency                       0x08001b28   Section        0  software_dds.o(i.DDS_SetFrequency)
    i.DDS_SetPhase                           0x08001bd4   Section        0  software_dds.o(i.DDS_SetPhase)
    i.DDS_Setup                              0x08001bf0   Section        0  software_dds.o(i.DDS_Setup)
    i.DDS_Start                              0x08001c5c   Section        0  software_dds.o(i.DDS_Start)
    i.DDS_Stop                               0x08001d24   Section        0  software_dds.o(i.DDS_Stop)
    i.DDS_Test_FreqMinus                     0x08001d6c   Section        0  dds_test.o(i.DDS_Test_FreqMinus)
    i.DDS_Test_FreqPlus                      0x08001e14   Section        0  dds_test.o(i.DDS_Test_FreqPlus)
    i.DDS_Test_Init                          0x08001ebc   Section        0  dds_test.o(i.DDS_Test_Init)
    i.DDS_Test_ProcessCommand                0x08001f8c   Section        0  dds_test.o(i.DDS_Test_ProcessCommand)
    i.DDS_Test_SetAmplitude                  0x08002170   Section        0  dds_test.o(i.DDS_Test_SetAmplitude)
    i.DDS_Test_SetMode                       0x080021b8   Section        0  dds_test.o(i.DDS_Test_SetMode)
    i.DDS_Test_ShowDebugInfo                 0x08002280   Section        0  dds_test.o(i.DDS_Test_ShowDebugInfo)
    i.DDS_Test_ShowHelp                      0x080024dc   Section        0  dds_test.o(i.DDS_Test_ShowHelp)
    i.DDS_Test_ShowStatus                    0x08002774   Section        0  dds_test.o(i.DDS_Test_ShowStatus)
    i.DDS_Test_Start                         0x08002910   Section        0  dds_test.o(i.DDS_Test_Start)
    i.DDS_Test_Stop                          0x08002954   Section        0  dds_test.o(i.DDS_Test_Stop)
    i.DDS_Test_TestDAC                       0x0800298c   Section        0  dds_test.o(i.DDS_Test_TestDAC)
    i.DMA1_Stream5_IRQHandler                0x08002a98   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream0_IRQHandler                0x08002aa4   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08002ab0   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08002ab1   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08002ae4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08002ae5   Thumb Code   126  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002b62   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002b63   Thumb Code    56  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08002b9a   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08002b9c   Section        0  main.o(i.Error_Handler)
    i.FMC_NORSRAM_Extended_Timing_Init       0x08002ba0   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    i.FMC_NORSRAM_Init                       0x08002bf0   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    i.FMC_NORSRAM_Timing_Init                0x08002c80   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x08002cea   Section        0  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADC_ConfigChannel                  0x08002cec   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08002e78   Section        0  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08002e94   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08002e96   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_IRQHandler                     0x08002e98   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x08002fda   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x08003030   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x08003034   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x080030dc   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x08003268   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DACEx_ConvCpltCallbackCh2          0x080032d8   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2)
    i.HAL_DACEx_ConvHalfCpltCallbackCh2      0x080032da   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2)
    i.HAL_DACEx_ErrorCallbackCh2             0x080032dc   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2)
    i.HAL_DAC_ConfigChannel                  0x080032de   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_ConvCpltCallbackCh1            0x08003340   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1)
    i.HAL_DAC_ConvHalfCpltCallbackCh1        0x08003342   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1)
    i.HAL_DAC_ErrorCallbackCh1               0x08003344   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1)
    i.HAL_DAC_Init                           0x08003346   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x08003370   Section        0  dac.o(i.HAL_DAC_MspInit)
    i.HAL_DAC_SetValue                       0x08003410   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    i.HAL_DAC_Start                          0x08003446   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    i.HAL_DAC_Start_DMA                      0x080034b8   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
    i.HAL_DAC_Stop_DMA                       0x080035c0   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA)
    i.HAL_DMA_Abort                          0x0800361c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080036be   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x080036e4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x080038d0   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080039bc   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08003a2c   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_FMC_MspInit                        0x08003a54   Section        0  fmc.o(i.HAL_FMC_MspInit)
    HAL_FMC_MspInit                          0x08003a55   Thumb Code   138  fmc.o(i.HAL_FMC_MspInit)
    i.HAL_GPIO_DeInit                        0x08003af8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    i.HAL_GPIO_Init                          0x08003c70   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08003ebc   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08003eca   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08003ed8   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08003ee4   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003ef4   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08003f28   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003f6c   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_DisableIRQ                    0x08003f9c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ)
    i.HAL_NVIC_EnableIRQ                     0x08003fc0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003fdc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08004044   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_EnableOverDrive              0x08004068   Section        0  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    i.HAL_RCC_CSSCallback                    0x080040e4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    i.HAL_RCC_ClockConfig                    0x080040e8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_EnableCSS                      0x08004264   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    i.HAL_RCC_GetHCLKFreq                    0x08004270   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x0800427c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800429c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080042bc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_NMI_IRQHandler                 0x0800432c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    i.HAL_RCC_OscConfig                      0x0800434c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SRAM_Init                          0x080047ce   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x0800482c   Section        0  fmc.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_Config                     0x08004830   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08004858   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080048f2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08004950   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x08004990   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Start_IT                  0x08004a10   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Base_Stop                      0x08004a9a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    i.HAL_TIM_Base_Stop_IT                   0x08004ac0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    i.HAL_TIM_ConfigClockSource              0x08004af0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_GenerateEvent                  0x08004bda   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08004c04   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08004c58   Section        0  my_usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08004cb8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_DeInit                        0x08004d28   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DeInit)
    i.HAL_UART_ErrorCallback                 0x08004d60   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08004d64   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004fe4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspDeInit                     0x0800504c   Section        0  usart.o(i.HAL_UART_MspDeInit)
    i.HAL_UART_MspInit                       0x080050c8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080051e4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08005200   Section        0  my_usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x080052c8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x080052ca   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08005382   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08005384   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_ADC1_Init                           0x08005388   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DAC_Init                            0x080053e8   Section        0  dac.o(i.MX_DAC_Init)
    i.MX_DMA_Init                            0x08005438   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FMC_Init                            0x08005484   Section        0  fmc.o(i.MX_FMC_Init)
    i.MX_GPIO_Init                           0x080054f8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM3_Init                           0x08005698   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM6_Init                           0x08005700   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_USART1_UART_Init                    0x08005748   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08005798   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x080057d0   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08005808   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800580a   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PID_Init                               0x08005810   Section        0  app_pid.o(i.PID_Init)
    i.PendSV_Handler                         0x08005860   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08005862   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08005864   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005868   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005908   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08005918   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x080059ec   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08005a02   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08005a03   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08005a12   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08005a13   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005a38   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005a39   Thumb Code    40  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08005a60   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005a61   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08005a6e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08005a6f   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08005ab8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08005ab9   Thumb Code   144  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08005b48   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08005b49   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08005b66   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005b67   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08005bb4   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08005bb5   Thumb Code    26  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08005bce   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08005bcf   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08005bea   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005beb   Thumb Code   202  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08005cb4   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08005cb5   Thumb Code   248  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08005db0   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08005e60   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x08005e98   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08005e99   Thumb Code    94  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08005ef6   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08005ef7   Thumb Code   142  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08005f84   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08005f90   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08005f9c   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08005fa8   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0sprintf                             0x08005fac   Section        0  printfa.o(i.__0sprintf)
    i.__0vsnprintf                           0x08005fd4   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassifyf                      0x08006008   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__aeabi_errno_addr                     0x08006030   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__hardfp_cosf                          0x08006038   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_log10f                        0x08006188   Section        0  log10f.o(i.__hardfp_log10f)
    i.__hardfp_roundf                        0x08006308   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sinf                          0x080063a4   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sqrtf                         0x08006534   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x08006570   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08006584   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x0800658c   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x0800659c   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x080065ac   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__scatterload_copy                     0x08006700   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800670e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08006710   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08006720   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x0800672c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800672d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080068b0   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080068b1   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08006f64   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08006f65   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08006f88   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08006f89   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08006fb6   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08006fb7   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x08006fcc   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08006fcd   Thumb Code    10  printfa.o(i._sputc)
    i.adc_tim_dma_init                       0x08006fd8   Section        0  adc_app.o(i.adc_tim_dma_init)
    i.calculate_bandwidth                    0x0800700c   Section        0  wave_recognition.o(i.calculate_bandwidth)
    i.calculate_carrier_suppression          0x08007094   Section        0  wave_recognition.o(i.calculate_carrier_suppression)
    i.calculate_fft_spectrum                 0x0800711c   Section        0  my_fft.o(i.calculate_fft_spectrum)
    i.calculate_sinad                        0x080071e0   Section        0  my_fft.o(i.calculate_sinad)
    i.calculate_thd                          0x08007218   Section        0  my_fft.o(i.calculate_thd)
    i.calculate_thd_n                        0x08007384   Section        0  my_fft.o(i.calculate_thd_n)
    i.circuit_learning_task                  0x08007474   Section        0  scheduler.o(i.circuit_learning_task)
    i.dac_app_init                           0x080075cc   Section        0  dac_app.o(i.dac_app_init)
    i.dac_app_set_amplitude                  0x080075ec   Section        0  dac_app.o(i.dac_app_set_amplitude)
    i.dac_app_set_waveform                   0x08007654   Section        0  dac_app.o(i.dac_app_set_waveform)
    i.detect_symmetry                        0x08007678   Section        0  wave_recognition.o(i.detect_symmetry)
    i.fft_init                               0x0800772c   Section        0  my_fft.o(i.fft_init)
    i.find_peaks                             0x08007748   Section        0  wave_recognition.o(i.find_peaks)
    i.generate_hanning_window                0x08007844   Section        0  my_fft.o(i.generate_hanning_window)
    i.generate_sine                          0x080078a4   Section        0  dac_app.o(i.generate_sine)
    generate_sine                            0x080078a5   Thumb Code   202  dac_app.o(i.generate_sine)
    i.generate_square                        0x08007980   Section        0  dac_app.o(i.generate_square)
    generate_square                          0x08007981   Thumb Code   116  dac_app.o(i.generate_square)
    i.generate_triangle                      0x080079fc   Section        0  dac_app.o(i.generate_triangle)
    generate_triangle                        0x080079fd   Thumb Code   292  dac_app.o(i.generate_triangle)
    i.generate_waveform                      0x08007b2c   Section        0  dac_app.o(i.generate_waveform)
    generate_waveform                        0x08007b2d   Thumb Code    28  dac_app.o(i.generate_waveform)
    i.get_current_ad_frequency               0x08007b4c   Section        0  key_app.o(i.get_current_ad_frequency)
    i.get_precise_peak_frequency             0x08007b58   Section        0  my_fft.o(i.get_precise_peak_frequency)
    i.key_proc                               0x08007c28   Section        0  key_app.o(i.key_proc)
    i.key_read                               0x08007dc0   Section        0  key_app.o(i.key_read)
    i.main                                   0x08007e04   Section        0  main.o(i.main)
    i.my_printf                              0x08007f14   Section        0  my_usart.o(i.my_printf)
    i.output_fft_spectrum                    0x08007f48   Section        0  my_fft.o(i.output_fft_spectrum)
    i.preprocess_signal                      0x08008244   Section        0  wave_recognition.o(i.preprocess_signal)
    preprocess_signal                        0x08008245   Thumb Code   162  wave_recognition.o(i.preprocess_signal)
    i.process_uart1_command                  0x080082f0   Section        0  my_usart.o(i.process_uart1_command)
    i.process_uart2_command                  0x08008a08   Section        0  my_usart.o(i.process_uart2_command)
    i.recognize_waveform                     0x08008c74   Section        0  wave_recognition.o(i.recognize_waveform)
    i.round_to_nearest_k                     0x08008fa0   Section        0  my_fft.o(i.round_to_nearest_k)
    i.scheduler_init                         0x08008fc4   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08008fd0   Section        0  scheduler.o(i.scheduler_run)
    i.start_circuit_learning                 0x08009010   Section        0  scheduler.o(i.start_circuit_learning)
    i.start_dac_dma                          0x080090e0   Section        0  dac_app.o(i.start_dac_dma)
    start_dac_dma                            0x080090e1   Thumb Code    44  dac_app.o(i.start_dac_dma)
    i.stop_dac_dma                           0x08009118   Section        0  dac_app.o(i.stop_dac_dma)
    stop_dac_dma                             0x08009119   Thumb Code    22  dac_app.o(i.stop_dac_dma)
    i.update_timer_frequency                 0x08009138   Section        0  dac_app.o(i.update_timer_frequency)
    update_timer_frequency                   0x08009139   Thumb Code   334  dac_app.o(i.update_timer_frequency)
    .constdata                               0x08009294   Section       32  dds_test.o(.constdata)
    .constdata                               0x080092b4   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x080092b4   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x080092bc   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x080092cc   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x080092d4   Section       64  log10f.o(.constdata)
    logahi                                   0x080092d4   Data          32  log10f.o(.constdata)
    logalo                                   0x080092f4   Data          32  log10f.o(.constdata)
    .constdata                               0x08009314   Section       32  rredf.o(.constdata)
    twooverpi                                0x08009314   Data          32  rredf.o(.constdata)
    .constdata                               0x08009334   Section      129  ctype_o.o(.constdata)
    .constdata                               0x080093b8   Section        4  ctype_o.o(.constdata)
    table                                    0x080093b8   Data           4  ctype_o.o(.constdata)
    .conststring                             0x080093bc   Section       70  wave_recognition.o(.conststring)
    .conststring                             0x08009404   Section      177  dds_test.o(.conststring)
    .data                                    0x20000000   Section        8  fmc.o(.data)
    FMC_Initialized                          0x20000000   Data           4  fmc.o(.data)
    FMC_DeInitialized                        0x20000004   Data           4  fmc.o(.data)
    .data                                    0x20000008   Section       12  key_app.o(.data)
    waveform_type                            0x2000000c   Data           1  key_app.o(.data)
    current_phase                            0x2000000e   Data           2  key_app.o(.data)
    current_ad_freq                          0x20000010   Data           4  key_app.o(.data)
    .data                                    0x20000014   Section        1  adc_app.o(.data)
    .data                                    0x20000018   Section       12  dac_app.o(.data)
    current_waveform                         0x20000018   Data           1  dac_app.o(.data)
    zero_based_waveform                      0x20000019   Data           1  dac_app.o(.data)
    current_peak_amplitude_mv                0x2000001a   Data           2  dac_app.o(.data)
    dac_amplitude_raw                        0x2000001c   Data           2  dac_app.o(.data)
    current_frequency_hz                     0x20000020   Data           4  dac_app.o(.data)
    .data                                    0x20000024   Section       36  software_dds.o(.data)
    table_initialized                        0x20000024   Data           1  software_dds.o(.data)
    phase_accumulator                        0x20000028   Data           4  software_dds.o(.data)
    phase_increment                          0x2000002c   Data           4  software_dds.o(.data)
    dds_debug_counter                        0x20000030   Data           4  software_dds.o(.data)
    dds_sample_counter                       0x20000034   Data           4  software_dds.o(.data)
    dds_interrupt_counter                    0x20000038   Data           4  software_dds.o(.data)
    dds_config                               0x2000003c   Data          12  software_dds.o(.data)
    .data                                    0x20000048   Section       16  my_usart.o(.data)
    call_counter                             0x20000054   Data           4  my_usart.o(.data)
    .data                                    0x20000058   Section       52  scheduler.o(.data)
    learning_active                          0x20000058   Data           1  scheduler.o(.data)
    learning_step                            0x20000059   Data           1  scheduler.o(.data)
    learning_start_time                      0x2000005c   Data           4  scheduler.o(.data)
    current_freq                             0x20000060   Data           4  scheduler.o(.data)
    step_start_time                          0x20000064   Data           4  scheduler.o(.data)
    scheduler_task                           0x20000068   Data          36  scheduler.o(.data)
    .data                                    0x2000008c   Section       12  dds_test.o(.data)
    test_config                              0x2000008c   Data          12  dds_test.o(.data)
    .data                                    0x20000098   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x200000a4   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x200000a8   Section        4  errno.o(.data)
    _errno                                   0x200000a8   Data           4  errno.o(.data)
    .bss                                     0x200000ac   Section      168  adc.o(.bss)
    .bss                                     0x20000154   Section      116  dac.o(.bss)
    .bss                                     0x200001c8   Section       80  fmc.o(.bss)
    .bss                                     0x20000218   Section      144  tim.o(.bss)
    .bss                                     0x200002a8   Section      312  usart.o(.bss)
    .bss                                     0x200003e0   Section    12288  ad_measure.o(.bss)
    .bss                                     0x200033e0   Section       24  da_output.o(.bss)
    .bss                                     0x200033f8   Section    12288  adc_app.o(.bss)
    .bss                                     0x200063f8   Section      256  dac_app.o(.bss)
    waveform_buffer                          0x200063f8   Data         256  dac_app.o(.bss)
    .bss                                     0x200064f8   Section     2048  software_dds.o(.bss)
    sine_table                               0x200064f8   Data        2048  software_dds.o(.bss)
    .bss                                     0x20006cf8   Section    16404  my_fft.o(.bss)
    .bss                                     0x2000ad0c   Section      512  my_usart.o(.bss)
    .bss                                     0x2000af0c   Section     4048  scheduler.o(.bss)
    spectrum_buffer                          0x2000af0c   Data        2000  scheduler.o(.bss)
    adc_buffer                               0x2000b6dc   Data        2048  scheduler.o(.bss)
    .bss                                     0x2000bedc   Section       36  app_pid.o(.bss)
    STACK                                    0x2000bf00   Section     1024  startup_stm32f429xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001c5   Thumb Code     8  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM7_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    __aeabi_uldivmod                         0x080001e9   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x0800024b   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800026f   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800027d   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800027d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800027d   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000281   Thumb Code    18  memseta.o(.text)
    strstr                                   0x08000293   Thumb Code    36  strstr.o(.text)
    strchr                                   0x080002b7   Thumb Code    20  strchr.o(.text)
    strlen                                   0x080002cb   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x080002d9   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x080002f5   Thumb Code    18  strcpy.o(.text)
    strncmp                                  0x08000307   Thumb Code    30  strncmp.o(.text)
    atoi                                     0x08000325   Thumb Code    26  atoi.o(.text)
    __aeabi_dmul                             0x0800033f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000423   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x08000501   Thumb Code    34  dflti.o(.text)
    __aeabi_f2ulz                            0x08000523   Thumb Code    44  ffixul.o(.text)
    __aeabi_d2iz                             0x0800054f   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x0800058d   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x080005b5   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x080005b5   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x080005e5   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x08000615   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x0800064d   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800064d   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x08000679   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000679   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000697   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000697   Thumb Code     0  llushr.o(.text)
    strtol                                   0x080006b7   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x08000727   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000727   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000739   Thumb Code    92  fepilogue.o(.text)
    _frnd                                    0x08000795   Thumb Code    60  frnd.o(.text)
    _double_round                            0x080007d1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080007ef   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x0800088b   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080009cd   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080009d3   Thumb Code     6  dadd.o(.text)
    __aeabi_d2ulz                            0x080009d9   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000a09   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000a09   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000a2d   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000a2d   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x08000a51   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x08000a59   Thumb Code   158  _strtoul.o(.text)
    _chval                                   0x08000af7   Thumb Code    28  _chval.o(.text)
    arm_bitreversal_f32                      0x08000b15   Thumb Code   190  arm_bitreversal.o(.text.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x08000bd3   Thumb Code    64  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x08000c15   Thumb Code   148  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x08000ca9   Thumb Code   340  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    arm_cos_f32                              0x08000dfd   Thumb Code   152  arm_cos_f32.o(.text.arm_cos_f32)
    arm_radix4_butterfly_f32                 0x08000e95   Thumb Code   858  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x080011f1   Thumb Code   890  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    ADC_IRQHandler                           0x08001601   Thumb Code     6  stm32f4xx_it.o(i.ADC_IRQHandler)
    BusFault_Handler                         0x0800173d   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    CTRL_INIT                                0x0800173f   Thumb Code    10  cmd_to_fun.o(i.CTRL_INIT)
    Check_UART_Status                        0x08001749   Thumb Code    76  my_usart.o(i.Check_UART_Status)
    DAC_DMAConvCpltCh1                       0x080017f9   Thumb Code    16  stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1)
    DAC_DMAConvCpltCh2                       0x08001809   Thumb Code    16  stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2)
    DAC_DMAErrorCh1                          0x08001819   Thumb Code    24  stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1)
    DAC_DMAErrorCh2                          0x08001831   Thumb Code    24  stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2)
    DAC_DMAHalfConvCpltCh1                   0x08001849   Thumb Code    10  stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1)
    DAC_DMAHalfConvCpltCh2                   0x08001853   Thumb Code    10  stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2)
    DA_Apply_Settings                        0x0800185d   Thumb Code   206  da_output.o(i.DA_Apply_Settings)
    DA_FPGA_START                            0x0800193d   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_START)
    DA_FPGA_STOP                             0x0800194b   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_STOP)
    DA_Init                                  0x08001959   Thumb Code    50  da_output.o(i.DA_Init)
    DA_SetConfig                             0x08001991   Thumb Code    28  da_output.o(i.DA_SetConfig)
    DDS_CalculatePhaseIncrement              0x080019b1   Thumb Code    20  software_dds.o(i.DDS_CalculatePhaseIncrement)
    DDS_GetConfig                            0x080019cd   Thumb Code    10  software_dds.o(i.DDS_GetConfig)
    DDS_GetInterruptStats                    0x080019dd   Thumb Code    12  software_dds.o(i.DDS_GetInterruptStats)
    DDS_Init                                 0x080019ed   Thumb Code   140  software_dds.o(i.DDS_Init)
    DDS_SetAmplitude                         0x08001acd   Thumb Code    32  software_dds.o(i.DDS_SetAmplitude)
    DDS_SetFrequency                         0x08001b29   Thumb Code    54  software_dds.o(i.DDS_SetFrequency)
    DDS_SetPhase                             0x08001bd5   Thumb Code    22  software_dds.o(i.DDS_SetPhase)
    DDS_Setup                                0x08001bf1   Thumb Code    52  software_dds.o(i.DDS_Setup)
    DDS_Start                                0x08001c5d   Thumb Code    66  software_dds.o(i.DDS_Start)
    DDS_Stop                                 0x08001d25   Thumb Code    40  software_dds.o(i.DDS_Stop)
    DDS_Test_FreqMinus                       0x08001d6d   Thumb Code    74  dds_test.o(i.DDS_Test_FreqMinus)
    DDS_Test_FreqPlus                        0x08001e15   Thumb Code    76  dds_test.o(i.DDS_Test_FreqPlus)
    DDS_Test_Init                            0x08001ebd   Thumb Code    42  dds_test.o(i.DDS_Test_Init)
    DDS_Test_ProcessCommand                  0x08001f8d   Thumb Code   276  dds_test.o(i.DDS_Test_ProcessCommand)
    DDS_Test_SetAmplitude                    0x08002171   Thumb Code    40  dds_test.o(i.DDS_Test_SetAmplitude)
    DDS_Test_SetMode                         0x080021b9   Thumb Code    78  dds_test.o(i.DDS_Test_SetMode)
    DDS_Test_ShowDebugInfo                   0x08002281   Thumb Code   198  dds_test.o(i.DDS_Test_ShowDebugInfo)
    DDS_Test_ShowHelp                        0x080024dd   Thumb Code   134  dds_test.o(i.DDS_Test_ShowHelp)
    DDS_Test_ShowStatus                      0x08002775   Thumb Code   158  dds_test.o(i.DDS_Test_ShowStatus)
    DDS_Test_Start                           0x08002911   Thumb Code    40  dds_test.o(i.DDS_Test_Start)
    DDS_Test_Stop                            0x08002955   Thumb Code    28  dds_test.o(i.DDS_Test_Stop)
    DDS_Test_TestDAC                         0x0800298d   Thumb Code   120  dds_test.o(i.DDS_Test_TestDAC)
    DMA1_Stream5_IRQHandler                  0x08002a99   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream0_IRQHandler                  0x08002aa5   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DebugMon_Handler                         0x08002b9b   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08002b9d   Thumb Code     4  main.o(i.Error_Handler)
    FMC_NORSRAM_Extended_Timing_Init         0x08002ba1   Thumb Code    76  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    FMC_NORSRAM_Init                         0x08002bf1   Thumb Code   140  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    FMC_NORSRAM_Timing_Init                  0x08002c81   Thumb Code   106  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    HAL_ADCEx_InjectedConvCpltCallback       0x08002ceb   Thumb Code     2  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADC_ConfigChannel                    0x08002ced   Thumb Code   374  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08002e79   Thumb Code    20  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08002e95   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08002e97   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x08002e99   Thumb Code   322  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08002fdb   Thumb Code    86  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x08003031   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x08003035   Thumb Code   146  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x080030dd   Thumb Code   362  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x08003269   Thumb Code   112  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DACEx_ConvCpltCallbackCh2            0x080032d9   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2)
    HAL_DACEx_ConvHalfCpltCallbackCh2        0x080032db   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2)
    HAL_DACEx_ErrorCallbackCh2               0x080032dd   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2)
    HAL_DAC_ConfigChannel                    0x080032df   Thumb Code    98  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_ConvCpltCallbackCh1              0x08003341   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1)
    HAL_DAC_ConvHalfCpltCallbackCh1          0x08003343   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1)
    HAL_DAC_ErrorCallbackCh1                 0x08003345   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1)
    HAL_DAC_Init                             0x08003347   Thumb Code    42  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x08003371   Thumb Code   138  dac.o(i.HAL_DAC_MspInit)
    HAL_DAC_SetValue                         0x08003411   Thumb Code    54  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    HAL_DAC_Start                            0x08003447   Thumb Code   114  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    HAL_DAC_Start_DMA                        0x080034b9   Thumb Code   240  stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
    HAL_DAC_Stop_DMA                         0x080035c1   Thumb Code    92  stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA)
    HAL_DMA_Abort                            0x0800361d   Thumb Code   162  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080036bf   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x080036e5   Thumb Code   488  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080038d1   Thumb Code   230  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080039bd   Thumb Code   112  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08003a2d   Thumb Code    34  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_DeInit                          0x08003af9   Thumb Code   358  stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    HAL_GPIO_Init                            0x08003c71   Thumb Code   564  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08003ebd   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08003ecb   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08003ed9   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08003ee5   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003ef5   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08003f29   Thumb Code    58  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003f6d   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_DisableIRQ                      0x08003f9d   Thumb Code    36  stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ)
    HAL_NVIC_EnableIRQ                       0x08003fc1   Thumb Code    28  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003fdd   Thumb Code    98  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08004045   Thumb Code    30  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x08004069   Thumb Code   110  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    HAL_RCC_CSSCallback                      0x080040e5   Thumb Code     2  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    HAL_RCC_ClockConfig                      0x080040e9   Thumb Code   354  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_EnableCSS                        0x08004265   Thumb Code     8  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    HAL_RCC_GetHCLKFreq                      0x08004271   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x0800427d   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800429d   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080042bd   Thumb Code    94  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_NMI_IRQHandler                   0x0800432d   Thumb Code    24  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    HAL_RCC_OscConfig                        0x0800434d   Thumb Code  1154  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x080047cf   Thumb Code    94  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x0800482d   Thumb Code     4  fmc.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x08004831   Thumb Code    36  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08004859   Thumb Code   154  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080048f3   Thumb Code    92  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08004951   Thumb Code    52  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x08004991   Thumb Code   128  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x08004a11   Thumb Code   138  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Base_Stop                        0x08004a9b   Thumb Code    38  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    HAL_TIM_Base_Stop_IT                     0x08004ac1   Thumb Code    48  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    HAL_TIM_ConfigClockSource                0x08004af1   Thumb Code   234  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_GenerateEvent                    0x08004bdb   Thumb Code    42  stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08004c05   Thumb Code    82  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08004c59   Thumb Code    74  my_usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08004cb9   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_DeInit                          0x08004d29   Thumb Code    56  stm32f4xx_hal_uart.o(i.HAL_UART_DeInit)
    HAL_UART_ErrorCallback                   0x08004d61   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08004d65   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004fe5   Thumb Code   102  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspDeInit                       0x0800504d   Thumb Code    98  usart.o(i.HAL_UART_MspDeInit)
    HAL_UART_MspInit                         0x080050c9   Thumb Code   258  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080051e5   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08005201   Thumb Code   168  my_usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080052c9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x080052cb   Thumb Code   184  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08005383   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08005385   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x08005389   Thumb Code    88  adc.o(i.MX_ADC1_Init)
    MX_DAC_Init                              0x080053e9   Thumb Code    70  dac.o(i.MX_DAC_Init)
    MX_DMA_Init                              0x08005439   Thumb Code    72  dma.o(i.MX_DMA_Init)
    MX_FMC_Init                              0x08005485   Thumb Code   108  fmc.o(i.MX_FMC_Init)
    MX_GPIO_Init                             0x080054f9   Thumb Code   382  gpio.o(i.MX_GPIO_Init)
    MX_TIM3_Init                             0x08005699   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_TIM6_Init                             0x08005701   Thumb Code    62  tim.o(i.MX_TIM6_Init)
    MX_USART1_UART_Init                      0x08005749   Thumb Code    66  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08005799   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x080057d1   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08005809   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800580b   Thumb Code     6  stm32f4xx_it.o(i.NMI_Handler)
    PID_Init                                 0x08005811   Thumb Code    60  app_pid.o(i.PID_Init)
    PendSV_Handler                           0x08005861   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08005863   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08005865   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005869   Thumb Code   150  main.o(i.SystemClock_Config)
    SystemInit                               0x08005909   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08005919   Thumb Code   190  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x080059ed   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_DMA                   0x08005db1   Thumb Code   164  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08005e61   Thumb Code    56  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08005f85   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08005f91   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08005f9d   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08005fa9   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0sprintf                               0x08005fad   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08005fad   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08005fad   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08005fad   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08005fad   Thumb Code     0  printfa.o(i.__0sprintf)
    __0vsnprintf                             0x08005fd5   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08005fd5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08005fd5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08005fd5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08005fd5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassifyf                        0x08006009   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __aeabi_errno_addr                       0x08006031   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08006031   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __hardfp_cosf                            0x08006039   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_log10f                          0x08006189   Thumb Code   332  log10f.o(i.__hardfp_log10f)
    __hardfp_roundf                          0x08006309   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sinf                            0x080063a5   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sqrtf                           0x08006535   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x08006571   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08006585   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x0800658d   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x0800659d   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x080065ad   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __scatterload_copy                       0x08006701   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800670f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08006711   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08006721   Thumb Code     6  errno.o(i.__set_errno)
    adc_tim_dma_init                         0x08006fd9   Thumb Code    34  adc_app.o(i.adc_tim_dma_init)
    calculate_bandwidth                      0x0800700d   Thumb Code   132  wave_recognition.o(i.calculate_bandwidth)
    calculate_carrier_suppression            0x08007095   Thumb Code   118  wave_recognition.o(i.calculate_carrier_suppression)
    calculate_fft_spectrum                   0x0800711d   Thumb Code   180  my_fft.o(i.calculate_fft_spectrum)
    calculate_sinad                          0x080071e1   Thumb Code    48  my_fft.o(i.calculate_sinad)
    calculate_thd                            0x08007219   Thumb Code   336  my_fft.o(i.calculate_thd)
    calculate_thd_n                          0x08007385   Thumb Code   210  my_fft.o(i.calculate_thd_n)
    circuit_learning_task                    0x08007475   Thumb Code   208  scheduler.o(i.circuit_learning_task)
    dac_app_init                             0x080075cd   Thumb Code    28  dac_app.o(i.dac_app_init)
    dac_app_set_amplitude                    0x080075ed   Thumb Code    92  dac_app.o(i.dac_app_set_amplitude)
    dac_app_set_waveform                     0x08007655   Thumb Code    30  dac_app.o(i.dac_app_set_waveform)
    detect_symmetry                          0x08007679   Thumb Code   168  wave_recognition.o(i.detect_symmetry)
    fft_init                                 0x0800772d   Thumb Code    22  my_fft.o(i.fft_init)
    find_peaks                               0x08007749   Thumb Code   238  wave_recognition.o(i.find_peaks)
    generate_hanning_window                  0x08007845   Thumb Code    82  my_fft.o(i.generate_hanning_window)
    get_current_ad_frequency                 0x08007b4d   Thumb Code     8  key_app.o(i.get_current_ad_frequency)
    get_precise_peak_frequency               0x08007b59   Thumb Code   192  my_fft.o(i.get_precise_peak_frequency)
    key_proc                                 0x08007c29   Thumb Code   176  key_app.o(i.key_proc)
    key_read                                 0x08007dc1   Thumb Code    60  key_app.o(i.key_read)
    main                                     0x08007e05   Thumb Code   166  main.o(i.main)
    my_printf                                0x08007f15   Thumb Code    50  my_usart.o(i.my_printf)
    output_fft_spectrum                      0x08007f49   Thumb Code   436  my_fft.o(i.output_fft_spectrum)
    process_uart1_command                    0x080082f1   Thumb Code  1588  my_usart.o(i.process_uart1_command)
    process_uart2_command                    0x08008a09   Thumb Code   244  my_usart.o(i.process_uart2_command)
    recognize_waveform                       0x08008c75   Thumb Code   532  wave_recognition.o(i.recognize_waveform)
    round_to_nearest_k                       0x08008fa1   Thumb Code    32  my_fft.o(i.round_to_nearest_k)
    scheduler_init                           0x08008fc5   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08008fd1   Thumb Code    60  scheduler.o(i.scheduler_run)
    start_circuit_learning                   0x08009011   Thumb Code    62  scheduler.o(i.start_circuit_learning)
    AHBPrescTable                            0x080092bc   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x080092cc   Data           8  system_stm32f4xx.o(.constdata)
    __ctype_table                            0x08009334   Data         129  ctype_o.o(.constdata)
    armBitRevTable                           0x080094b6   Data        2048  arm_common_tables.o(.rodata.armBitRevTable)
    sinTable_f32                             0x08009cb8   Data        2052  arm_common_tables.o(.rodata.sinTable_f32)
    twiddleCoef_4096                         0x0800a4bc   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    Region$$Table$$Base                      0x080124bc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080124dc   Number         0  anon$$obj.o(Region$$Table)
    key_val                                  0x20000008   Data           1  key_app.o(.data)
    key_old                                  0x20000009   Data           1  key_app.o(.data)
    key_down                                 0x2000000a   Data           1  key_app.o(.data)
    key_up                                   0x2000000b   Data           1  key_app.o(.data)
    AdcConvEnd                               0x20000014   Data           1  adc_app.o(.data)
    commandReceived2                         0x20000048   Data           1  my_usart.o(.data)
    commandReceived3                         0x20000049   Data           1  my_usart.o(.data)
    uart1_cmd_flag                           0x2000004a   Data           1  my_usart.o(.data)
    rxTemp1                                  0x2000004b   Data           1  my_usart.o(.data)
    rxTemp3                                  0x2000004c   Data           1  my_usart.o(.data)
    rxTemp2                                  0x2000004d   Data           1  my_usart.o(.data)
    rxIndex3                                 0x2000004e   Data           2  my_usart.o(.data)
    rxIndex2                                 0x20000050   Data           2  my_usart.o(.data)
    task_num                                 0x2000005a   Data           1  scheduler.o(.data)
    uwTickFreq                               0x20000098   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x2000009c   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x200000a0   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x200000a4   Data           4  system_stm32f4xx.o(.data)
    hadc1                                    0x200000ac   Data          72  adc.o(.bss)
    hdma_adc1                                0x200000f4   Data          96  adc.o(.bss)
    hdac                                     0x20000154   Data          20  dac.o(.bss)
    hdma_dac1                                0x20000168   Data          96  dac.o(.bss)
    hsram2                                   0x200001c8   Data          80  fmc.o(.bss)
    htim3                                    0x20000218   Data          72  tim.o(.bss)
    htim6                                    0x20000260   Data          72  tim.o(.bss)
    huart1                                   0x200002a8   Data          72  usart.o(.bss)
    huart2                                   0x200002f0   Data          72  usart.o(.bss)
    huart3                                   0x20000338   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x20000380   Data          96  usart.o(.bss)
    fifo_data1                               0x200003e0   Data        2048  ad_measure.o(.bss)
    fifo_data2                               0x20000be0   Data        2048  ad_measure.o(.bss)
    fifo_data1_f                             0x200013e0   Data        4096  ad_measure.o(.bss)
    fifo_data2_f                             0x200023e0   Data        4096  ad_measure.o(.bss)
    da_channels                              0x200033e0   Data          24  da_output.o(.bss)
    dac_val_buffer                           0x200033f8   Data        4096  adc_app.o(.bss)
    adc_val_buffer                           0x200043f8   Data        8192  adc_app.o(.bss)
    fft_instance                             0x20006cf8   Data          20  my_fft.o(.bss)
    fft_input_buffer                         0x20006d0c   Data        8192  my_fft.o(.bss)
    fft_magnitude                            0x20008d0c   Data        4096  my_fft.o(.bss)
    window_buffer                            0x20009d0c   Data        4096  my_fft.o(.bss)
    rxBuffer3                                0x2000ad0c   Data         128  my_usart.o(.bss)
    rxBuffer2                                0x2000ad8c   Data         128  my_usart.o(.bss)
    uart1_dma_buffer                         0x2000ae0c   Data         128  my_usart.o(.bss)
    uart1_cmd_buffer                         0x2000ae8c   Data         128  my_usart.o(.bss)
    PID                                      0x2000bedc   Data          36  app_pid.o(.bss)
    __initial_sp                             0x2000c300   Data           0  startup_stm32f429xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00012588, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000124dc, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         5799  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         6159    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         6162    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6164    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6166    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         6167    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         6174    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         6169    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         6171    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         6160    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x00000024   Code   RO            4    .text               startup_stm32f429xx.o
    0x080001e8   0x080001e8   0x00000062   Code   RO         5802    .text               mc_w.l(uldiv.o)
    0x0800024a   0x0800024a   0x00000024   Code   RO         5804    .text               mc_w.l(memcpya.o)
    0x0800026e   0x0800026e   0x00000024   Code   RO         5806    .text               mc_w.l(memseta.o)
    0x08000292   0x08000292   0x00000024   Code   RO         5808    .text               mc_w.l(strstr.o)
    0x080002b6   0x080002b6   0x00000014   Code   RO         5810    .text               mc_w.l(strchr.o)
    0x080002ca   0x080002ca   0x0000000e   Code   RO         5812    .text               mc_w.l(strlen.o)
    0x080002d8   0x080002d8   0x0000001c   Code   RO         5814    .text               mc_w.l(strcmp.o)
    0x080002f4   0x080002f4   0x00000012   Code   RO         5816    .text               mc_w.l(strcpy.o)
    0x08000306   0x08000306   0x0000001e   Code   RO         5818    .text               mc_w.l(strncmp.o)
    0x08000324   0x08000324   0x0000001a   Code   RO         6083    .text               mc_w.l(atoi.o)
    0x0800033e   0x0800033e   0x000000e4   Code   RO         6085    .text               mf_w.l(dmul.o)
    0x08000422   0x08000422   0x000000de   Code   RO         6087    .text               mf_w.l(ddiv.o)
    0x08000500   0x08000500   0x00000022   Code   RO         6089    .text               mf_w.l(dflti.o)
    0x08000522   0x08000522   0x0000002c   Code   RO         6091    .text               mf_w.l(ffixul.o)
    0x0800054e   0x0800054e   0x0000003e   Code   RO         6093    .text               mf_w.l(dfixi.o)
    0x0800058c   0x0800058c   0x00000026   Code   RO         6095    .text               mf_w.l(f2d.o)
    0x080005b2   0x080005b2   0x00000002   PAD
    0x080005b4   0x080005b4   0x00000030   Code   RO         6097    .text               mf_w.l(cdcmple.o)
    0x080005e4   0x080005e4   0x00000030   Code   RO         6099    .text               mf_w.l(cdrcmple.o)
    0x08000614   0x08000614   0x00000038   Code   RO         6101    .text               mf_w.l(d2f.o)
    0x0800064c   0x0800064c   0x0000002c   Code   RO         6178    .text               mc_w.l(uidiv.o)
    0x08000678   0x08000678   0x0000001e   Code   RO         6180    .text               mc_w.l(llshl.o)
    0x08000696   0x08000696   0x00000020   Code   RO         6182    .text               mc_w.l(llushr.o)
    0x080006b6   0x080006b6   0x00000070   Code   RO         6191    .text               mc_w.l(strtol.o)
    0x08000726   0x08000726   0x00000000   Code   RO         6193    .text               mc_w.l(iusefp.o)
    0x08000726   0x08000726   0x0000006e   Code   RO         6194    .text               mf_w.l(fepilogue.o)
    0x08000794   0x08000794   0x0000003c   Code   RO         6196    .text               mf_w.l(frnd.o)
    0x080007d0   0x080007d0   0x000000ba   Code   RO         6198    .text               mf_w.l(depilogue.o)
    0x0800088a   0x0800088a   0x0000014e   Code   RO         6200    .text               mf_w.l(dadd.o)
    0x080009d8   0x080009d8   0x00000030   Code   RO         6206    .text               mf_w.l(dfixul.o)
    0x08000a08   0x08000a08   0x00000024   Code   RO         6208    .text               mc_w.l(init.o)
    0x08000a2c   0x08000a2c   0x00000024   Code   RO         6211    .text               mc_w.l(llsshr.o)
    0x08000a50   0x08000a50   0x00000008   Code   RO         6213    .text               mc_w.l(ctype_o.o)
    0x08000a58   0x08000a58   0x0000009e   Code   RO         6241    .text               mc_w.l(_strtoul.o)
    0x08000af6   0x08000af6   0x0000001c   Code   RO         6246    .text               mc_w.l(_chval.o)
    0x08000b12   0x08000b12   0x00000002   PAD
    0x08000b14   0x08000b14   0x000000be   Code   RO         5598    .text.arm_bitreversal_f32  arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08000bd2   0x08000bd2   0x00000040   Code   RO         5575    .text.arm_cfft_radix4_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08000c12   0x08000c12   0x00000002   PAD
    0x08000c14   0x08000c14   0x00000094   Code   RO         5589    .text.arm_cfft_radix4_init_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08000ca8   0x08000ca8   0x00000154   Code   RO         5556    .text.arm_cmplx_mag_f32  arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08000dfc   0x08000dfc   0x00000098   Code   RO         5547    .text.arm_cos_f32   arm_cortexM4lf_math.lib(arm_cos_f32.o)
    0x08000e94   0x08000e94   0x0000035a   Code   RO         5579    .text.arm_radix4_butterfly_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x080011ee   0x080011ee   0x00000002   PAD
    0x080011f0   0x080011f0   0x0000037a   Code   RO         5577    .text.arm_radix4_butterfly_inverse_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800156a   0x0800156a   0x00000074   Code   RO         2044    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x080015de   0x080015de   0x00000016   Code   RO         2045    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x080015f4   0x080015f4   0x0000000a   Code   RO         2046    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x080015fe   0x080015fe   0x00000002   PAD
    0x08001600   0x08001600   0x0000000c   Code   RO          627    i.ADC_IRQHandler    stm32f4xx_it.o
    0x0800160c   0x0800160c   0x00000130   Code   RO         2047    i.ADC_Init          stm32f4xx_hal_adc.o
    0x0800173c   0x0800173c   0x00000002   Code   RO          628    i.BusFault_Handler  stm32f4xx_it.o
    0x0800173e   0x0800173e   0x0000000a   Code   RO         1755    i.CTRL_INIT         cmd_to_fun.o
    0x08001748   0x08001748   0x000000b0   Code   RO         1620    i.Check_UART_Status  my_usart.o
    0x080017f8   0x080017f8   0x00000010   Code   RO         3599    i.DAC_DMAConvCpltCh1  stm32f4xx_hal_dac.o
    0x08001808   0x08001808   0x00000010   Code   RO         3743    i.DAC_DMAConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08001818   0x08001818   0x00000018   Code   RO         3600    i.DAC_DMAErrorCh1   stm32f4xx_hal_dac.o
    0x08001830   0x08001830   0x00000018   Code   RO         3744    i.DAC_DMAErrorCh2   stm32f4xx_hal_dac_ex.o
    0x08001848   0x08001848   0x0000000a   Code   RO         3601    i.DAC_DMAHalfConvCpltCh1  stm32f4xx_hal_dac.o
    0x08001852   0x08001852   0x0000000a   Code   RO         3745    i.DAC_DMAHalfConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x0800185c   0x0800185c   0x000000e0   Code   RO          818    i.DA_Apply_Settings  da_output.o
    0x0800193c   0x0800193c   0x0000000e   Code   RO         1756    i.DA_FPGA_START     cmd_to_fun.o
    0x0800194a   0x0800194a   0x0000000e   Code   RO         1757    i.DA_FPGA_STOP      cmd_to_fun.o
    0x08001958   0x08001958   0x00000038   Code   RO          819    i.DA_Init           da_output.o
    0x08001990   0x08001990   0x00000020   Code   RO          820    i.DA_SetConfig      da_output.o
    0x080019b0   0x080019b0   0x0000001c   Code   RO         1204    i.DDS_CalculatePhaseIncrement  software_dds.o
    0x080019cc   0x080019cc   0x00000010   Code   RO         1205    i.DDS_GetConfig     software_dds.o
    0x080019dc   0x080019dc   0x00000010   Code   RO         1206    i.DDS_GetInterruptStats  software_dds.o
    0x080019ec   0x080019ec   0x000000e0   Code   RO         1207    i.DDS_Init          software_dds.o
    0x08001acc   0x08001acc   0x0000005c   Code   RO         1209    i.DDS_SetAmplitude  software_dds.o
    0x08001b28   0x08001b28   0x000000ac   Code   RO         1210    i.DDS_SetFrequency  software_dds.o
    0x08001bd4   0x08001bd4   0x0000001c   Code   RO         1211    i.DDS_SetPhase      software_dds.o
    0x08001bf0   0x08001bf0   0x0000006c   Code   RO         1213    i.DDS_Setup         software_dds.o
    0x08001c5c   0x08001c5c   0x000000c8   Code   RO         1214    i.DDS_Start         software_dds.o
    0x08001d24   0x08001d24   0x00000048   Code   RO         1215    i.DDS_Stop          software_dds.o
    0x08001d6c   0x08001d6c   0x000000a8   Code   RO         1943    i.DDS_Test_FreqMinus  dds_test.o
    0x08001e14   0x08001e14   0x000000a8   Code   RO         1944    i.DDS_Test_FreqPlus  dds_test.o
    0x08001ebc   0x08001ebc   0x000000d0   Code   RO         1945    i.DDS_Test_Init     dds_test.o
    0x08001f8c   0x08001f8c   0x000001e4   Code   RO         1946    i.DDS_Test_ProcessCommand  dds_test.o
    0x08002170   0x08002170   0x00000048   Code   RO         1947    i.DDS_Test_SetAmplitude  dds_test.o
    0x080021b8   0x080021b8   0x000000c8   Code   RO         1948    i.DDS_Test_SetMode  dds_test.o
    0x08002280   0x08002280   0x0000025c   Code   RO         1949    i.DDS_Test_ShowDebugInfo  dds_test.o
    0x080024dc   0x080024dc   0x00000298   Code   RO         1950    i.DDS_Test_ShowHelp  dds_test.o
    0x08002774   0x08002774   0x0000019c   Code   RO         1951    i.DDS_Test_ShowStatus  dds_test.o
    0x08002910   0x08002910   0x00000044   Code   RO         1952    i.DDS_Test_Start    dds_test.o
    0x08002954   0x08002954   0x00000038   Code   RO         1953    i.DDS_Test_Stop     dds_test.o
    0x0800298c   0x0800298c   0x0000010c   Code   RO         1954    i.DDS_Test_TestDAC  dds_test.o
    0x08002a98   0x08002a98   0x0000000c   Code   RO          629    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08002aa4   0x08002aa4   0x0000000c   Code   RO          630    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x08002ab0   0x08002ab0   0x00000034   Code   RO         2883    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08002ae4   0x08002ae4   0x0000007e   Code   RO         2884    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002b62   0x08002b62   0x00000038   Code   RO         2885    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08002b9a   0x08002b9a   0x00000002   Code   RO          631    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002b9c   0x08002b9c   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08002ba0   0x08002ba0   0x00000050   Code   RO         3847    i.FMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fmc.o
    0x08002bf0   0x08002bf0   0x00000090   Code   RO         3848    i.FMC_NORSRAM_Init  stm32f4xx_ll_fmc.o
    0x08002c80   0x08002c80   0x0000006a   Code   RO         3849    i.FMC_NORSRAM_Timing_Init  stm32f4xx_ll_fmc.o
    0x08002cea   0x08002cea   0x00000002   Code   RO         2218    i.HAL_ADCEx_InjectedConvCpltCallback  stm32f4xx_hal_adc_ex.o
    0x08002cec   0x08002cec   0x0000018c   Code   RO         2049    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08002e78   0x08002e78   0x0000001c   Code   RO         1056    i.HAL_ADC_ConvCpltCallback  adc_app.o
    0x08002e94   0x08002e94   0x00000002   Code   RO         2051    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x08002e96   0x08002e96   0x00000002   Code   RO         2053    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x08002e98   0x08002e98   0x00000142   Code   RO         2057    i.HAL_ADC_IRQHandler  stm32f4xx_hal_adc.o
    0x08002fda   0x08002fda   0x00000056   Code   RO         2058    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08003030   0x08003030   0x00000002   Code   RO         2059    i.HAL_ADC_LevelOutOfWindowCallback  stm32f4xx_hal_adc.o
    0x08003032   0x08003032   0x00000002   PAD
    0x08003034   0x08003034   0x000000a8   Code   RO          369    i.HAL_ADC_MspInit   adc.o
    0x080030dc   0x080030dc   0x0000018c   Code   RO         2065    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x08003268   0x08003268   0x00000070   Code   RO         2068    i.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x080032d8   0x080032d8   0x00000002   Code   RO         3746    i.HAL_DACEx_ConvCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x080032da   0x080032da   0x00000002   Code   RO         3747    i.HAL_DACEx_ConvHalfCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x080032dc   0x080032dc   0x00000002   Code   RO         3753    i.HAL_DACEx_ErrorCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x080032de   0x080032de   0x00000062   Code   RO         3602    i.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x08003340   0x08003340   0x00000002   Code   RO         3603    i.HAL_DAC_ConvCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x08003342   0x08003342   0x00000002   Code   RO         3604    i.HAL_DAC_ConvHalfCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x08003344   0x08003344   0x00000002   Code   RO         3607    i.HAL_DAC_ErrorCallbackCh1  stm32f4xx_hal_dac.o
    0x08003346   0x08003346   0x0000002a   Code   RO         3612    i.HAL_DAC_Init      stm32f4xx_hal_dac.o
    0x08003370   0x08003370   0x000000a0   Code   RO          411    i.HAL_DAC_MspInit   dac.o
    0x08003410   0x08003410   0x00000036   Code   RO         3615    i.HAL_DAC_SetValue  stm32f4xx_hal_dac.o
    0x08003446   0x08003446   0x00000072   Code   RO         3616    i.HAL_DAC_Start     stm32f4xx_hal_dac.o
    0x080034b8   0x080034b8   0x00000108   Code   RO         3617    i.HAL_DAC_Start_DMA  stm32f4xx_hal_dac.o
    0x080035c0   0x080035c0   0x0000005c   Code   RO         3619    i.HAL_DAC_Stop_DMA  stm32f4xx_hal_dac.o
    0x0800361c   0x0800361c   0x000000a2   Code   RO         2886    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080036be   0x080036be   0x00000024   Code   RO         2887    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080036e2   0x080036e2   0x00000002   PAD
    0x080036e4   0x080036e4   0x000001ec   Code   RO         2891    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x080038d0   0x080038d0   0x000000ec   Code   RO         2892    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080039bc   0x080039bc   0x00000070   Code   RO         2896    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08003a2c   0x08003a2c   0x00000028   Code   RO         3336    i.HAL_Delay         stm32f4xx_hal.o
    0x08003a54   0x08003a54   0x000000a4   Code   RO          476    i.HAL_FMC_MspInit   fmc.o
    0x08003af8   0x08003af8   0x00000178   Code   RO         2776    i.HAL_GPIO_DeInit   stm32f4xx_hal_gpio.o
    0x08003c70   0x08003c70   0x0000024c   Code   RO         2779    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08003ebc   0x08003ebc   0x0000000e   Code   RO         2781    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08003eca   0x08003eca   0x0000000e   Code   RO         2783    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08003ed8   0x08003ed8   0x0000000c   Code   RO         3344    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003ee4   0x08003ee4   0x00000010   Code   RO         3350    i.HAL_IncTick       stm32f4xx_hal.o
    0x08003ef4   0x08003ef4   0x00000034   Code   RO         3351    i.HAL_Init          stm32f4xx_hal.o
    0x08003f28   0x08003f28   0x00000044   Code   RO         3352    i.HAL_InitTick      stm32f4xx_hal.o
    0x08003f6c   0x08003f6c   0x00000030   Code   RO          739    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08003f9c   0x08003f9c   0x00000024   Code   RO         3188    i.HAL_NVIC_DisableIRQ  stm32f4xx_hal_cortex.o
    0x08003fc0   0x08003fc0   0x0000001c   Code   RO         3189    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08003fdc   0x08003fdc   0x00000068   Code   RO         3195    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08004044   0x08004044   0x00000024   Code   RO         3196    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08004068   0x08004068   0x0000007c   Code   RO         3115    i.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x080040e4   0x080040e4   0x00000002   Code   RO         2322    i.HAL_RCC_CSSCallback  stm32f4xx_hal_rcc.o
    0x080040e6   0x080040e6   0x00000002   PAD
    0x080040e8   0x080040e8   0x0000017c   Code   RO         2323    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08004264   0x08004264   0x0000000c   Code   RO         2326    i.HAL_RCC_EnableCSS  stm32f4xx_hal_rcc.o
    0x08004270   0x08004270   0x0000000c   Code   RO         2328    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x0800427c   0x0800427c   0x00000020   Code   RO         2330    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800429c   0x0800429c   0x00000020   Code   RO         2331    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x080042bc   0x080042bc   0x00000070   Code   RO         2332    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800432c   0x0800432c   0x00000020   Code   RO         2334    i.HAL_RCC_NMI_IRQHandler  stm32f4xx_hal_rcc.o
    0x0800434c   0x0800434c   0x00000482   Code   RO         2335    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080047ce   0x080047ce   0x0000005e   Code   RO         4023    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x0800482c   0x0800482c   0x00000004   Code   RO          478    i.HAL_SRAM_MspInit  fmc.o
    0x08004830   0x08004830   0x00000028   Code   RO         3200    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08004858   0x08004858   0x0000009a   Code   RO         4890    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x080048f2   0x080048f2   0x0000005c   Code   RO         4156    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800494e   0x0800494e   0x00000002   PAD
    0x08004950   0x08004950   0x00000040   Code   RO          525    i.HAL_TIM_Base_MspInit  tim.o
    0x08004990   0x08004990   0x00000080   Code   RO         4159    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08004a10   0x08004a10   0x0000008a   Code   RO         4161    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08004a9a   0x08004a9a   0x00000026   Code   RO         4162    i.HAL_TIM_Base_Stop  stm32f4xx_hal_tim.o
    0x08004ac0   0x08004ac0   0x00000030   Code   RO         4164    i.HAL_TIM_Base_Stop_IT  stm32f4xx_hal_tim.o
    0x08004af0   0x08004af0   0x000000ea   Code   RO         4165    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08004bda   0x08004bda   0x0000002a   Code   RO         4187    i.HAL_TIM_GenerateEvent  stm32f4xx_hal_tim.o
    0x08004c04   0x08004c04   0x00000052   Code   RO         5148    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08004c56   0x08004c56   0x00000002   PAD
    0x08004c58   0x08004c58   0x00000060   Code   RO         1621    i.HAL_UARTEx_RxEventCallback  my_usart.o
    0x08004cb8   0x08004cb8   0x00000070   Code   RO         5162    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08004d28   0x08004d28   0x00000038   Code   RO         5163    i.HAL_UART_DeInit   stm32f4xx_hal_uart.o
    0x08004d60   0x08004d60   0x00000002   Code   RO         5164    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08004d62   0x08004d62   0x00000002   PAD
    0x08004d64   0x08004d64   0x00000280   Code   RO         5167    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08004fe4   0x08004fe4   0x00000066   Code   RO         5168    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x0800504a   0x0800504a   0x00000002   PAD
    0x0800504c   0x0800504c   0x0000007c   Code   RO          572    i.HAL_UART_MspDeInit  usart.o
    0x080050c8   0x080050c8   0x0000011c   Code   RO          573    i.HAL_UART_MspInit  usart.o
    0x080051e4   0x080051e4   0x0000001c   Code   RO         5173    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08005200   0x08005200   0x000000c8   Code   RO         1622    i.HAL_UART_RxCpltCallback  my_usart.o
    0x080052c8   0x080052c8   0x00000002   Code   RO         5175    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x080052ca   0x080052ca   0x000000b8   Code   RO         5176    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08005382   0x08005382   0x00000002   Code   RO         5179    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08005384   0x08005384   0x00000002   Code   RO          632    i.HardFault_Handler  stm32f4xx_it.o
    0x08005386   0x08005386   0x00000002   PAD
    0x08005388   0x08005388   0x00000060   Code   RO          370    i.MX_ADC1_Init      adc.o
    0x080053e8   0x080053e8   0x00000050   Code   RO          412    i.MX_DAC_Init       dac.o
    0x08005438   0x08005438   0x0000004c   Code   RO          452    i.MX_DMA_Init       dma.o
    0x08005484   0x08005484   0x00000074   Code   RO          479    i.MX_FMC_Init       fmc.o
    0x080054f8   0x080054f8   0x000001a0   Code   RO          344    i.MX_GPIO_Init      gpio.o
    0x08005698   0x08005698   0x00000068   Code   RO          526    i.MX_TIM3_Init      tim.o
    0x08005700   0x08005700   0x00000048   Code   RO          527    i.MX_TIM6_Init      tim.o
    0x08005748   0x08005748   0x00000050   Code   RO          574    i.MX_USART1_UART_Init  usart.o
    0x08005798   0x08005798   0x00000038   Code   RO          575    i.MX_USART2_UART_Init  usart.o
    0x080057d0   0x080057d0   0x00000038   Code   RO          576    i.MX_USART3_UART_Init  usart.o
    0x08005808   0x08005808   0x00000002   Code   RO          633    i.MemManage_Handler  stm32f4xx_it.o
    0x0800580a   0x0800580a   0x00000006   Code   RO          634    i.NMI_Handler       stm32f4xx_it.o
    0x08005810   0x08005810   0x00000050   Code   RO         1899    i.PID_Init          app_pid.o
    0x08005860   0x08005860   0x00000002   Code   RO          635    i.PendSV_Handler    stm32f4xx_it.o
    0x08005862   0x08005862   0x00000002   Code   RO          636    i.SVC_Handler       stm32f4xx_it.o
    0x08005864   0x08005864   0x00000004   Code   RO          637    i.SysTick_Handler   stm32f4xx_it.o
    0x08005868   0x08005868   0x000000a0   Code   RO           14    i.SystemClock_Config  main.o
    0x08005908   0x08005908   0x00000010   Code   RO         5512    i.SystemInit        system_stm32f4xx.o
    0x08005918   0x08005918   0x000000d4   Code   RO         4249    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x080059ec   0x080059ec   0x00000016   Code   RO         4260    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08005a02   0x08005a02   0x00000010   Code   RO         4261    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08005a12   0x08005a12   0x00000026   Code   RO         4267    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005a38   0x08005a38   0x00000028   Code   RO         4269    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005a60   0x08005a60   0x0000000e   Code   RO         5181    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08005a6e   0x08005a6e   0x0000004a   Code   RO         5182    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08005ab8   0x08005ab8   0x00000090   Code   RO         5183    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08005b48   0x08005b48   0x0000001e   Code   RO         5185    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08005b66   0x08005b66   0x0000004e   Code   RO         5191    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005bb4   0x08005bb4   0x0000001a   Code   RO         5192    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08005bce   0x08005bce   0x0000001c   Code   RO         5193    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08005bea   0x08005bea   0x000000ca   Code   RO         5194    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08005cb4   0x08005cb4   0x000000fc   Code   RO         5195    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08005db0   0x08005db0   0x000000b0   Code   RO         5196    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08005e60   0x08005e60   0x00000038   Code   RO         5197    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08005e98   0x08005e98   0x0000005e   Code   RO         5198    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08005ef6   0x08005ef6   0x0000008e   Code   RO         5199    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08005f84   0x08005f84   0x0000000c   Code   RO          638    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08005f90   0x08005f90   0x0000000c   Code   RO          639    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08005f9c   0x08005f9c   0x0000000c   Code   RO          640    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08005fa8   0x08005fa8   0x00000002   Code   RO          641    i.UsageFault_Handler  stm32f4xx_it.o
    0x08005faa   0x08005faa   0x00000002   PAD
    0x08005fac   0x08005fac   0x00000028   Code   RO         6057    i.__0sprintf        mc_w.l(printfa.o)
    0x08005fd4   0x08005fd4   0x00000034   Code   RO         6060    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08006008   0x08006008   0x00000026   Code   RO         6125    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x0800602e   0x0800602e   0x00000002   PAD
    0x08006030   0x08006030   0x00000008   Code   RO         6184    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x08006038   0x08006038   0x00000150   Code   RO         5731    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x08006188   0x08006188   0x00000180   Code   RO         5743    i.__hardfp_log10f   m_wm.l(log10f.o)
    0x08006308   0x08006308   0x0000009a   Code   RO         5795    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x080063a2   0x080063a2   0x00000002   PAD
    0x080063a4   0x080063a4   0x00000190   Code   RO         5771    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x08006534   0x08006534   0x0000003a   Code   RO         5783    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x0800656e   0x0800656e   0x00000002   PAD
    0x08006570   0x08006570   0x00000014   Code   RO         6127    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08006584   0x08006584   0x00000006   Code   RO         6128    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x0800658a   0x0800658a   0x00000002   PAD
    0x0800658c   0x0800658c   0x00000010   Code   RO         6130    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x0800659c   0x0800659c   0x00000010   Code   RO         6133    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x080065ac   0x080065ac   0x00000154   Code   RO         6144    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x08006700   0x08006700   0x0000000e   Code   RO         6250    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800670e   0x0800670e   0x00000002   Code   RO         6251    i.__scatterload_null  mc_w.l(handlers.o)
    0x08006710   0x08006710   0x0000000e   Code   RO         6252    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800671e   0x0800671e   0x00000002   PAD
    0x08006720   0x08006720   0x0000000c   Code   RO         6186    i.__set_errno       mc_w.l(errno.o)
    0x0800672c   0x0800672c   0x00000184   Code   RO         6062    i._fp_digits        mc_w.l(printfa.o)
    0x080068b0   0x080068b0   0x000006b4   Code   RO         6063    i._printf_core      mc_w.l(printfa.o)
    0x08006f64   0x08006f64   0x00000024   Code   RO         6064    i._printf_post_padding  mc_w.l(printfa.o)
    0x08006f88   0x08006f88   0x0000002e   Code   RO         6065    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08006fb6   0x08006fb6   0x00000016   Code   RO         6066    i._snputc           mc_w.l(printfa.o)
    0x08006fcc   0x08006fcc   0x0000000a   Code   RO         6067    i._sputc            mc_w.l(printfa.o)
    0x08006fd6   0x08006fd6   0x00000002   PAD
    0x08006fd8   0x08006fd8   0x00000034   Code   RO         1058    i.adc_tim_dma_init  adc_app.o
    0x0800700c   0x0800700c   0x00000088   Code   RO         1493    i.calculate_bandwidth  wave_recognition.o
    0x08007094   0x08007094   0x00000088   Code   RO         1494    i.calculate_carrier_suppression  wave_recognition.o
    0x0800711c   0x0800711c   0x000000c4   Code   RO         1295    i.calculate_fft_spectrum  my_fft.o
    0x080071e0   0x080071e0   0x00000038   Code   RO         1296    i.calculate_sinad   my_fft.o
    0x08007218   0x08007218   0x0000016c   Code   RO         1297    i.calculate_thd     my_fft.o
    0x08007384   0x08007384   0x000000f0   Code   RO         1298    i.calculate_thd_n   my_fft.o
    0x08007474   0x08007474   0x00000158   Code   RO         1836    i.circuit_learning_task  scheduler.o
    0x080075cc   0x080075cc   0x00000020   Code   RO         1102    i.dac_app_init      dac_app.o
    0x080075ec   0x080075ec   0x00000068   Code   RO         1103    i.dac_app_set_amplitude  dac_app.o
    0x08007654   0x08007654   0x00000024   Code   RO         1105    i.dac_app_set_waveform  dac_app.o
    0x08007678   0x08007678   0x000000b4   Code   RO         1495    i.detect_symmetry   wave_recognition.o
    0x0800772c   0x0800772c   0x0000001c   Code   RO         1299    i.fft_init          my_fft.o
    0x08007748   0x08007748   0x000000fc   Code   RO         1496    i.find_peaks        wave_recognition.o
    0x08007844   0x08007844   0x00000060   Code   RO         1300    i.generate_hanning_window  my_fft.o
    0x080078a4   0x080078a4   0x000000dc   Code   RO         1107    i.generate_sine     dac_app.o
    0x08007980   0x08007980   0x0000007c   Code   RO         1108    i.generate_square   dac_app.o
    0x080079fc   0x080079fc   0x00000130   Code   RO         1109    i.generate_triangle  dac_app.o
    0x08007b2c   0x08007b2c   0x00000020   Code   RO         1110    i.generate_waveform  dac_app.o
    0x08007b4c   0x08007b4c   0x0000000c   Code   RO          914    i.get_current_ad_frequency  key_app.o
    0x08007b58   0x08007b58   0x000000d0   Code   RO         1301    i.get_precise_peak_frequency  my_fft.o
    0x08007c28   0x08007c28   0x00000198   Code   RO          915    i.key_proc          key_app.o
    0x08007dc0   0x08007dc0   0x00000044   Code   RO          916    i.key_read          key_app.o
    0x08007e04   0x08007e04   0x00000110   Code   RO           15    i.main              main.o
    0x08007f14   0x08007f14   0x00000032   Code   RO         1623    i.my_printf         my_usart.o
    0x08007f46   0x08007f46   0x00000002   PAD
    0x08007f48   0x08007f48   0x000002fc   Code   RO         1302    i.output_fft_spectrum  my_fft.o
    0x08008244   0x08008244   0x000000ac   Code   RO         1497    i.preprocess_signal  wave_recognition.o
    0x080082f0   0x080082f0   0x00000718   Code   RO         1624    i.process_uart1_command  my_usart.o
    0x08008a08   0x08008a08   0x0000026c   Code   RO         1625    i.process_uart2_command  my_usart.o
    0x08008c74   0x08008c74   0x0000032c   Code   RO         1498    i.recognize_waveform  wave_recognition.o
    0x08008fa0   0x08008fa0   0x00000024   Code   RO         1303    i.round_to_nearest_k  my_fft.o
    0x08008fc4   0x08008fc4   0x0000000c   Code   RO         1837    i.scheduler_init    scheduler.o
    0x08008fd0   0x08008fd0   0x00000040   Code   RO         1838    i.scheduler_run     scheduler.o
    0x08009010   0x08009010   0x000000d0   Code   RO         1839    i.start_circuit_learning  scheduler.o
    0x080090e0   0x080090e0   0x00000038   Code   RO         1111    i.start_dac_dma     dac_app.o
    0x08009118   0x08009118   0x00000020   Code   RO         1112    i.stop_dac_dma      dac_app.o
    0x08009138   0x08009138   0x0000015c   Code   RO         1113    i.update_timer_frequency  dac_app.o
    0x08009294   0x08009294   0x00000020   Data   RO         1955    .constdata          dds_test.o
    0x080092b4   0x080092b4   0x00000008   Data   RO         2898    .constdata          stm32f4xx_hal_dma.o
    0x080092bc   0x080092bc   0x00000010   Data   RO         5513    .constdata          system_stm32f4xx.o
    0x080092cc   0x080092cc   0x00000008   Data   RO         5514    .constdata          system_stm32f4xx.o
    0x080092d4   0x080092d4   0x00000040   Data   RO         5746    .constdata          m_wm.l(log10f.o)
    0x08009314   0x08009314   0x00000020   Data   RO         6145    .constdata          m_wm.l(rredf.o)
    0x08009334   0x08009334   0x00000081   Data   RO         6214    .constdata          mc_w.l(ctype_o.o)
    0x080093b5   0x080093b5   0x00000003   PAD
    0x080093b8   0x080093b8   0x00000004   Data   RO         6215    .constdata          mc_w.l(ctype_o.o)
    0x080093bc   0x080093bc   0x00000046   Data   RO         1499    .conststring        wave_recognition.o
    0x08009402   0x08009402   0x00000002   PAD
    0x08009404   0x08009404   0x000000b1   Data   RO         1956    .conststring        dds_test.o
    0x080094b5   0x080094b5   0x00000001   PAD
    0x080094b6   0x080094b6   0x00000800   Data   RO         5612    .rodata.armBitRevTable  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08009cb6   0x08009cb6   0x00000002   PAD
    0x08009cb8   0x08009cb8   0x00000804   Data   RO         5724    .rodata.sinTable_f32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800a4bc   0x0800a4bc   0x00008000   Data   RO         5630    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080124bc   0x080124bc   0x00000020   Data   RO         6248    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08012588, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080124dc, Size: 0x0000c300, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080124dc   0x00000008   Data   RW          481    .data               fmc.o
    0x20000008   0x080124e4   0x0000000c   Data   RW          918    .data               key_app.o
    0x20000014   0x080124f0   0x00000001   Data   RW         1061    .data               adc_app.o
    0x20000015   0x080124f1   0x00000003   PAD
    0x20000018   0x080124f4   0x0000000c   Data   RW         1115    .data               dac_app.o
    0x20000024   0x08012500   0x00000024   Data   RW         1217    .data               software_dds.o
    0x20000048   0x08012524   0x00000010   Data   RW         1630    .data               my_usart.o
    0x20000058   0x08012534   0x00000034   Data   RW         1843    .data               scheduler.o
    0x2000008c   0x08012568   0x0000000c   Data   RW         1957    .data               dds_test.o
    0x20000098   0x08012574   0x0000000c   Data   RW         3358    .data               stm32f4xx_hal.o
    0x200000a4   0x08012580   0x00000004   Data   RW         5515    .data               system_stm32f4xx.o
    0x200000a8   0x08012584   0x00000004   Data   RW         6187    .data               mc_w.l(errno.o)
    0x200000ac        -       0x000000a8   Zero   RW          371    .bss                adc.o
    0x20000154        -       0x00000074   Zero   RW          413    .bss                dac.o
    0x200001c8        -       0x00000050   Zero   RW          480    .bss                fmc.o
    0x20000218        -       0x00000090   Zero   RW          528    .bss                tim.o
    0x200002a8        -       0x00000138   Zero   RW          577    .bss                usart.o
    0x200003e0        -       0x00003000   Zero   RW          768    .bss                ad_measure.o
    0x200033e0        -       0x00000018   Zero   RW          822    .bss                da_output.o
    0x200033f8        -       0x00003000   Zero   RW         1059    .bss                adc_app.o
    0x200063f8        -       0x00000100   Zero   RW         1114    .bss                dac_app.o
    0x200064f8        -       0x00000800   Zero   RW         1216    .bss                software_dds.o
    0x20006cf8        -       0x00004014   Zero   RW         1304    .bss                my_fft.o
    0x2000ad0c        -       0x00000200   Zero   RW         1627    .bss                my_usart.o
    0x2000af0c        -       0x00000fd0   Zero   RW         1841    .bss                scheduler.o
    0x2000bedc        -       0x00000024   Zero   RW         1902    .bss                app_pid.o
    0x2000bf00        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0      12288       1148   ad_measure.o
       264         30          0          0        168       1887   adc.o
        80         26          0          1      12288       2092   adc_app.o
        80         20          0          0         36        792   app_pid.o
        38          0          0          0          0       1511   cmd_to_fun.o
       312         28          0          0         24       3433   da_output.o
       240         32          0          0        116       1877   dac.o
      1288        100          0         12        256      10145   dac_app.o
      3372       2108        209         12          0       9417   dds_test.o
        76          4          0          0          0        874   dma.o
       284         34          0          8         80       2374   fmc.o
       416         34          0          0          0       1275   gpio.o
       488        244          0         12          0       2413   key_app.o
       436        116          0          0          0     806204   main.o
      1988        450          0          0      16404       9526   my_fft.o
      2958       1760          0         16        512       6733   my_usart.o
       628        296          0         52       4048       4275   scheduler.o
       956        508          0         36       2048       7399   software_dds.o
        36          8        428          0       1024        864   startup_stm32f429xx.o
       188         30          0         12          0       9777   stm32f4xx_hal.o
      1770         62          0          0          0       9833   stm32f4xx_hal_adc.o
         2          0          0          0          0       1061   stm32f4xx_hal_adc_ex.o
       244         16          0          0          0      34393   stm32f4xx_hal_cortex.o
       720         24          0          0          0       9848   stm32f4xx_hal_dac.o
        56          0          0          0          0       4221   stm32f4xx_hal_dac_ex.o
      1272         18          8          0          0       7742   stm32f4xx_hal_dma.o
       992         42          0          0          0       4261   stm32f4xx_hal_gpio.o
        48          6          0          0          0        914   stm32f4xx_hal_msp.o
       124         14          0          0          0       1356   stm32f4xx_hal_pwr_ex.o
      1768        116          0          0          0       8451   stm32f4xx_hal_rcc.o
        94          0          0          0          0       1292   stm32f4xx_hal_sram.o
      1048         22          0          0          0       9729   stm32f4xx_hal_tim.o
       154          0          0          0          0       1488   stm32f4xx_hal_tim_ex.o
      2526         20          0          0          0      19614   stm32f4xx_hal_uart.o
        96         36          0          0          0       7403   stm32f4xx_it.o
       330          8          0          0          0       4604   stm32f4xx_ll_fmc.o
        16          4         24          4          0       1211   system_stm32f4xx.o
       240         30          0          0        144       2435   tim.o
       600         82          0          0        312       4131   usart.o
      1688        338         70          0          0       8842   wave_recognition.o

    ----------------------------------------------------------------------
     27938       <USER>        <GROUP>        168      49748    1026845   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          3          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       190          0          0          0          0       2756   arm_bitreversal.o
      1812          0          0          0          0       9774   arm_cfft_radix4_f32.o
       148         20          0          0          0       1573   arm_cfft_radix4_init_f32.o
       340          0          0          0          0       2270   arm_cmplx_mag_f32.o
         0          0      36868          0          0       6294   arm_common_tables.o
       152         12          0          0          0       1104   arm_cos_f32.o
       336         56          0          0          0        136   cosf.o
        38          0          0          0          0        116   fpclassifyf.o
        58         18          0          0          0        464   funder.o
       384         52         64          0          0        140   log10f.o
       154          0          0          0          0        140   roundf.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
        58          0          0          0          0        136   sqrtf.o
        28          0          0          0          0         68   _chval.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        20         10          0          4          0        136   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2310         92          0          0          0        680   printfa.o
        20          0          0          0          0         68   strchr.o
        28          0          0          0          0         76   strcmp.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        30          0          0          0          0         80   strncmp.o
        36          0          0          0          0         80   strstr.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        44          0          0          0          0         68   ffixul.o
        60          0          0          0          0         76   frnd.o

    ----------------------------------------------------------------------
      9158        <USER>      <GROUP>          4          0      28895   Library Totals
        20          0          5          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2642         32      36868          0          0      23771   arm_cortexM4lf_math.lib
      1768        206         96          0          0       1504   m_wm.l
      3210        122        133          4          0       2272   mc_w.l
      1518          0          0          0          0       1348   mf_w.l

    ----------------------------------------------------------------------
      9158        <USER>      <GROUP>          4          0      28895   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     37096       7026      37876        172      49748    1033252   Grand Totals
     37096       7026      37876        172      49748    1033252   ELF Image Totals
     37096       7026      37876        172          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                74972 (  73.21kB)
    Total RW  Size (RW Data + ZI Data)             49920 (  48.75kB)
    Total ROM Size (Code + RO Data + RW Data)      75144 (  73.38kB)

==============================================================================


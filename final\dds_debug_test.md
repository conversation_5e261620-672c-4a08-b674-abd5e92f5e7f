# DDS调试测试指南

## 🔧 调试步骤

### 1. 编译并烧录代码
确保所有修改都已编译并烧录到STM32

### 2. 串口连接测试
- 连接UART1（调试串口）
- 波特率：115200
- 数据位：8，停止位：1，无校验

### 3. 基本功能测试

#### 3.1 查看帮助信息
```
dds help
```
应该显示所有可用命令，包括新增的`debug`命令

#### 3.2 查看调试信息
```
debug
```
应该显示：
- 测试配置信息
- DDS配置信息  
- TIM6定时器配置
- 预期的中断频率（应该是100kHz）

#### 3.3 启动DDS测试
```
start
```
观察串口输出的调试信息，确认：
- DDS启动成功
- TIM6状态正常
- 相位增量计算正确

#### 3.4 查看状态
```
status
```
确认DDS配置与测试配置一致

### 4. 频率测试

#### 4.1 设置低频模式
```
low freq
```

#### 4.2 调整频率
```
plus
plus
minus
```
每次调整后查看状态，确认频率变化正确

#### 4.3 设置高频模式
```
high freq
```

### 5. 幅度测试

#### 5.1 设置不同幅度
```
amp 25
amp 50
amp 75
amp 100
```
每次设置后查看状态和调试信息

### 6. 预期问题和解决方案

#### 6.1 如果输出频率仍然是10kHz
- 检查TIM6配置是否正确更新
- 确认中断是否正常工作
- 查看相位增量计算

#### 6.2 如果幅度控制不正常
- 检查DAC输出范围
- 确认幅度计算公式
- 验证DAC配置

#### 6.3 如果串口无响应
- 检查串口连接
- 确认波特率设置
- 验证命令格式

## 🎯 关键调试点

### 1. TIM6配置验证
- 预分频器：17 (18-1)
- 自动重装载：99 (100-1)  
- 预期频率：180MHz/(18×100) = 100kHz

### 2. 相位增量计算
- 1kHz信号：phase_increment = (1000 << 32) / 100000
- 应该约为0x28F5C28F

### 3. 幅度控制
- 50%幅度：dds_config.amplitude = (50 × 4095) / 100 = 2047
- DAC输出范围：0-4095，中心点2048

## 📊 测试记录

记录以下信息：
- [ ] TIM6实际中断频率
- [ ] 各频率设置下的实际输出频率
- [ ] 各幅度设置下的实际输出电压
- [ ] 串口调试信息是否正常
- [ ] 命令响应是否及时

## 🚀 下一步优化

根据测试结果：
1. 调整定时器配置
2. 优化相位增量计算
3. 改进幅度控制算法
4. 增强错误处理

/*******************************************************************************
 * @file      dds_test.c
 * <AUTHOR>
 * @version   V1.0
 * @date      2025-07-31
 * @brief     软件DDS测试模块实现
 * @note      实现串口控制的频率步进测试功能
 *******************************************************************************/

#include "dds_test.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

// 全局测试配置
static DDS_TestConfig_t test_config = {
    .mode = FREQ_MODE_LOW,
    .frequency = 1000,      // 初始1kHz
    .amplitude = 80,        // 初始80%幅度
    .test_active = 0
};

// 频率范围定义
#define LOW_FREQ_MIN    100     // 低频最小值 100Hz
#define LOW_FREQ_MAX    3000    // 低频最大值 3000Hz
#define LOW_FREQ_STEP   100     // 低频步进 100Hz

#define HIGH_FREQ_MIN   1000    // 高频最小值 1kHz
#define HIGH_FREQ_MAX   50000   // 高频最大值 50kHz
#define HIGH_FREQ_STEP  200     // 高频步进 200Hz

/**
 * @brief  初始化DDS测试模块
 * @param  None
 * @retval None
 */
void DDS_Test_Init(void)
{
    my_printf(&huart1, "\r\n=== Software DDS Test Module ===\r\n");
    my_printf(&huart1, "Low Freq Mode: 100Hz-3000Hz, Step 100Hz\r\n");
    my_printf(&huart1, "High Freq Mode: 1kHz-50kHz, Step 200Hz\r\n");
    my_printf(&huart1, "Type 'dds help' for commands\r\n");

    DDS_Test_ShowStatus();
}

/**
 * @brief  处理串口DDS测试命令
 * @param  command: 接收到的命令字符串
 * @retval None
 */
void DDS_Test_ProcessCommand(char* command)
{
    // 去除换行符
    char* newline = strchr(command, '\r');
    if (newline) *newline = '\0';
    newline = strchr(command, '\n');
    if (newline) *newline = '\0';
    
    my_printf(&huart1, "DDS Command: %s\r\n", command);

    // 命令解析
    if (strcmp(command, "low freq") == 0) {
        DDS_Test_SetMode(FREQ_MODE_LOW);
    }
    else if (strcmp(command, "high freq") == 0) {
        DDS_Test_SetMode(FREQ_MODE_HIGH);
    }
    else if (strcmp(command, "plus") == 0) {
        DDS_Test_FreqPlus();
    }
    else if (strcmp(command, "minus") == 0) {
        DDS_Test_FreqMinus();
    }
    else if (strcmp(command, "start") == 0) {
        DDS_Test_Start();
    }
    else if (strcmp(command, "stop") == 0) {
        DDS_Test_Stop();
    }
    else if (strcmp(command, "status") == 0) {
        DDS_Test_ShowStatus();
    }
    else if (strcmp(command, "dds help") == 0) {
        DDS_Test_ShowHelp();
    }
    else if (strncmp(command, "amp ", 4) == 0) {
        // 设置幅度: "amp 50" 设置为50%
        int amplitude = atoi(command + 4);
        if (amplitude >= 0 && amplitude <= 100) {
            DDS_Test_SetAmplitude(amplitude);
        } else {
            my_printf(&huart1, "Error: Amplitude must be 0-100%%\r\n");
        }
    }
    else if (strcmp(command, "debug") == 0) {
        // 显示详细调试信息
        DDS_Test_ShowDebugInfo();
    }
    else {
        my_printf(&huart1, "Unknown command. Type 'dds help' for help\r\n");
    }
}

/**
 * @brief  设置频率模式
 * @param  mode: 频率模式
 * @retval None
 */
void DDS_Test_SetMode(FreqMode_t mode)
{
    test_config.mode = mode;
    
    if (mode == FREQ_MODE_LOW) {
        // 切换到低频模式，设置合适的初始频率
        if (test_config.frequency < LOW_FREQ_MIN || test_config.frequency > LOW_FREQ_MAX) {
            test_config.frequency = 1000; // 默认1kHz
        }
        my_printf(&huart1, "Switched to LOW FREQ mode (100Hz-3000Hz, step 100Hz)\r\n");
    } else {
        // 切换到高频模式，设置合适的初始频率
        if (test_config.frequency < HIGH_FREQ_MIN || test_config.frequency > HIGH_FREQ_MAX) {
            test_config.frequency = 1000; // 默认1kHz
        }
        my_printf(&huart1, "Switched to HIGH FREQ mode (1kHz-50kHz, step 200Hz)\r\n");
    }
    
    // 如果测试激活，更新输出
    if (test_config.test_active) {
        DDS_Setup(test_config.frequency, test_config.amplitude, 0, DDS_WAVEFORM_SINE);
    }
    
    DDS_Test_ShowStatus();
}

/**
 * @brief  频率增加一个步进
 * @param  None
 * @retval None
 */
void DDS_Test_FreqPlus(void)
{
    uint32_t new_freq = test_config.frequency;
    uint32_t step, max_freq;
    
    if (test_config.mode == FREQ_MODE_LOW) {
        step = LOW_FREQ_STEP;
        max_freq = LOW_FREQ_MAX;
    } else {
        step = HIGH_FREQ_STEP;
        max_freq = HIGH_FREQ_MAX;
    }
    
    new_freq += step;
    
    if (new_freq <= max_freq) {
        test_config.frequency = new_freq;
        my_printf(&huart1, "Frequency increased to %lu Hz (+%lu Hz)\r\n",
                  test_config.frequency, step);


        // 如果测试激活，更新输出
        if (test_config.test_active) {
            DDS_Setup(test_config.frequency, test_config.amplitude, 0, DDS_WAVEFORM_SINE);
        }
    } else {
        my_printf(&huart1, "Frequency limit reached! Max: %lu Hz\r\n", max_freq);
    }
    
    DDS_Test_ShowStatus();
}

/**
 * @brief  频率减少一个步进
 * @param  None
 * @retval None
 */
void DDS_Test_FreqMinus(void)
{
    uint32_t new_freq = test_config.frequency;
    uint32_t step, min_freq;
    
    if (test_config.mode == FREQ_MODE_LOW) {
        step = LOW_FREQ_STEP;
        min_freq = LOW_FREQ_MIN;
    } else {
        step = HIGH_FREQ_STEP;
        min_freq = HIGH_FREQ_MIN;
    }
    
    if (new_freq >= step + min_freq) {
        new_freq -= step;
        test_config.frequency = new_freq;
        my_printf(&huart1, "Frequency decreased to %lu Hz (-%lu Hz)\r\n",
                  test_config.frequency, step);

        // 如果测试激活，更新输出
        if (test_config.test_active) {
            DDS_Setup(test_config.frequency, test_config.amplitude, 0, DDS_WAVEFORM_SINE);
        }
    } else {
        my_printf(&huart1, "Frequency limit reached! Min: %lu Hz\r\n", min_freq);
    }
    
    DDS_Test_ShowStatus();
}

/**
 * @brief  设置输出幅度
 * @param  amplitude: 幅度百分比 (0-100)
 * @retval None
 */
void DDS_Test_SetAmplitude(uint8_t amplitude)
{
    test_config.amplitude = amplitude;
    my_printf(&huart1, "Amplitude set to %d%%\r\n", amplitude);

    // 如果测试激活，更新输出
    if (test_config.test_active) {
        DDS_Setup(test_config.frequency, test_config.amplitude, 0, DDS_WAVEFORM_SINE);
    }

    DDS_Test_ShowStatus();
}

/**
 * @brief  启动DDS测试
 * @param  None
 * @retval None
 */
void DDS_Test_Start(void)
{
    test_config.test_active = 1;
    
    DDS_Setup(test_config.frequency, test_config.amplitude, 0, DDS_WAVEFORM_SINE);
    DDS_Start();
    
    my_printf(&huart1, "DDS Test STARTED!\r\n");
    DDS_Test_ShowStatus();
}

/**
 * @brief  停止DDS测试
 * @param  None
 * @retval None
 */
void DDS_Test_Stop(void)
{
    test_config.test_active = 0;
    DDS_Stop();

    my_printf(&huart1, "DDS Test STOPPED!\r\n");
    DDS_Test_ShowStatus();
}

/**
 * @brief  显示当前DDS状态
 * @param  None
 * @retval None
 */
void DDS_Test_ShowStatus(void)
{
    const char* mode_str = (test_config.mode == FREQ_MODE_LOW) ? "LOW" : "HIGH";
    const char* status_str = test_config.test_active ? "RUNNING" : "STOPPED";

    my_printf(&huart1, "\r\n--- DDS Status ---\r\n");
    my_printf(&huart1, "Mode: %s FREQ\r\n", mode_str);
    my_printf(&huart1, "Frequency: %lu Hz\r\n", test_config.frequency);
    my_printf(&huart1, "Amplitude: %d%%\r\n", test_config.amplitude);
    my_printf(&huart1, "Status: %s\r\n", status_str);
    my_printf(&huart1, "Output Voltage: %.2fV peak-to-peak\r\n",
              (test_config.amplitude * 3.3f) / 100.0f);

    // 显示底层DDS配置用于调试
    DDS_Config_t dds_cfg = DDS_GetConfig();
    my_printf(&huart1, "DDS Config: freq=%lu Hz, amp=%d, enabled=%d\r\n",
              dds_cfg.frequency, dds_cfg.amplitude, dds_cfg.enabled);

    my_printf(&huart1, "-----------------\r\n\r\n");
}

/**
 * @brief  显示帮助信息
 * @param  None
 * @retval None
 */
void DDS_Test_ShowHelp(void)
{
    my_printf(&huart1, "\r\n=== DDS Test Commands ===\r\n");
    my_printf(&huart1, "low freq    - Switch to low frequency mode (100Hz-3000Hz, step 100Hz)\r\n");
    my_printf(&huart1, "high freq   - Switch to high frequency mode (1kHz-50kHz, step 200Hz)\r\n");
    my_printf(&huart1, "plus        - Increase frequency by one step\r\n");
    my_printf(&huart1, "minus       - Decrease frequency by one step\r\n");
    my_printf(&huart1, "amp XX      - Set amplitude to XX%% (0-100)\r\n");
    my_printf(&huart1, "start       - Start DDS output\r\n");
    my_printf(&huart1, "stop        - Stop DDS output\r\n");
    my_printf(&huart1, "status      - Show current status\r\n");
    my_printf(&huart1, "debug       - Show detailed debug info\r\n");
    my_printf(&huart1, "dds help    - Show this help\r\n");
    my_printf(&huart1, "========================\r\n\r\n");

    my_printf(&huart1, "Example usage:\r\n");
    my_printf(&huart1, "1. low freq  -> plus -> plus -> start\r\n");
    my_printf(&huart1, "2. high freq -> amp 50 -> start\r\n");
    my_printf(&huart1, "3. plus -> plus -> minus\r\n\r\n");
}

/**
 * @brief  显示详细调试信息
 * @param  None
 * @retval None
 */
void DDS_Test_ShowDebugInfo(void)
{
    my_printf(&huart1, "\r\n=== DDS Debug Info ===\r\n");

    // 显示测试配置
    my_printf(&huart1, "Test Config:\r\n");
    my_printf(&huart1, "  mode: %d, freq: %lu Hz, amp: %d%%, active: %d\r\n",
              test_config.mode, test_config.frequency, test_config.amplitude, test_config.test_active);

    // 显示DDS配置
    DDS_Config_t dds_cfg = DDS_GetConfig();
    my_printf(&huart1, "DDS Config:\r\n");
    my_printf(&huart1, "  freq: %lu Hz, amp: %d, phase: %d, waveform: %d, enabled: %d\r\n",
              dds_cfg.frequency, dds_cfg.amplitude, dds_cfg.phase_offset, dds_cfg.waveform, dds_cfg.enabled);

    // 显示定时器状态
    extern TIM_HandleTypeDef htim6;
    my_printf(&huart1, "TIM6 Config:\r\n");
    my_printf(&huart1, "  Prescaler: %d, Period: %d\r\n",
              htim6.Init.Prescaler, htim6.Init.Period);
    my_printf(&huart1, "  Expected freq: %d Hz\r\n",
              180000000 / ((htim6.Init.Prescaler + 1) * (htim6.Init.Period + 1)));

    my_printf(&huart1, "======================\r\n");
}

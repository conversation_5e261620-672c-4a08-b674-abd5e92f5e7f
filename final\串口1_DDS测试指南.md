# 串口1 DDS测试指南

## 🔧 串口1完整命令支持

现在串口1已经支持所有DDS测试命令，您只需要使用一个串口即可完成所有调试工作。

### 📋 支持的命令列表

#### 系统命令
- `help` - 显示系统帮助
- `learn` - 启动电路学习
- `dds help` - 显示DDS命令帮助

#### DDS测试命令
- `start` - 启动DDS输出
- `stop` - 停止DDS输出  
- `status` - 显示当前状态
- `debug` - 显示详细调试信息
- `plus` - 增加频率
- `minus` - 减少频率
- `amp XX` - 设置幅度为XX%（0-100）
- `low freq` - 切换到低频模式
- `high freq` - 切换到高频模式

## 🧪 完整测试流程

### 1. 基本功能验证
```bash
# 查看所有可用命令
help

# 查看DDS专用命令
dds help

# 查看详细调试信息
debug
```

### 2. DDS系统诊断
```bash
# 显示详细调试信息，验证：
debug
# 期望看到：
# - TIM6 Prescaler: 17, Period: 99
# - Expected freq: 100000 Hz
# - DDS Config完整信息
```

### 3. DDS功能测试
```bash
# 查看初始状态
status

# 启动DDS
start

# 再次查看状态（enabled应该变为1）
status
debug

# 测试频率控制
plus
status
plus
status  
minus
status

# 测试幅度控制
amp 25
status
amp 50
status
amp 75
status
amp 100
status

# 测试模式切换
low freq
status
high freq
status

# 停止DDS
stop
status
```

## 🎯 关键验证点

### 1. TIM6配置验证
使用`debug`命令检查：
```
TIM6 Config:
  Prescaler: 17, Period: 99
  Expected freq: 100000 Hz  ← 这里应该是100000，不是10000
```

### 2. DDS配置验证
使用`status`和`debug`命令检查：
```
DDS Config: freq=1000 Hz, amp=2047, enabled=1
```

### 3. 频率控制验证
- `plus`命令应该按步进增加频率
- `minus`命令应该按步进减少频率
- 低频模式：100Hz步进，范围100-3000Hz
- 高频模式：200Hz步进，范围1000-50000Hz

### 4. 幅度控制验证
- `amp 0` → amp=0
- `amp 50` → amp=2047 (约50%的4095)
- `amp 100` → amp=4095

## 🔍 问题诊断

### 如果命令无响应：
1. 检查串口连接（UART1，115200波特率）
2. 确认命令格式正确（注意空格）
3. 查看是否有"DMA Received: [命令]"的回显

### 如果TIM6频率仍是10000Hz：
1. 重新编译并烧录代码
2. 检查CubeMX是否覆盖了手动修改
3. 确认tim.c中的修改已生效

### 如果频率控制无效：
1. 使用`debug`查看phase_increment值
2. 确认DDS enabled=1
3. 检查相位累加器是否正常工作

### 如果幅度控制无效：
1. 使用`debug`查看amplitude值变化
2. 确认DAC配置正常
3. 检查幅度计算公式

## 📊 测试记录表

```
测试项目                    | 预期结果              | 实际结果    | 状态
---------------------------|---------------------|-----------|------
help命令                   | 显示系统命令列表         |           | [ ]
dds help命令               | 显示DDS命令列表         |           | [ ]
debug命令                  | 显示详细调试信息         |           | [ ]
TIM6 Expected freq         | 100000 Hz          |           | [ ]
start命令                  | DDS enabled=1      |           | [ ]
plus命令                   | 频率按步进增加          |           | [ ]
minus命令                  | 频率按步进减少          |           | [ ]
amp 50命令                 | amplitude=2047     |           | [ ]
low freq命令               | 切换到低频模式          |           | [ ]
high freq命令              | 切换到高频模式          |           | [ ]
stop命令                   | DDS enabled=0      |           | [ ]
```

## 🚀 成功标准

如果以下所有项目都正常，说明DDS系统修复成功：

- [ ] 所有命令都能正确响应
- [ ] TIM6预期频率显示为100000 Hz
- [ ] 频率控制能按步进正确变化
- [ ] 幅度控制能在0-100%范围内调节
- [ ] 调试信息显示完整且准确
- [ ] 实际输出频率与设置值匹配
- [ ] 实际输出幅度与设置值匹配

## 📝 下一步

测试完成后，请告诉我：
1. 哪些命令工作正常
2. 哪些功能还有问题
3. debug命令显示的关键参数值
4. 实际测量的输出频率和幅度

这样我们就能准确定位剩余问题并进行针对性修复。

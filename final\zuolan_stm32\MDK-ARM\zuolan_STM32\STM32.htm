<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\zuolan_STM32\STM32.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\zuolan_STM32\STM32.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 01 17:47:36 2025
<BR><P>
<H3>Maximum Stack Usage =        888 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
circuit_learning_task &rArr; DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1f]">CAN1_RX0_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f]">CAN1_RX0_IRQHandler</a><BR>
 <LI><a href="#[5]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">BusFault_Handler</a><BR>
 <LI><a href="#[3]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">HardFault_Handler</a><BR>
 <LI><a href="#[4]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">MemManage_Handler</a><BR>
 <LI><a href="#[10e]">UART_EndTxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10e]">UART_EndTxTransfer</a><BR>
 <LI><a href="#[10f]">UART_EndRxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10f]">UART_EndRxTransfer</a><BR>
 <LI><a href="#[6]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[68]">ADC_DMAConvCplt</a> from stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[6a]">ADC_DMAError</a> from stm32f4xx_hal_adc.o(i.ADC_DMAError) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[69]">ADC_DMAHalfConvCplt</a> from stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[1d]">ADC_IRQHandler</a> from stm32f4xx_it.o(i.ADC_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1f]">CAN1_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[20]">CAN1_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[21]">CAN1_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1e]">CAN1_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4b]">CAN2_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4c]">CAN2_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4d]">CAN2_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4a]">CAN2_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6e]">DAC_DMAConvCpltCh1</a> from stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[6b]">DAC_DMAConvCpltCh2</a> from stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[70]">DAC_DMAErrorCh1</a> from stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[6d]">DAC_DMAErrorCh2</a> from stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[6f]">DAC_DMAHalfConvCpltCh1</a> from stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[6c]">DAC_DMAHalfConvCpltCh2</a> from stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[59]">DCMI_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3a]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[64]">DMA2D_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream0_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[51]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[48]">ETH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[49]">ETH_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[11]">EXTI0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[33]">EXTI15_10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[12]">EXTI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[13]">EXTI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[14]">EXTI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[15]">EXTI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[22]">EXTI9_5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[f]">FLASH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3b]">FMC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5b]">FPU_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5a]">HASH_RNG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2b]">I2C1_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2a]">I2C1_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2d]">I2C2_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2c]">I2C2_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[54]">I2C3_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[53]">I2C3_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[63]">LTDC_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[62]">LTDC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4e]">OTG_FS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[35]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[56]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[55]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[58]">OTG_HS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[57]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[c]">PVD_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[10]">RCC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[34]">RTC_Alarm_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[e]">RTC_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[61]">SAI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3c]">SDIO_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2e]">SPI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2f]">SPI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3e]">SPI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5e]">SPI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5f]">SPI5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[60]">SPI6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[66]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[d]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[23]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[26]">TIM1_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[25]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[24]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[27]">TIM2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[28]">TIM3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[29]">TIM4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3d]">TIM5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[41]">TIM6_DAC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[42]">TIM7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[36]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[39]">TIM8_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[38]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[37]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3f]">UART4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[40]">UART5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5c]">UART7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5d]">UART8_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[71]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[74]">UART_DMAError</a> from stm32f4xx_hal_uart.o(i.UART_DMAError) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[72]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[73]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[30]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[31]">USART2_IRQHandler</a> from stm32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[32]">USART3_IRQHandler</a> from stm32f4xx_it.o(i.USART3_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[52]">USART6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[b]">WWDG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[67]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[76]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[75]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[78]">circuit_learning_task</a> from scheduler.o(i.circuit_learning_task) referenced from scheduler.o(.data)
 <LI><a href="#[77]">key_proc</a> from key_app.o(i.key_proc) referenced from scheduler.o(.data)
 <LI><a href="#[65]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[79]">process_uart2_command</a> from my_usart.o(i.process_uart2_command) referenced from scheduler.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[67]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[170]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[7a]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[96]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[171]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[172]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[173]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[174]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[175]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[176]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_CalculatePhaseIncrement
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[10d]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[d3]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;circuit_learning_task
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_TestDAC
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[177]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[80]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[178]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[179]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[7f]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart2_command
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[dc]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FMC_MspInit
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fft_spectrum
</UL>

<P><STRONG><a name="[17a]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[81]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[167]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart2_command
</UL>

<P><STRONG><a name="[c6]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[a3]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_UART_Status
</UL>

<P><STRONG><a name="[c7]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[169]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[c8]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[82]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[85]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_peaks
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_symmetry
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_carrier_suppression
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[89]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_symmetry
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_carrier_suppression
</UL>

<P><STRONG><a name="[8a]"></a>__aeabi_f2ulz</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, ffixul.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
</UL>

<P><STRONG><a name="[8b]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_symmetry
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_carrier_suppression
</UL>

<P><STRONG><a name="[d2]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;circuit_learning_task
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_peaks
</UL>

<P><STRONG><a name="[17b]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[16c]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[13f]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[8c]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_peaks
</UL>

<P><STRONG><a name="[17c]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[142]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[7e]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2ulz
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[17d]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[7d]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[17e]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[84]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[17f]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frnd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[180]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>_frnd</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, frnd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _frnd
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_roundf
</UL>

<P><STRONG><a name="[88]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[86]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[91]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[93]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[94]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[95]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7b]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[181]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[92]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[182]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[8e]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[8f]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[97]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
</UL>

<P><STRONG><a name="[9b]"></a>arm_bitreversal_f32</STRONG> (Thumb, 190 bytes, Stack size 40 bytes, arm_bitreversal.o(.text.arm_bitreversal_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = arm_bitreversal_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
</UL>

<P><STRONG><a name="[98]"></a>arm_cfft_radix4_f32</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_inverse_f32
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_f32
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitreversal_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fft_spectrum
</UL>

<P><STRONG><a name="[153]"></a>arm_cfft_radix4_init_f32</STRONG> (Thumb, 148 bytes, Stack size 0 bytes, arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32))
<BR><BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_init
</UL>

<P><STRONG><a name="[9c]"></a>arm_cmplx_mag_f32</STRONG> (Thumb, 340 bytes, Stack size 40 bytes, arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = arm_cmplx_mag_f32 &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fft_spectrum
</UL>

<P><STRONG><a name="[156]"></a>arm_cos_f32</STRONG> (Thumb, 152 bytes, Stack size 0 bytes, arm_cos_f32.o(.text.arm_cos_f32))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_hanning_window
</UL>

<P><STRONG><a name="[9a]"></a>arm_radix4_butterfly_f32</STRONG> (Thumb, 858 bytes, Stack size 68 bytes, arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = arm_radix4_butterfly_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
</UL>

<P><STRONG><a name="[99]"></a>arm_radix4_butterfly_inverse_f32</STRONG> (Thumb, 890 bytes, Stack size 68 bytes, arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = arm_radix4_butterfly_inverse_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
</UL>

<P><STRONG><a name="[1d]"></a>ADC_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ADC_IRQHandler &rArr; HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15d]"></a>CTRL_INIT</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, cmd_to_fun.o(i.CTRL_INIT))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a2]"></a>Check_UART_Status</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, my_usart.o(i.Check_UART_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Check_UART_Status &rArr; MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6e]"></a>DAC_DMAConvCpltCh1</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAConvCpltCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConvCpltCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[6b]"></a>DAC_DMAConvCpltCh2</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAConvCpltCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ConvCpltCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[70]"></a>DAC_DMAErrorCh1</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAErrorCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ErrorCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[6d]"></a>DAC_DMAErrorCh2</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAErrorCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ErrorCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[6f]"></a>DAC_DMAHalfConvCpltCh1</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAHalfConvCpltCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConvHalfCpltCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[6c]"></a>DAC_DMAHalfConvCpltCh2</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAHalfConvCpltCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ConvHalfCpltCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[ad]"></a>DA_Apply_Settings</STRONG> (Thumb, 206 bytes, Stack size 32 bytes, da_output.o(i.DA_Apply_Settings))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = DA_Apply_Settings &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_roundf
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2ulz
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_FPGA_STOP
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_FPGA_START
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[b0]"></a>DA_FPGA_START</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, cmd_to_fun.o(i.DA_FPGA_START))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
</UL>

<P><STRONG><a name="[ae]"></a>DA_FPGA_STOP</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, cmd_to_fun.o(i.DA_FPGA_STOP))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
</UL>

<P><STRONG><a name="[b1]"></a>DA_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, da_output.o(i.DA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = DA_Init &rArr; DA_Apply_Settings &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b2]"></a>DA_SetConfig</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, da_output.o(i.DA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Init
</UL>

<P><STRONG><a name="[b3]"></a>DDS_CalculatePhaseIncrement</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, software_dds.o(i.DDS_CalculatePhaseIncrement))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DDS_CalculatePhaseIncrement &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_SetFrequency
</UL>

<P><STRONG><a name="[d0]"></a>DDS_GetConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, software_dds.o(i.DDS_GetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowDebugInfo
</UL>

<P><STRONG><a name="[d1]"></a>DDS_GetInterruptStats</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, software_dds.o(i.DDS_GetInterruptStats))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowDebugInfo
</UL>

<P><STRONG><a name="[b4]"></a>DDS_Init</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, software_dds.o(i.DDS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = DDS_Init &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Start
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_SetValue
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_CalculatePhaseIncrement
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b9]"></a>DDS_SetAmplitude</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, software_dds.o(i.DDS_SetAmplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = DDS_SetAmplitude &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
</UL>

<P><STRONG><a name="[ba]"></a>DDS_SetFrequency</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, software_dds.o(i.DDS_SetFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_CalculatePhaseIncrement
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
</UL>

<P><STRONG><a name="[bc]"></a>DDS_SetPhase</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, software_dds.o(i.DDS_SetPhase))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
</UL>

<P><STRONG><a name="[bb]"></a>DDS_Setup</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, software_dds.o(i.DDS_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_SetPhase
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_SetFrequency
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_SetAmplitude
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;circuit_learning_task
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Start
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetMode
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetAmplitude
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqPlus
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqMinus
</UL>

<P><STRONG><a name="[bd]"></a>DDS_Start</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, software_dds.o(i.DDS_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = DDS_Start &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_circuit_learning
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Start
</UL>

<P><STRONG><a name="[bf]"></a>DDS_Stop</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, software_dds.o(i.DDS_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = DDS_Stop &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Stop_IT
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_SetValue
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Stop
</UL>

<P><STRONG><a name="[c1]"></a>DDS_Test_FreqMinus</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_FreqMinus))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = DDS_Test_FreqMinus &rArr; DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[c3]"></a>DDS_Test_FreqPlus</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_FreqPlus))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = DDS_Test_FreqPlus &rArr; DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[c4]"></a>DDS_Test_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = DDS_Test_Init &rArr; DDS_Test_ShowStatus &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c5]"></a>DDS_Test_ProcessCommand</STRONG> (Thumb, 276 bytes, Stack size 16 bytes, dds_test.o(i.DDS_Test_ProcessCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = DDS_Test_ProcessCommand &rArr; DDS_Test_TestDAC &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowHelp
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_TestDAC
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Start
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowDebugInfo
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetMode
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetAmplitude
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqPlus
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqMinus
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart2_command
</UL>

<P><STRONG><a name="[cd]"></a>DDS_Test_SetAmplitude</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_SetAmplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = DDS_Test_SetAmplitude &rArr; DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[c9]"></a>DDS_Test_SetMode</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_SetMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = DDS_Test_SetMode &rArr; DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[ce]"></a>DDS_Test_ShowDebugInfo</STRONG> (Thumb, 198 bytes, Stack size 40 bytes, dds_test.o(i.DDS_Test_ShowDebugInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = DDS_Test_ShowDebugInfo &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_GetInterruptStats
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_GetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[cc]"></a>DDS_Test_ShowHelp</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_ShowHelp))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = DDS_Test_ShowHelp &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[c2]"></a>DDS_Test_ShowStatus</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, dds_test.o(i.DDS_Test_ShowStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DDS_Test_ShowStatus &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_GetConfig
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Start
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetMode
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetAmplitude
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqPlus
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqMinus
</UL>

<P><STRONG><a name="[ca]"></a>DDS_Test_Start</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = DDS_Test_Start &rArr; DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Start
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[cb]"></a>DDS_Test_Stop</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dds_test.o(i.DDS_Test_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = DDS_Test_Stop &rArr; DDS_Test_ShowStatus &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Stop
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[cf]"></a>DDS_Test_TestDAC</STRONG> (Thumb, 120 bytes, Stack size 56 bytes, dds_test.o(i.DDS_Test_TestDAC))
<BR><BR>[Stack]<UL><LI>Max Depth = 664<LI>Call Chain = DDS_Test_TestDAC &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_SetValue
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>

<P><STRONG><a name="[1b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[df]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[100]"></a>FMC_NORSRAM_Extended_Timing_Init</STRONG> (Thumb, 76 bytes, Stack size 4 bytes, stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FMC_NORSRAM_Extended_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[fe]"></a>FMC_NORSRAM_Init</STRONG> (Thumb, 140 bytes, Stack size 4 bytes, stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FMC_NORSRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[ff]"></a>FMC_NORSRAM_Timing_Init</STRONG> (Thumb, 106 bytes, Stack size 4 bytes, stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FMC_NORSRAM_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[d7]"></a>HAL_ADCEx_InjectedConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[121]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 374 bytes, Stack size 12 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[9f]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, adc_app.o(i.HAL_ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[a0]"></a>HAL_ADC_ConvHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAHalfConvCplt
</UL>

<P><STRONG><a name="[9e]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAError
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[a1]"></a>HAL_ADC_IRQHandler</STRONG> (Thumb, 322 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_LevelOutOfWindowCallback
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConvCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[d9]"></a>HAL_ADC_Init</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[d8]"></a>HAL_ADC_LevelOutOfWindowCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 146 bytes, Stack size 40 bytes, adc.o(i.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[e2]"></a>HAL_ADC_Start_DMA</STRONG> (Thumb, 362 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
</UL>

<P><STRONG><a name="[d6]"></a>HAL_ADC_Stop_DMA</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>

<P><STRONG><a name="[a8]"></a>HAL_DACEx_ConvCpltCallbackCh2</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAConvCpltCh2
</UL>

<P><STRONG><a name="[ac]"></a>HAL_DACEx_ConvHalfCpltCallbackCh2</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAHalfConvCpltCh2
</UL>

<P><STRONG><a name="[aa]"></a>HAL_DACEx_ErrorCallbackCh2</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAErrorCh2
</UL>

<P><STRONG><a name="[123]"></a>HAL_DAC_ConfigChannel</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DAC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[a7]"></a>HAL_DAC_ConvCpltCallbackCh1</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAConvCpltCh1
</UL>

<P><STRONG><a name="[ab]"></a>HAL_DAC_ConvHalfCpltCallbackCh1</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAHalfConvCpltCh1
</UL>

<P><STRONG><a name="[a9]"></a>HAL_DAC_ErrorCallbackCh1</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAErrorCh1
</UL>

<P><STRONG><a name="[e5]"></a>HAL_DAC_Init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[e6]"></a>HAL_DAC_MspInit</STRONG> (Thumb, 138 bytes, Stack size 40 bytes, dac.o(i.HAL_DAC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
</UL>

<P><STRONG><a name="[b7]"></a>HAL_DAC_SetValue</STRONG> (Thumb, 54 bytes, Stack size 4 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DAC_SetValue
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Stop
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_TestDAC
</UL>

<P><STRONG><a name="[b6]"></a>HAL_DAC_Start</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DAC_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Init
</UL>

<P><STRONG><a name="[e7]"></a>HAL_DAC_Start_DMA</STRONG> (Thumb, 240 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_DAC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
</UL>

<P><STRONG><a name="[e8]"></a>HAL_DAC_Stop_DMA</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_DAC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_dac_dma
</UL>

<P><STRONG><a name="[e4]"></a>HAL_DMA_Abort</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Stop_DMA
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[113]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[d5]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 488 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
</UL>

<P><STRONG><a name="[de]"></a>HAL_DMA_Init</STRONG> (Thumb, 230 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>

<P><STRONG><a name="[e3]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Start_DMA
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>

<P><STRONG><a name="[d4]"></a>HAL_Delay</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_TestDAC
</UL>

<P><STRONG><a name="[11a]"></a>HAL_GPIO_DeInit</STRONG> (Thumb, 358 bytes, Stack size 36 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspDeInit
</UL>

<P><STRONG><a name="[dd]"></a>HAL_GPIO_Init</STRONG> (Thumb, 564 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FMC_MspInit
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[15c]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>

<P><STRONG><a name="[127]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[e9]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_EnableOverDrive
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;circuit_learning_task
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_circuit_learning
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[12d]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[ee]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f0]"></a>HAL_InitTick</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[f1]"></a>HAL_MspInit</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[11b]"></a>HAL_NVIC_DisableIRQ</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspDeInit
</UL>

<P><STRONG><a name="[e1]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[e0]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 98 bytes, Stack size 4 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[ef]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[f3]"></a>HAL_PWREx_EnableOverDrive</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PWREx_EnableOverDrive
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fa]"></a>HAL_RCC_CSSCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_NMI_IRQHandler
</UL>

<P><STRONG><a name="[f4]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 354 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[12f]"></a>HAL_RCC_EnableCSS</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS))
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f7]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
</UL>

<P><STRONG><a name="[f6]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[f8]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[f5]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[f9]"></a>HAL_RCC_NMI_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RCC_NMI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_CSSCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>

<P><STRONG><a name="[fb]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1154 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fc]"></a>HAL_SRAM_Init</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_hal_sram.o(i.HAL_SRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_NORSRAM_Timing_Init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_NORSRAM_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_NORSRAM_Extended_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
</UL>

<P><STRONG><a name="[fd]"></a>HAL_SRAM_MspInit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fmc.o(i.HAL_SRAM_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SRAM_MspInit &rArr; HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FMC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[f2]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[129]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[101]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[102]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[144]"></a>HAL_TIM_Base_Start</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start))
<BR><BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
</UL>

<P><STRONG><a name="[be]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Start
</UL>

<P><STRONG><a name="[16e]"></a>HAL_TIM_Base_Stop</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop))
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_dac_dma
</UL>

<P><STRONG><a name="[c0]"></a>HAL_TIM_Base_Stop_IT</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Stop
</UL>

<P><STRONG><a name="[104]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_ConfigClockSource
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[16f]"></a>HAL_TIM_GenerateEvent</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent))
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
</UL>

<P><STRONG><a name="[109]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[10b]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, my_usart.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[10c]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UART_DMAStop &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[a5]"></a>HAL_UART_DeInit</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_UART_DeInit &rArr; HAL_UART_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspDeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_UART_Status
</UL>

<P><STRONG><a name="[114]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[111]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 636 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Transmit_IT
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[117]"></a>HAL_UART_Init</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[110]"></a>HAL_UART_MspDeInit</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, usart.o(i.HAL_UART_MspDeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_UART_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_DisableIRQ
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DeInit
</UL>

<P><STRONG><a name="[118]"></a>HAL_UART_MspInit</STRONG> (Thumb, 258 bytes, Stack size 48 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[11c]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT))
<BR><BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[11e]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, my_usart.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_RxCpltCallback &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[130]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[a4]"></a>HAL_UART_Transmit</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_UART_Status
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[131]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
</UL>

<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[120]"></a>MX_ADC1_Init</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, adc.o(i.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[122]"></a>MX_DAC_Init</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, dac.o(i.MX_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_DAC_Init &rArr; HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[124]"></a>MX_DMA_Init</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[125]"></a>MX_FMC_Init</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, fmc.o(i.MX_FMC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = MX_FMC_Init &rArr; HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[126]"></a>MX_GPIO_Init</STRONG> (Thumb, 382 bytes, Stack size 64 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[128]"></a>MX_TIM3_Init</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, tim.o(i.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12a]"></a>MX_TIM6_Init</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, tim.o(i.MX_TIM6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = MX_TIM6_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a6]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_UART_Status
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12b]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12c]"></a>MX_USART3_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART3_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_USART3_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NMI_Handler &rArr; HAL_RCC_NMI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_NMI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15e]"></a>PID_Init</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, app_pid.o(i.PID_Init))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[12e]"></a>SystemClock_Config</STRONG> (Thumb, 150 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_EnableCSS
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_EnableOverDrive
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[66]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[103]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 190 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[105]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[10a]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[11d]"></a>UART_Start_Receive_IT</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>

<P><STRONG><a name="[30]"></a>USART1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = USART2_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART3_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = USART3_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[132]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[183]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[16b]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[184]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[185]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[134]"></a>__0vsnprintf</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[186]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[187]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[188]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[162]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[13c]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[83]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[189]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[135]"></a>__hardfp_cosf</STRONG> (Thumb, 280 bytes, Stack size 8 bytes, cosf.o(i.__hardfp_cosf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;preprocess_signal
</UL>

<P><STRONG><a name="[13a]"></a>__hardfp_log10f</STRONG> (Thumb, 332 bytes, Stack size 8 bytes, log10f.o(i.__hardfp_log10f))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_log10f
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_divzero
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_sinad
</UL>

<P><STRONG><a name="[af]"></a>__hardfp_roundf</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, roundf.o(i.__hardfp_roundf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __hardfp_roundf &rArr; _frnd
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frnd
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;round_to_nearest_k
</UL>

<P><STRONG><a name="[b5]"></a>__hardfp_sinf</STRONG> (Thumb, 344 bytes, Stack size 16 bytes, sinf.o(i.__hardfp_sinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Init
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sine
</UL>

<P><STRONG><a name="[9d]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_thd_n
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_thd
</UL>

<P><STRONG><a name="[13b]"></a>__mathlib_flt_divzero</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_divzero))
<BR><BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log10f
</UL>

<P><STRONG><a name="[139]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log10f
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[138]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log10f
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[13d]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[136]"></a>__mathlib_rredf2</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __mathlib_rredf2
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[18a]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[18b]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[18c]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[137]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log10f
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[143]"></a>adc_tim_dma_init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, adc_app.o(i.adc_tim_dma_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = adc_tim_dma_init &rArr; HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16a]"></a>calculate_bandwidth</STRONG> (Thumb, 132 bytes, Stack size 12 bytes, wave_recognition.o(i.calculate_bandwidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = calculate_bandwidth
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[145]"></a>calculate_carrier_suppression</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, wave_recognition.o(i.calculate_carrier_suppression))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = calculate_carrier_suppression &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[146]"></a>calculate_fft_spectrum</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, my_fft.o(i.calculate_fft_spectrum))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = calculate_fft_spectrum &rArr; arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[147]"></a>calculate_sinad</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, my_fft.o(i.calculate_sinad))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = calculate_sinad &rArr; calculate_thd_n &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_log10f
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_thd_n
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
</UL>

<P><STRONG><a name="[149]"></a>calculate_thd</STRONG> (Thumb, 336 bytes, Stack size 32 bytes, my_fft.o(i.calculate_thd))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = calculate_thd &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
</UL>

<P><STRONG><a name="[148]"></a>calculate_thd_n</STRONG> (Thumb, 210 bytes, Stack size 16 bytes, my_fft.o(i.calculate_thd_n))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = calculate_thd_n &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_sinad
</UL>

<P><STRONG><a name="[78]"></a>circuit_learning_task</STRONG> (Thumb, 208 bytes, Stack size 240 bytes, scheduler.o(i.circuit_learning_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 888<LI>Call Chain = circuit_learning_task &rArr; DDS_Setup &rArr; DDS_SetFrequency &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[14b]"></a>dac_app_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dac_app.o(i.dac_app_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = dac_app_init &rArr; dac_app_set_amplitude &rArr; start_dac_dma &rArr; update_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14c]"></a>dac_app_set_amplitude</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, dac_app.o(i.dac_app_set_amplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = dac_app_set_amplitude &rArr; start_dac_dma &rArr; update_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_dac_dma
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_init
</UL>

<P><STRONG><a name="[150]"></a>dac_app_set_waveform</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, dac_app.o(i.dac_app_set_waveform))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = dac_app_set_waveform &rArr; start_dac_dma &rArr; update_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_dac_dma
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[151]"></a>detect_symmetry</STRONG> (Thumb, 168 bytes, Stack size 32 bytes, wave_recognition.o(i.detect_symmetry))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = detect_symmetry &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[152]"></a>fft_init</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, my_fft.o(i.fft_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = fft_init &rArr; generate_hanning_window
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_init_f32
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_hanning_window
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[155]"></a>find_peaks</STRONG> (Thumb, 238 bytes, Stack size 32 bytes, wave_recognition.o(i.find_peaks))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = find_peaks &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[154]"></a>generate_hanning_window</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, my_fft.o(i.generate_hanning_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = generate_hanning_window
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_init
</UL>

<P><STRONG><a name="[163]"></a>get_current_ad_frequency</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, key_app.o(i.get_current_ad_frequency))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
</UL>

<P><STRONG><a name="[164]"></a>get_precise_peak_frequency</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, my_fft.o(i.get_precise_peak_frequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_precise_peak_frequency
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
</UL>

<P><STRONG><a name="[77]"></a>key_proc</STRONG> (Thumb, 176 bytes, Stack size 8 bytes, key_app.o(i.key_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 688<LI>Call Chain = key_proc &rArr; output_fft_spectrum &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_waveform
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fft_spectrum
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[15a]"></a>key_read</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, key_app.o(i.key_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = key_read
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[65]"></a>main</STRONG> (Thumb, 166 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 672<LI>Call Chain = main &rArr; process_uart1_command &rArr; DDS_Test_TestDAC &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_waveform
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_Apply_Settings
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_UART_Status
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CTRL_INIT
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[b8]"></a>my_printf</STRONG> (Thumb, 50 bytes, Stack size 544 bytes, my_usart.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart1_command
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Setup
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Stop
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Start
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_SetFrequency
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_SetAmplitude
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowHelp
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;circuit_learning_task
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_circuit_learning
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_TestDAC
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Start
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowDebugInfo
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetMode
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetAmplitude
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqPlus
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqMinus
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart2_command
</UL>

<P><STRONG><a name="[15b]"></a>output_fft_spectrum</STRONG> (Thumb, 436 bytes, Stack size 72 bytes, my_fft.o(i.output_fft_spectrum))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = output_fft_spectrum &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_ad_frequency
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;round_to_nearest_k
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_precise_peak_frequency
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_thd_n
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_thd
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_sinad
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[161]"></a>process_uart1_command</STRONG> (Thumb, 1566 bytes, Stack size 8 bytes, my_usart.o(i.process_uart1_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 672<LI>Call Chain = process_uart1_command &rArr; DDS_Test_TestDAC &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_TestDAC
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_Start
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowStatus
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ShowDebugInfo
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetMode
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_SetAmplitude
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqPlus
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_FreqMinus
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[79]"></a>process_uart2_command</STRONG> (Thumb, 244 bytes, Stack size 16 bytes, my_usart.o(i.process_uart2_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 696<LI>Call Chain = process_uart2_command &rArr; DDS_Test_ProcessCommand &rArr; DDS_Test_TestDAC &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_circuit_learning
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Test_ProcessCommand
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[14a]"></a>recognize_waveform</STRONG> (Thumb, 532 bytes, Stack size 264 bytes, wave_recognition.o(i.recognize_waveform))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = recognize_waveform &rArr; find_peaks &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_peaks
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_symmetry
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_carrier_suppression
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_bandwidth
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;preprocess_signal
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;circuit_learning_task
</UL>

<P><STRONG><a name="[165]"></a>round_to_nearest_k</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, my_fft.o(i.round_to_nearest_k))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = round_to_nearest_k &rArr; __hardfp_roundf &rArr; _frnd
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_roundf
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;output_fft_spectrum
</UL>

<P><STRONG><a name="[15f]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(i.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[160]"></a>scheduler_run</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, scheduler.o(i.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = scheduler_run
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[168]"></a>start_circuit_learning</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, scheduler.o(i.start_circuit_learning))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = start_circuit_learning &rArr; DDS_Start &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDS_Start
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart2_command
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[ed]"></a>HAL_FMC_MspInit</STRONG> (Thumb, 138 bytes, Stack size 48 bytes, fmc.o(i.HAL_FMC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
</UL>

<P><STRONG><a name="[157]"></a>generate_sine</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, dac_app.o(i.generate_sine))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = generate_sine &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>

<P><STRONG><a name="[158]"></a>generate_square</STRONG> (Thumb, 116 bytes, Stack size 12 bytes, dac_app.o(i.generate_square))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = generate_square
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>

<P><STRONG><a name="[159]"></a>generate_triangle</STRONG> (Thumb, 292 bytes, Stack size 12 bytes, dac_app.o(i.generate_triangle))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = generate_triangle
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>

<P><STRONG><a name="[14d]"></a>generate_waveform</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, dac_app.o(i.generate_waveform))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = generate_waveform &rArr; generate_sine &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_triangle
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_square
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sine
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_waveform
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
</UL>

<P><STRONG><a name="[14e]"></a>start_dac_dma</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, dac_app.o(i.start_dac_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = start_dac_dma &rArr; update_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Start_DMA
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_waveform
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
</UL>

<P><STRONG><a name="[14f]"></a>stop_dac_dma</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, dac_app.o(i.stop_dac_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = stop_dac_dma &rArr; HAL_DAC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Stop
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Stop_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_waveform
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
</UL>

<P><STRONG><a name="[16d]"></a>update_timer_frequency</STRONG> (Thumb, 334 bytes, Stack size 40 bytes, dac_app.o(i.update_timer_frequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = update_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_GenerateEvent
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Stop
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
</UL>

<P><STRONG><a name="[166]"></a>preprocess_signal</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, wave_recognition.o(i.preprocess_signal))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = preprocess_signal &rArr; __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recognize_waveform
</UL>

<P><STRONG><a name="[68]"></a>ADC_DMAConvCplt</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ADC_DMAConvCplt &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[6a]"></a>ADC_DMAError</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[69]"></a>ADC_DMAHalfConvCplt</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_DMAHalfConvCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[db]"></a>ADC_Init</STRONG> (Thumb, 298 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.ADC_Init))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[eb]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[ea]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[ec]"></a>DMA_SetConfig</STRONG> (Thumb, 56 bytes, Stack size 4 bytes, stm32f4xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[107]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[106]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[108]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[71]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[74]"></a>UART_DMAError</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[72]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 144 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UART_RxCpltCallback &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[73]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[10f]"></a>UART_EndRxTransfer</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[116]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_EndTransmit_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[10e]"></a>UART_EndTxTransfer</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[112]"></a>UART_Receive_IT</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[119]"></a>UART_SetConfig</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[115]"></a>UART_Transmit_IT</STRONG> (Thumb, 94 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_Transmit_IT))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[13e]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[133]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>

<P><STRONG><a name="[141]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[140]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[76]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsnprintf)
</UL>
<P><STRONG><a name="[75]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>

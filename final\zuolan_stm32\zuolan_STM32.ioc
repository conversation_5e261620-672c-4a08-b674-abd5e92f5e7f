#MicroXplorer Configuration settings - do not modify
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_0
ADC1.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC1.ContinuousConvMode=DISABLE
ADC1.DMAContinuousRequests=DISABLE
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-1\#ChannelRegularConversion,master,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversionFlag,DMAContinuousRequests,ContinuousConvMode,ClockPrescaler,ExternalTrigConv
ADC1.NbrOfConversionFlag=1
ADC1.Rank-1\#ChannelRegularConversion=1
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_OutputBuffer=DAC_OUTPUTBUFFER_ENABLE
DAC.DAC_OutputBuffer2=DAC_OUTPUTBUFFER_ENABLE
DAC.DAC_Trigger=DAC_TRIGGER_T6_TRGO
DAC.IPParameters=DAC_OutputBuffer2,DAC_OutputBuffer,DAC_Trigger
Dma.ADC1.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.1.Instance=DMA2_Stream0
Dma.ADC1.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.1.MemInc=DMA_MINC_ENABLE
Dma.ADC1.1.Mode=DMA_NORMAL
Dma.ADC1.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.1.Priority=DMA_PRIORITY_LOW
Dma.ADC1.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.DAC1.0.Direction=DMA_MEMORY_TO_PERIPH
Dma.DAC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.DAC1.0.Instance=DMA1_Stream5
Dma.DAC1.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.DAC1.0.MemInc=DMA_MINC_ENABLE
Dma.DAC1.0.Mode=DMA_CIRCULAR
Dma.DAC1.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.DAC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.DAC1.0.Priority=DMA_PRIORITY_LOW
Dma.DAC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=DAC1
Dma.Request1=ADC1
Dma.Request2=USART1_RX
Dma.RequestsNb=3
Dma.USART1_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.2.Instance=DMA2_Stream2
Dma.USART1_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.2.Mode=DMA_NORMAL
Dma.USART1_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.2.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FMC.AddressHoldTime2=1
FMC.AddressSetupTime2=3
FMC.BusTurnAroundDuration2=0
FMC.DataSetupTime2=6
FMC.IPParameters=AddressSetupTime2,DataSetupTime2,BusTurnAroundDuration2,AddressHoldTime2,WriteOperation2
FMC.WriteOperation2=FMC_WRITE_OPERATION_ENABLE
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F429IGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP10=USART2
Mcu.IP11=USART3
Mcu.IP2=DMA
Mcu.IP3=FMC
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=SYS
Mcu.IP7=TIM3
Mcu.IP8=TIM6
Mcu.IP9=USART1
Mcu.IPNb=12
Mcu.Name=STM32F429I(E-G)Tx
Mcu.Package=LQFP176
Mcu.Pin0=PF6
Mcu.Pin1=PF8
Mcu.Pin10=PA4
Mcu.Pin11=PA5
Mcu.Pin12=PC4
Mcu.Pin13=PE7
Mcu.Pin14=PE8
Mcu.Pin15=PE9
Mcu.Pin16=PE10
Mcu.Pin17=PE11
Mcu.Pin18=PE12
Mcu.Pin19=PE13
Mcu.Pin2=PF9
Mcu.Pin20=PE14
Mcu.Pin21=PE15
Mcu.Pin22=PB10
Mcu.Pin23=PB11
Mcu.Pin24=PH9
Mcu.Pin25=PH10
Mcu.Pin26=PH11
Mcu.Pin27=PH12
Mcu.Pin28=PD8
Mcu.Pin29=PD9
Mcu.Pin3=PH0/OSC_IN
Mcu.Pin30=PD10
Mcu.Pin31=PD14
Mcu.Pin32=PD15
Mcu.Pin33=PG6
Mcu.Pin34=PC6
Mcu.Pin35=PC7
Mcu.Pin36=PA9
Mcu.Pin37=PA10
Mcu.Pin38=PA13
Mcu.Pin39=PH13
Mcu.Pin4=PH1/OSC_OUT
Mcu.Pin40=PH14
Mcu.Pin41=PH15
Mcu.Pin42=PI0
Mcu.Pin43=PA14
Mcu.Pin44=PD0
Mcu.Pin45=PD1
Mcu.Pin46=PD4
Mcu.Pin47=PD5
Mcu.Pin48=PD6
Mcu.Pin49=PG9
Mcu.Pin5=PC1
Mcu.Pin50=PG11
Mcu.Pin51=PB4
Mcu.Pin52=PB6
Mcu.Pin53=PB7
Mcu.Pin54=PB9
Mcu.Pin55=PI5
Mcu.Pin56=VP_SYS_VS_Systick
Mcu.Pin57=VP_TIM3_VS_ClockSourceINT
Mcu.Pin58=VP_TIM6_VS_ClockSourceINT
Mcu.Pin6=PC2
Mcu.Pin7=PA0/WKUP
Mcu.Pin8=PA2
Mcu.Pin9=PA3
Mcu.PinsNb=59
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F429IGTx
MxCube.Version=6.11.1
MxDb.Version=DB.6.0.111
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0/WKUP.Signal=ADCx_IN0
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.Signal=COMP_DAC1_group
PA5.Signal=COMP_DAC2_group
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB4.GPIOParameters=GPIO_PuPd
PB4.GPIO_PuPd=GPIO_PULLUP
PB4.Locked=true
PB4.Signal=GPIO_Input
PB6.GPIOParameters=GPIO_PuPd
PB6.GPIO_PuPd=GPIO_PULLUP
PB6.Locked=true
PB6.Signal=GPIO_Input
PB7.Locked=true
PB7.Signal=FMC_NL
PB9.GPIOParameters=GPIO_PuPd
PB9.GPIO_PuPd=GPIO_PULLUP
PB9.Locked=true
PB9.Signal=GPIO_Input
PC1.GPIOParameters=GPIO_Label
PC1.GPIO_Label=AD9833_SDATA2
PC1.Locked=true
PC1.Signal=GPIO_Output
PC2.GPIOParameters=GPIO_Label
PC2.GPIO_Label=AD9833_SCLK2
PC2.Locked=true
PC2.Signal=GPIO_Output
PC4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC4.GPIO_Label=AD9959_P0
PC4.GPIO_PuPd=GPIO_PULLUP
PC4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC4.Locked=true
PC4.Signal=GPIO_Output
PC6.GPIOParameters=GPIO_Label
PC6.GPIO_Label=AD9833_FSYNC2
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC7.GPIO_Label=AD9959_SDIO1
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC7.Locked=true
PC7.Signal=GPIO_Output
PD0.Signal=FMC_D2_DA2
PD1.Signal=FMC_D3_DA3
PD10.Signal=FMC_D15_DA15
PD14.Signal=FMC_D0_DA0
PD15.Signal=FMC_D1_DA1
PD4.Signal=FMC_NOE
PD5.Signal=FMC_NWE
PD6.GPIOParameters=GPIO_PuPd
PD6.GPIO_PuPd=GPIO_PULLUP
PD6.Locked=true
PD6.Signal=GPIO_Input
PD8.Signal=FMC_D13_DA13
PD9.Signal=FMC_D14_DA14
PE10.Signal=FMC_D7_DA7
PE11.Signal=FMC_D8_DA8
PE12.Signal=FMC_D9_DA9
PE13.Signal=FMC_D10_DA10
PE14.Signal=FMC_D11_DA11
PE15.Signal=FMC_D12_DA12
PE7.Signal=FMC_D4_DA4
PE8.Signal=FMC_D5_DA5
PE9.Signal=FMC_D6_DA6
PF6.GPIOParameters=GPIO_Label
PF6.GPIO_Label=AD9833_SDATA1
PF6.Locked=true
PF6.Signal=GPIO_Output
PF8.GPIOParameters=GPIO_Label
PF8.GPIO_Label=AD9833_SCLK1
PF8.Locked=true
PF8.Signal=GPIO_Output
PF9.GPIOParameters=GPIO_Label
PF9.GPIO_Label=AD9833_FSYNC1
PF9.Locked=true
PF9.Signal=GPIO_Output
PG11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PG11.GPIO_Label=AD9959_SDIO2
PG11.GPIO_PuPd=GPIO_PULLUP
PG11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG11.Locked=true
PG11.Signal=GPIO_Output
PG6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PG6.GPIO_Label=AD9959_UP
PG6.GPIO_PuPd=GPIO_PULLUP
PG6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG6.Locked=true
PG6.Signal=GPIO_Output
PG9.Locked=true
PG9.Mode=NorPsramChipSelect2_2
PG9.Signal=FMC_NE2
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PH10.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PH10.GPIO_Label=AD9959_P1
PH10.GPIO_PuPd=GPIO_PULLUP
PH10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH10.Locked=true
PH10.Signal=GPIO_Output
PH11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PH11.GPIO_Label=AD9959_RST
PH11.GPIO_PuPd=GPIO_PULLUP
PH11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH11.Locked=true
PH11.Signal=GPIO_Output
PH12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PH12.GPIO_Label=AD9959_P2
PH12.GPIO_PuPd=GPIO_PULLUP
PH12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH12.Locked=true
PH12.Signal=GPIO_Output
PH13.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PH13.GPIO_Label=AD9959_P3
PH13.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PH13.GPIO_PuPd=GPIO_PULLUP
PH13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH13.Locked=true
PH13.PinState=GPIO_PIN_RESET
PH13.Signal=GPIO_Output
PH14.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PH14.GPIO_Label=AD9959_CS
PH14.GPIO_PuPd=GPIO_PULLUP
PH14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH14.Locked=true
PH14.Signal=GPIO_Output
PH15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PH15.GPIO_Label=AD9959_SDIO0
PH15.GPIO_PuPd=GPIO_PULLUP
PH15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH15.Locked=true
PH15.Signal=GPIO_Output
PH9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PH9.GPIO_Label=AD9959_PDC
PH9.GPIO_PuPd=GPIO_PULLUP
PH9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH9.Locked=true
PH9.Signal=GPIO_Output
PI0.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PI0.GPIO_Label=AD9959_SCK
PI0.GPIO_PuPd=GPIO_PULLUP
PI0.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI0.Locked=true
PI0.Signal=GPIO_Output
PI5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PI5.GPIO_Label=AD9959_SDIO3
PI5.GPIO_PuPd=GPIO_PULLUP
PI5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI5.Locked=true
PI5.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429IGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=zuolan_STM32.ioc
ProjectManager.ProjectName=zuolan_STM32
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-false,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_FMC_Init-FMC-false-HAL-true,5-MX_USART1_UART_Init-USART1-false-HAL-true,6-MX_USART2_UART_Init-USART2-false-HAL-true,7-MX_USART3_UART_Init-USART3-false-HAL-true,8-MX_DAC_Init-DAC-false-HAL-true,9-MX_TIM6_Init-TIM6-false-HAL-true,10-MX_ADC1_Init-ADC1-false-HAL-true,11-MX_TIM3_Init-TIM3-false-HAL-true
RCC.48MHZClocksFreq_Value=90000000
RCC.AHBFreq_Value=180000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=45000000
RCC.APB1TimFreq_Value=90000000
RCC.APB2CLKDivider=RCC_HCLK_DIV4
RCC.APB2Freq_Value=45000000
RCC.APB2TimFreq_Value=90000000
RCC.CortexFreq_Value=180000000
RCC.EnbaleCSS=true
RCC.EthernetFreq_Value=180000000
RCC.FCLKCortexFreq_Value=180000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=180000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EnbaleCSS,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=12250000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=180000000
RCC.PLLCLKFreq_Value=180000000
RCC.PLLM=25
RCC.PLLN=360
RCC.PLLQCLKFreq_Value=90000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SAI_AClocksFreq_Value=12250000
RCC.SAI_BClocksFreq_Value=12250000
RCC.SYSCLKFreq_VALUE=180000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=360000000
RCC.VCOSAIOutputFreq_Value=49000000
RCC.VCOSAIOutputFreq_ValueQ=12250000
RCC.VCOSAIOutputFreq_ValueR=24500000
RCC.VcooutputI2S=96000000
RCC.VcooutputI2SQ=96000000
SH.ADCx_IN0.0=ADC1_IN0,IN0
SH.ADCx_IN0.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
SH.COMP_DAC2_group.0=DAC_OUT2,DAC_OUT2
SH.COMP_DAC2_group.ConfNb=1
SH.FMC_D0_DA0.0=FMC_DA0,16b-da2
SH.FMC_D0_DA0.ConfNb=1
SH.FMC_D10_DA10.0=FMC_DA10,16b-da2
SH.FMC_D10_DA10.ConfNb=1
SH.FMC_D11_DA11.0=FMC_DA11,16b-da2
SH.FMC_D11_DA11.ConfNb=1
SH.FMC_D12_DA12.0=FMC_DA12,16b-da2
SH.FMC_D12_DA12.ConfNb=1
SH.FMC_D13_DA13.0=FMC_DA13,16b-da2
SH.FMC_D13_DA13.ConfNb=1
SH.FMC_D14_DA14.0=FMC_DA14,16b-da2
SH.FMC_D14_DA14.ConfNb=1
SH.FMC_D15_DA15.0=FMC_DA15,16b-da2
SH.FMC_D15_DA15.ConfNb=1
SH.FMC_D1_DA1.0=FMC_DA1,16b-da2
SH.FMC_D1_DA1.ConfNb=1
SH.FMC_D2_DA2.0=FMC_DA2,16b-da2
SH.FMC_D2_DA2.ConfNb=1
SH.FMC_D3_DA3.0=FMC_DA3,16b-da2
SH.FMC_D3_DA3.ConfNb=1
SH.FMC_D4_DA4.0=FMC_DA4,16b-da2
SH.FMC_D4_DA4.ConfNb=1
SH.FMC_D5_DA5.0=FMC_DA5,16b-da2
SH.FMC_D5_DA5.ConfNb=1
SH.FMC_D6_DA6.0=FMC_DA6,16b-da2
SH.FMC_D6_DA6.ConfNb=1
SH.FMC_D7_DA7.0=FMC_DA7,16b-da2
SH.FMC_D7_DA7.ConfNb=1
SH.FMC_D8_DA8.0=FMC_DA8,16b-da2
SH.FMC_D8_DA8.ConfNb=1
SH.FMC_D9_DA9.0=FMC_DA9,16b-da2
SH.FMC_D9_DA9.ConfNb=1
SH.FMC_NL.0=FMC_NL
SH.FMC_NL.ConfNb=1
SH.FMC_NOE.0=FMC_NOE,MuxedPsram2
SH.FMC_NOE.ConfNb=1
SH.FMC_NWE.0=FMC_NWE,MuxedPsram2
SH.FMC_NWE.ConfNb=1
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM3.Period=100-1
TIM3.Prescaler=180-1
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM6.Period=100-1
TIM6.Prescaler=18-1
TIM6.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom

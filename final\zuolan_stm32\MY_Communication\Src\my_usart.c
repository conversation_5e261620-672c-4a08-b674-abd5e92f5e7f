#include "my_usart.h"
#include "scheduler.h"
#include "dds_test.h"
#include "stdarg.h"
#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "my_usart_pack.h"
#include "my_hmi.h"
#include "bsp_system.h"

// 确保UART句柄的extern声明
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern UART_HandleTypeDef huart3;
// 串口1的缓冲区和状态变量
uint8_t rxBuffer1[RX_BUFFER_SIZE];     ///< 串口1接收缓冲区
uint8_t rxTemp1;                       ///< 串口1中断接收临时变量
uint16_t rxIndex1 = 0;                 ///< 串口1当前接收缓冲区索引
volatile uint8_t commandReceived1 = 0; ///< 串口1接收到命令标志

// 串口3的缓冲区和状态变量
uint8_t rxBuffer3[RX_BUFFER_SIZE];     ///< 串口3接收缓冲区
uint8_t rxTemp3;                       ///< 串口3中断接收临时变量
uint16_t rxIndex3 = 0;                 ///< 串口3当前接收缓冲区索引
volatile uint8_t commandReceived2 = 0; ///< 串口2接收到命令标志
volatile uint8_t commandReceived3 = 0; ///< 串口3接收到命令标志

// 串口2数据包缓冲区和变量
uint8_t rxBuffer2[RX_BUFFER_SIZE]; ///< 串口2接收缓冲区
uint8_t rxTemp2;                   ///< 串口2中断接收临时变量
uint16_t rxIndex2 = 0;             ///< 串口2当前接收缓冲区索引
volatile uint8_t frameStarted = 0; ///< 帧开始标志

// UART1 DMA接收相关变量（借鉴成功方案）
uint8_t uart1_dma_buffer[RX_BUFFER_SIZE]; ///< UART1 DMA接收缓冲区
uint8_t uart1_cmd_buffer[RX_BUFFER_SIZE]; ///< UART1 命令处理缓冲区
volatile uint8_t uart1_cmd_flag = 0;      ///< UART1 命令就绪标志

// 外部DMA句柄声明
extern DMA_HandleTypeDef hdma_usart1_rx ;

/**
 * @brief UART DMA接收事件回调函数（借鉴成功方案）
 * @param huart UART句柄
 * @param Size 指示事件发生时当前DMA已经成功传输了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 1. 确认目标串口 (USART1)
    if (huart->Instance == USART1)
    {
        // 2. 立即停止当前的 DMA 传输 (防止正在接收时)
        //    因为这个中断意味着发送方已经停止，或者 DMA 缓冲区等待满了
        HAL_UART_DMAStop(huart);

        // 3. 将 DMA 缓冲区中有效数据 (Size 个字节) 拷贝到命令处理缓冲区
        memcpy(uart1_cmd_buffer, uart1_dma_buffer, Size);
        uart1_cmd_buffer[Size] = '\0'; // 添加字符串结束符

        // 4. 设置"数据通知位"，主程序循环中数据处理
        uart1_cmd_flag = 1;

        // 5. 清空 DMA 接收缓冲区，为下次接收做准备
        memset(uart1_dma_buffer, 0, sizeof(uart1_dma_buffer));

        // 6. **关键步骤：重新启动下一轮 DMA 空闲接收**
        //    如果不再次调用，串口只能接收一次
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart1_dma_buffer, sizeof(uart1_dma_buffer));

        // 7. 如果之前关闭了半满中断，这里需要再次关闭 (可选需要)
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}

// UART2_DMA_Process函数已删除，不再使用DMA方式

/**
 * @brief 格式化打印并通过指定串口发送
 * @param huart 串口句柄
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return 返回发送的字节数
 */
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512]; // 临时缓冲区，用于存储格式化后的字符串
    va_list arg;      // 可变参数列表
    int len;

    va_start(arg, format);                                // 初始化可变参数列表
    len = vsnprintf(buffer, sizeof(buffer), format, arg); // 格式化字符串
    va_end(arg);                                          // 清理可变参数列表

    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF); // 发送格式化后的字符串
    return len;
}

u8 Adjust=50;

/**
 * @brief 检查UART句柄状态
 * @param None
 * @retval None
 */
void Check_UART_Status(void)
{
    // 简单的状态检查，直接发送字符串
    const char* test_msg = "UART Test OK\r\n";

    // 尝试发送测试消息
    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart1, (uint8_t*)test_msg, strlen(test_msg), 1000);

    // 如果发送失败，尝试重新初始化
    if (status != HAL_OK) {
        // 重新初始化UART1
        HAL_UART_DeInit(&huart1);
        MX_USART1_UART_Init();

        // 再次尝试发送
        HAL_UART_Transmit(&huart1, (uint8_t*)"UART Reinitialized\r\n", 20, 1000);
    }

    // 检查DMA状态
    char dma_msg[50];
    sprintf(dma_msg, "DMA State: %d\r\n", hdma_usart1_rx.State);
    HAL_UART_Transmit(&huart1, (uint8_t*)dma_msg, strlen(dma_msg), 1000);

    if (hdma_usart1_rx.State == HAL_DMA_STATE_READY) {
        HAL_UART_Transmit(&huart1, (uint8_t*)"DMA Ready\r\n", 11, 1000);
    } else if (hdma_usart1_rx.State == HAL_DMA_STATE_BUSY) {
        HAL_UART_Transmit(&huart1, (uint8_t*)"DMA Busy (Good)\r\n", 17, 1000);
    } else {
        HAL_UART_Transmit(&huart1, (uint8_t*)"DMA Error\r\n", 11, 1000);
    }

    // 如果DMA状态不正常，启动备用的中断接收
    if (hdma_usart1_rx.State != HAL_DMA_STATE_BUSY) {
        HAL_UART_Transmit(&huart1, (uint8_t*)"Starting backup interrupt RX\r\n", 31, 1000);
        HAL_UART_Receive_IT(&huart1, &rxTemp1, 1);
    }

    // 提示用户如何发送命令
    HAL_UART_Transmit(&huart1, (uint8_t*)"Ready! Type: help\r\n", 19, 1000);
}
/**
 * @brief 串口接收中断回调函数
 * @param huart 串口句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	
    // USART1现在使用DMA+IDLE接收，不需要中断处理

    if (huart->Instance == USART3) // USART3的接收中断
    {
        if (rxIndex3 < RX_BUFFER_SIZE - 1)
        {
            // 如果接收到换行符并且前一个字符是回车符，标记命令接收完成
            if (rxTemp3 == '\n' && rxBuffer3[rxIndex3 - 1] == '\r')
            {
                commandReceived3 = 1;           // 设置命令接收标志
                rxBuffer3[rxIndex3 - 1] = '\0'; // 添加字符串结束符
                rxIndex3 = 0;                   // 重置接收索引
            }
            else
            {
                rxBuffer3[rxIndex3++] = rxTemp3; // 保存接收到的数据
            }
        }
        else
        {
            rxIndex3 = 0; // 缓冲区溢出，重置索引
        }
        HAL_UART_Receive_IT(&huart3, &rxTemp3, 1); // 再次启动接收中断
    }

    if (huart->Instance == USART2) // USART2的接收中断 - 简化版本
    {
        // 简单回显
        HAL_UART_Transmit(&huart2, &rxTemp2, 1, 10);

        // 检查命令结束符
        if (rxTemp2 == '\r' || rxTemp2 == '\n')
        {
            if (rxIndex2 > 0) // 确保有数据
            {
                commandReceived2 = 1;           // 设置命令接收标志
                rxBuffer2[rxIndex2] = '\0';     // 添加字符串结束符
                HAL_UART_Transmit(&huart2, (uint8_t*)"\r\nOK\r\n", 6, 10);
            }
        }
        else if (rxIndex2 < RX_BUFFER_SIZE - 1)
        {
            rxBuffer2[rxIndex2++] = rxTemp2; // 保存接收到的数据
        }
        else
        {
            rxIndex2 = 0; // 缓冲区溢出，重置索引
        }

        // 重新启动接收中断
        HAL_UART_Receive_IT(&huart2, &rxTemp2, 1);
    }
}

/**
 * @brief 处理UART1接收到的命令（使用DMA+IDLE方案）
 * @param None
 * @retval None
 */
void process_uart1_command(void)
{
    // 添加静态计数器监控函数调用（但不频繁输出）
    static uint32_t call_count = 0;
    static uint32_t last_flag_count = 0;
    call_count++;

    // 暂时关闭频繁输出，避免刷屏
    // if (call_count % 100000 == 0) {
    //     my_printf(&huart1, "DMA check: calls=%lu, flags=%lu\r\n", call_count, last_flag_count);
    // }

    // 检查DMA接收标志
    if (uart1_cmd_flag) {
        last_flag_count++;
        uart1_cmd_flag = 0; // 清除命令接收标志

        // 调试：显示接收到的命令
        my_printf(&huart1, "DMA CMD: [%s]\r\n", uart1_cmd_buffer);

        // 处理DMA接收的命令
        process_command((char*)uart1_cmd_buffer);
        return;
    }

    // 检查中断接收标志（备用方案）
    if (commandReceived1) {
        last_flag_count++;
        commandReceived1 = 0; // 清除命令接收标志

        // 调试：显示接收到的命令
        my_printf(&huart1, "INT CMD: [%s]\r\n", rxBuffer1);

        // 处理中断接收的命令
        process_command((char*)rxBuffer1);

        // 清空缓冲区
        memset(rxBuffer1, 0, sizeof(rxBuffer1));
        rxIndex1 = 0;
        return;
    }
}

/**
 * @brief 处理接收到的命令（通用函数）
 * @param command 命令字符串
 * @retval None
 */
void process_command(char* command)
{
    if (command == NULL || strlen(command) == 0) {
        return;
    }

    // 处理命令
    if (strncmp(command, "dds help", 8) == 0) {
            my_printf(&huart1, "=== DDS Test Commands ===\r\n");
            my_printf(&huart1, "dds help     - Show this help\r\n");
            my_printf(&huart1, "plus         - Increase frequency\r\n");
            my_printf(&huart1, "minus        - Decrease frequency\r\n");
            my_printf(&huart1, "start        - Start DDS output\r\n");
            my_printf(&huart1, "stop         - Stop DDS output\r\n");
            my_printf(&huart1, "status       - Show current status\r\n");
            my_printf(&huart1, "debug        - Show detailed debug info\r\n");
            my_printf(&huart1, "test dac     - Test DAC direct output\r\n");
            my_printf(&huart1, "monitor      - Show real-time DDS status\r\n");
            my_printf(&huart1, "amp XX       - Set amplitude to XX%% (0-100)\r\n");
            my_printf(&huart1, "low freq     - Set low frequency mode\r\n");
            my_printf(&huart1, "high freq    - Set high frequency mode\r\n");
        }
    else if (strncmp(command, "help", 4) == 0) {
            my_printf(&huart1, "=== System Commands ===\r\n");
            my_printf(&huart1, "help         - Show this help\r\n");
            my_printf(&huart1, "learn        - Start circuit learning\r\n");
            my_printf(&huart1, "dds help     - Show DDS commands\r\n");
        }
    else if (strncmp(command, "learn", 5) == 0) {
            my_printf(&huart1, "Starting circuit learning...\r\n");
            // 这里可以调用学习函数
        }
    else if (strncmp(command, "plus", 4) == 0) {
            my_printf(&huart1, "Frequency increased\r\n");
            // 这里可以调用频率增加函数
						DDS_Test_FreqPlus();
        }
        else if (strncmp((char*)uart1_cmd_buffer, "minus", 5) == 0) {
            my_printf(&huart1, "Frequency decreased\r\n");
            // 这里可以调用频率减少函数
						DDS_Test_FreqMinus();
        }
        else if (strncmp((char*)uart1_cmd_buffer, "start", 5) == 0) {
            my_printf(&huart1, "DDS output started\r\n");
            // 这里可以调用DDS启动函数
						DDS_Test_Start();
        }
        else if (strncmp((char*)uart1_cmd_buffer, "stop", 4) == 0) {
            my_printf(&huart1, "DDS output stopped\r\n");
            // 这里可以调用DDS停止函数
						DDS_Test_Stop();
        }
        else if (strncmp((char*)uart1_cmd_buffer, "status", 6) == 0) {
            my_printf(&huart1, "System Status: Running\r\n");
            // 这里可以显示系统状态
						DDS_Test_ShowStatus();
        }
        else if (strncmp((char*)uart1_cmd_buffer, "low freq", 8) == 0) {
            my_printf(&huart1, "Low frequency mode activated\r\n");
            // 这里可以设置低频模式
						DDS_Test_SetMode(FREQ_MODE_LOW);
        }
        else if (strncmp((char*)uart1_cmd_buffer, "high freq", 9) == 0) {
            my_printf(&huart1, "High frequency mode activated\r\n");
            // 这里可以设置高频模式
						DDS_Test_SetMode(FREQ_MODE_HIGH);
        }
        else if (strncmp((char*)uart1_cmd_buffer, "debug", 5) == 0) {
            my_printf(&huart1, "Showing debug information...\r\n");
            // 调用DDS调试信息显示函数
							DDS_Test_ShowDebugInfo();
        }
        else if (strncmp(command, "test dac", 8) == 0) {
            my_printf(&huart1, "Testing DAC direct output...\r\n");
            // 调用DAC测试函数
							DDS_Test_TestDAC();
        }
        else if (strncmp(command, "amp ", 4) == 0) {
            // 设置幅度: "amp 50" 设置为50%
            int amplitude = atoi(command + 4);
            if (amplitude >= 0 && amplitude <= 100) {
                my_printf(&huart1, "Setting amplitude to %d%%\r\n", amplitude);
                DDS_Test_SetAmplitude(amplitude);
            } else {
                my_printf(&huart1, "Error: Amplitude must be 0-100%%\r\n");
            }
        }
    else {
        my_printf(&huart1, "Unknown command: %s\r\n", command);
        my_printf(&huart1, "Type 'help' or 'dds help' for available commands\r\n");
    }
}

/**
 * @brief 处理UART2接收到的命令
 * @param None
 * @retval None
 */
void process_uart2_command(void)
{
    if (commandReceived2) {
        commandReceived2 = 0; // 清除标志
        // 调试：显示接收到的命令
        my_printf(&huart2, "Received: [%s]\r\n", rxBuffer2);

        // 重置接收索引
        rxIndex2 = 0;

        // 检查是否是学习命令
        if (strstr((char*)rxBuffer2, "learn") != NULL ||
            strstr((char*)rxBuffer2, "LEARN") != NULL) {
            start_circuit_learning();
        }
        // 检查是否是DDS测试命令
        else if (strstr((char*)rxBuffer2, "low freq") != NULL ||
                 strstr((char*)rxBuffer2, "high freq") != NULL ||
                 strstr((char*)rxBuffer2, "plus") != NULL ||
                 strstr((char*)rxBuffer2, "minus") != NULL ||
                 strstr((char*)rxBuffer2, "start") != NULL ||
                 strstr((char*)rxBuffer2, "stop") != NULL ||
                 strstr((char*)rxBuffer2, "status") != NULL ||
                 strstr((char*)rxBuffer2, "debug") != NULL ||
                 strstr((char*)rxBuffer2, "dds help") != NULL ||
                 strstr((char*)rxBuffer2, "amp ") != NULL) {
            DDS_Test_ProcessCommand((char*)rxBuffer2);
        }
        else if (strstr((char*)rxBuffer2, "help") != NULL ||
                 strstr((char*)rxBuffer2, "HELP") != NULL) {
            my_printf(&huart2, "Available commands (send via UART2):\r\n");
            my_printf(&huart2, "  learn     - Start circuit learning\r\n");
            my_printf(&huart2, "  dds help  - Show DDS test commands\r\n");
            my_printf(&huart2, "  help      - Show this help\r\n");
        }
        else {
            my_printf(&huart2, "Unknown command: %s\r\n", rxBuffer2);
            my_printf(&huart2, "Type 'help' or 'dds help' for available commands\r\n");
        }

        // 清空接收缓冲区
        memset(rxBuffer2, 0, RX_BUFFER_SIZE);
        rxIndex2 = 0; // 重置接收索引
    }
}
